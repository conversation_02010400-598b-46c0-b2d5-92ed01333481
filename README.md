crm-wechat
===========================

###########环境依赖
JDK1.8

###########部署步骤
start.sh

###########目录结构描述
├── crm-wechat            // 项目名称</br>
├── crm-wechat-client     // 接口公共参数</br>
├── crm-wechat-dao      // 数据dao层</br>
├── crm-wechat-remote   // 启动</br>
├── crm-wechat-service  // 业务代码</br>
├── pom.xml                 // 父pom</br>
└── README.md               // help</br>

###########事务处理</br>
1.springCloud 项目采取注解 @Transactional 来开启事务。</br>
2.在类上加上@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
保证有事务 就在事务中运行,没有就无事务运行。</br>
3.增删改 在方法上加上@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
默认的事务传播机制,没有事务就开启一个,有就在当前事务中运行。</br>
4.调用外部接口全部在outerservice包中。</br>


