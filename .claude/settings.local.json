{"permissions": {"allow": ["Bash(rg:*)", "<PERSON><PERSON>(claude mcp)", "<PERSON><PERSON>(claude mcp:*)", "<PERSON><PERSON>(sed:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "WebFetch(domain:developer.work.weixin.qq.com)", "WebSearch", "<PERSON><PERSON>(mv:*)", "mcp__mysql-mcp__execute_sql"], "defaultMode": "acceptEdits"}, "mcpServers": {"mysql-mcp": {"command": "mysql_mcp_server", "args": [], "env": {"MYSQL_DATABASE": "docker_it34_crmwechat", "MYSQL_HOST": "**************", "MYSQL_PASSWORD": "crmdocusr", "MYSQL_PORT": "3306", "MYSQL_USER": "crmdocusr"}}}}