# CRM微信项目接口定义与实现规范

## 项目信息
- 项目名称：crm-wechat
- 技术栈：Spring Boot + Spring Cloud + Dubbo + MyBatis
- JDK版本：1.8
- 作者：hongdong.xie
- 生成时间：2025-06-18 16:06:07

## 项目结构规范

### 1. 模块结构
```
crm-wechat/
├── crm-wechat-client/     # Dubbo接口定义和出入参定义
├── crm-wechat-dao/        # 数据库操作，MyBatis配置
├── crm-wechat-remote/     # 公共内容，启动类，切面，异常处理，配置
└── crm-wechat-service/    # 业务逻辑实现
```

### 2. 包名规范
```
crm-wechat-client:
├── com.howbuy.crm.wechat.client.facade.{业务包名}/        # 接口定义
├── com.howbuy.crm.wechat.client.domain.request.{业务包名}/ # 请求参数
├── com.howbuy.crm.wechat.client.domain.response.{业务包名}/ # 响应参数
├── com.howbuy.crm.wechat.client.enums/                    # 枚举类
└── com.howbuy.crm.wechat.client.base/                     # 基础类

crm-wechat-service:
├── com.howbuy.crm.wechat.service.facade/                  # Dubbo接口实现
├── com.howbuy.crm.wechat.service.service.{业务包名}/      # 业务服务层
├── com.howbuy.crm.wechat.service.repository/              # 数据访问层
└── com.howbuy.crm.wechat.service.controller/              # HTTP接口
```

## 接口定义规范

### 1. Dubbo接口规范
```java
/**
 * <AUTHOR>
 * @description: 接口功能描述
 * @date 2025-06-18 16:06:07
 * @since JDK 1.8
 */
public interface {业务名}Facade {

    /**
     * @api {DUBBO} com.howbuy.crm.wechat.client.facade.{业务包名}.{接口名}.{方法名}(request) {方法名}()
     * @apiVersion 1.0.0
     * @apiGroup {接口名}
     * @apiName {方法名}()
     * @apiDescription 接口功能描述
     * @apiParam (请求参数) {String} field1 字段1描述
     * @apiParam (请求参数) {String} field2 字段2描述
     * @apiParamExample 请求参数示例
     * field1=value1&field2=value2
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 C0510002-参数错误 C0510003-系统错误 C0510004-未查询到数据
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} returnObject 返回内容
     * @apiSuccess (响应结果) {String} returnObject.field1 字段1描述
     * @apiSuccess (响应结果) {String} returnObject.field2 字段2描述
     * @apiSuccessExample 响应结果示例
     * {"returnObject":{"field1":"value1","field2":"value2"},"code":"0000","description":"操作成功"}
     */
    Response<{响应VO}> {方法名}({请求Request} request);
}
```

### 2. 命名规范
- 接口名：{业务名}Facade
- 请求类：{方法名}Request
- 响应类：{方法名}VO（不使用Response后缀）
- 方法名：使用camelCase，动词开头

## 请求参数规范

### 1. 请求对象定义
```java
/**
 * @description: 请求功能描述
 * <AUTHOR>
 * @date 2025-06-18 16:06:07
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class {方法名}Request extends BaseRequest {

    private static final long serialVersionUID = {生成的序列化ID}L;
    
    /**
     * 字段描述
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "字段中文名", isRequired = true)
    private String field1;

    /**
     * 字段描述
     */
    private String field2;
}
```

### 2. 注解规范
- 使用 `@Data` 而不是 `@Getter/@Setter`
- 继承 `BaseRequest`
- 使用 `@EqualsAndHashCode(callSuper = true)`
- 参数验证使用 `@MyValidation` 注解
- List、Map等集合类型参数不加 `@MyValidation`，在业务层验证

## 响应参数规范

### 1. 响应对象定义
```java
/**
 * <AUTHOR>
 * @description: 响应功能描述
 * @date 2025-06-18 16:06:07
 * @since JDK 1.8
 */
@Data
public class {方法名}VO implements Serializable {

    private static final long serialVersionUID = {生成的序列化ID}L;

    /**
     * 字段描述
     */
    private String field1;

    /**
     * 字段描述
     */
    private String field2;
}
```

### 2. 注解规范
- 使用 `@Data` 注解
- 实现 `Serializable` 接口
- 定义 `serialVersionUID`
- 每个字段添加中文注释

## 接口实现规范

### 1. Dubbo接口实现类
```java
/**
 * <AUTHOR>
 * @description: 接口实现功能描述
 * @date 2025-06-18 16:06:07
 * @since JDK 1.8
 */
@DubboService
@Slf4j
public class {业务名}FacadeImpl implements {业务名}Facade {

    @Resource
    private {业务名}Service {业务名}Service;

    @Override
    public Response<{响应VO}> {方法名}({请求Request} request) {
        return Response.ok({业务名}Service.{方法名}(request));
    }
}
```

### 2. 注解规范
- 使用 `@DubboService` 标识Dubbo服务
- 使用 `@Slf4j` 用于日志记录
- 不使用 `@Component` 注解
- 使用 `@Resource` 注入Service

### 3. 实现规范
- 实现类命名：{接口名}Impl
- 包路径：`com.howbuy.crm.wechat.service.facade`
- 默认返回：`Response.ok(service.method(request))`
- 不在Facade层处理业务逻辑

## Service层规范

### 1. Service类定义
```java
/**
 * <AUTHOR>
 * @description: 业务服务功能描述
 * @date 2025-06-18 16:06:07
 * @since JDK 1.8
 */
@Service
@Slf4j
public class {业务名}Service {

    @Resource
    private {Repository名}Repository {repository名}Repository;

    /**
     * @param request 请求参数
     * @return 返回结果
     * @description: 方法功能描述
     * <AUTHOR>
     * @date 2025-06-18 16:06:07
     * @since JDK 1.8
     */
    public {响应VO} {方法名}({请求Request} request) {
        // 业务逻辑实现
        return new {响应VO}();
    }
}
```

### 2. 注解规范
- 使用 `@Service` 标识Spring服务
- 使用 `@Slf4j` 用于日志记录
- 使用 `@Resource` 注入Repository
- 不使用 `@Transactional`，事务在Repository层管理

## 统一响应格式

### 1. Response类结构
```java
@Data
public class Response<T> implements Serializable {
    private String code;           // 返回码
    private String description;    // 返回描述
    private T returnObject;        // 返回内容
}
```

### 2. 返回码规范
- 成功：`0000` - "操作成功"
- 参数错误：`C0510002` - "参数错误"
- 系统错误：`C0510003` - "系统错误"
- 未查询到数据：`C0510004` - "没有查询到匹配数据"

### 3. 构造方法
- 成功返回：`Response.ok(data)`
- 错误返回：`new Response(code, message, null)`

## 代码风格规范

### 1. 命名约定
- 变量：camelCase
- 方法：camelCase
- 类：PascalCase
- 常量：UPPERCASE_WITH_UNDERSCORES

### 2. 注释规范
- 类注释：包含@description、@author、@date、@since
- 方法注释：包含@description、@param、@return、@author、@date、@since
- 字段注释：使用中文描述字段用途
- API文档：使用@api格式的完整注释

### 3. 导包顺序
- 静态导入优先
- 包顺序：java → javax → org → com

### 4. 禁止使用
- 禁止使用 `BeanUtils.copyProperties` 做类属性copy
- VO/PO/DTO/BO等实体类中，使用@Setter/@Getter（不用@Data）- 仅适用于dao模块

## 文件模板

### 1. 版权声明模板
```java
/**
 * Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
```

### 2. 包声明后的类注释模板
```java
/**
 * <AUTHOR>
 * @description: 类功能描述
 * @date 2025-06-18 16:06:07
 * @since JDK 1.8
 */
```

## 生成规则

### 1. 生成顺序
1. 在crm-wechat-client中生成接口定义
2. 在crm-wechat-client中生成请求/响应对象
3. 在crm-wechat-service中生成接口实现类
4. 在crm-wechat-service中生成Service类

### 2. 默认实现
- Facade实现：返回 `Response.ok(service.method(request))`
- Service实现：返回空的VO对象或null
- 所有必要的注解和注释都要完整

### 3. 文件位置
- 接口：`crm-wechat-client/src/main/java/com/howbuy/crm/wechat/client/facade/{业务包名}/`
- 请求：`crm-wechat-client/src/main/java/com/howbuy/crm/wechat/client/domain/request/{业务包名}/`
- 响应：`crm-wechat-client/src/main/java/com/howbuy/crm/wechat/client/domain/response/{业务包名}/`
- 实现：`crm-wechat-service/src/main/java/com/howbuy/crm/wechat/service/facade/`
- 服务：`crm-wechat-service/src/main/java/com/howbuy/crm/wechat/service/service/{业务包名}/` 