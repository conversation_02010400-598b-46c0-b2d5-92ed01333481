---
description: 外部HTTP接口封装规则
globs: 
alwaysApply: false
---
# 外部HTTP接口封装生成规则

## 规则说明
根据外部HTTP接口信息，自动生成标准的HTTP服务封装类。

## 命名约定
- **服务类**: {SystemName}ApiOuterService
- **请求Context**: {BusinessName}Context
- **响应DTO**: {BusinessName}DTO
- **方法名**: {businessName}（首字母小写）

## 文件结构
```
crm-wechat-service/src/main/java/com/howbuy/crm/wechat/service/outerservice/
├── {systemPackage}/
│   ├── {SystemName}ApiOuterService.java
│   └── domain/
│       ├── context/{BusinessName}Context.java
│       └── dto/{BusinessName}DTO.java
```

## 服务类模板

```java
@Slf4j
@Service
public class {SystemName}ApiOuterService {

    @Resource
    private RestTemplate restTemplate;
    
    @Value("${systemname.api.base.url:}")
    private String baseUrl;

    /**
     * @description: {业务描述}
     * @param {param} {参数描述}
     * @return {ReturnType} 响应结果
     * <AUTHOR>
     * @date 2025-08-18 13:59:51
     */
    public {ReturnType} {methodName}({ParamType} {param}) {
        log.info("{ServiceName}>>>{methodName}请求开始，参数：{}", {param});
        
        try {
            // 构建请求对象
            {BusinessName}Context request = new {BusinessName}Context();
            request.set{Param}({param});
            
            // 构建请求URL
            String url = buildRequestUrl();
            
            // 发送HTTP请求
            ResponseEntity<String> response = HttpUtils.postForJson(url, request, String.class, restTemplate);
            
            log.info("{ServiceName}>>>{methodName}响应结果，状态码：{}，响应体：{}", 
                response.getStatusCode(), response.getBody());
            
            // 处理响应结果
            return processResponse(response);
            
        } catch (Exception e) {
            log.error("{ServiceName}>>>{methodName}调用异常", e);
            throw new BusinessException(ResponseCodeEnum.SYS_ERROR.getCode(),
                "{SystemName}系统{methodName}接口调用异常：" + e.getMessage());
        }
    }
    
    /**
     * 构建请求URL
     */
    private String buildRequestUrl() {
        String apiPath = "{apiPath}";
        return baseUrl + apiPath;
    }
    
    /**
     * 处理响应结果
     */
    private {ReturnType} processResponse(ResponseEntity<String> response) {
        // 检查HTTP状态码
        if (!response.getStatusCode().is2xxSuccessful()) {
            log.error("{ServiceName}>>>{methodName} HTTP请求失败，状态码：{}", response.getStatusCode());
            throw new BusinessException(ResponseCodeEnum.SYS_ERROR.getCode(),
                "{SystemName}系统接口调用失败，HTTP状态码：" + response.getStatusCode());
        }
        
        String responseBody = response.getBody();
        if (responseBody == null || responseBody.trim().isEmpty()) {
            log.error("{ServiceName}>>>{methodName}响应体为空");
            return null;
        }
        
        try {
            // 解析JSON响应
            {BusinessName}DTO responseDto = JSON.parseObject(responseBody, {BusinessName}DTO.class);
            
            // 检查业务状态码
            if (!isBusinessSuccess(responseDto)) {
                log.error("{ServiceName}>>>{methodName}业务处理失败，响应：{}", responseBody);
                return null;
            }
            
            // 提取结果
            return extractSimpleResult(responseDto);
            
        } catch (Exception e) {
            log.error("{ServiceName}>>>{methodName}解析响应JSON异常：{}", responseBody, e);
            throw new BusinessException(ResponseCodeEnum.SYS_ERROR.getCode(),
                "{SystemName}系统响应解析异常：" + e.getMessage());
        }
    }
    
    /**
     * 检查业务是否成功
     */
    private boolean isBusinessSuccess({BusinessName}DTO response) {
        return response != null && response.getSuccess() != null && response.getSuccess();
    }
    
    /**
     * 提取简单结果
     */
    private {ReturnType} extractSimpleResult({BusinessName}DTO response) {
        if (response == null) {
            return null;
        }
        
        // 从响应数据中提取结果
        if (response.getData() != null) {
            return String.valueOf(response.getData()); // 根据实际返回类型调整
        }
        
        return null;
    }
}
```

## 请求Context模板

```java
@Getter
@Setter
@ToString
public class {BusinessName}Context implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * {参数描述}
     */
    private {ParamType} {paramName};
    
    // 根据接口文档添加其他参数
}
```

## 响应DTO模板

```java
@Getter
@Setter
@ToString
public class {BusinessName}DTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应状态码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private Object data;
    
    /**
     * 是否成功
     */
    private Boolean success;
}
```

## 使用示例

### AI系统获取AccessToken接口
- 系统名称: aisystem
- 业务功能: getAccessToken
- 生成文件:
  - AisystemApiOuterService.java
  - GetAccessTokenContext.java
  - GetAccessTokenDTO.java

### 配置要求
```properties
# 外部系统配置
aisystem.api.base.url=https://ai-system.example.com
aisystem.api.timeout=30000
```

## 核心约定
1. 服务类命名：{SystemName}ApiOuterService
2. 包结构：outerservice.{systemname}.domain.{context|dto}
3. 使用HttpUtils工具类发送请求
4. 统一异常处理：BusinessException + ResponseCodeEnum.SYS_ERROR
5. 响应解析：先检查HTTP状态码，再检查业务状态码
6. 日志记录：请求开始、响应结果、异常信息