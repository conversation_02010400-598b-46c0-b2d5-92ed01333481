---
description: CRM微信项目级别规范指南code review
globs: 
alwaysApply: true
---

# CRM微信项目级别规范指南

## 项目概述
CRM微信系统(crm-wechat)是负责管理好买微信客户关系管理的核心系统，包含微信用户管理、消息推送、群管理、素材管理等功能。项目采用微服务架构，基于Spring Boot + Dubbo实现。

## 项目结构规范

### 模块划分
- **crm-wechat-client**: 包含Dubbo接口定义和出入参定义
  - 基础包名: `com.howbuy.crm.wechat.client`
  - 接口定义: `com.howbuy.crm.wechat.client.facade`
  - 微信认证接口: `com.howbuy.crm.wechat.client.facade.wechatauth`
  - 微信客户信息接口: `com.howbuy.crm.wechat.client.facade.wechatcustinfo`
  - 微信JS-SDK接口: `com.howbuy.crm.wechat.client.facade.wechatjssdk`
  - 微信素材接口: `com.howbuy.crm.wechat.client.facade.wechatmaterial`
  - 枚举类: `com.howbuy.crm.wechat.client.enums`
  - 接口入参: `com.howbuy.crm.wechat.client.domain.request`
  - 接口出参: `com.howbuy.crm.wechat.client.domain.response`

- **crm-wechat-remote**: 包含公共内容，如启动类、切面、异常处理等
  - 基础包名: `com.howbuy.crm.wechat.remote`
  - 启动类: `com.howbuy.crm.wechat.remote.CrmWechatApplication`
  - 配置目录: `src/main/resources/`

- **crm-wechat-dao**: 包含数据库操作和ORM相关配置
  - 基础包名: `com.howbuy.crm.wechat.dao`
  - 数据库操作: `com.howbuy.crm.wechat.dao.mapper`
    - **自定义SQL操作**: 对于自定义的SQL操作，应在 `com.howbuy.crm.wechat.dao.mapper.customize` 包中创建名为 `CustomizeXXXXMapper` 的接口。例如，`CmWechatCustInfoPOMapper` 对应的自定义Mapper为 `CustomizeCmWechatCustInfoPOMapper`。
  - 数据库实体: `com.howbuy.crm.wechat.dao.po`
  - 关联查询对象: `com.howbuy.crm.wechat.dao.bo`
  - 视图对象: `com.howbuy.crm.wechat.dao.vo`

- **crm-wechat-service**: 包含业务逻辑实现
  - 基础包名: `com.howbuy.crm.wechat.service`
  - Dubbo接口实现: `com.howbuy.crm.wechat.service.facade`
  - 业务实现: `com.howbuy.crm.wechat.service.service`
  - 事务管理: `com.howbuy.crm.wechat.service.repository`
  - 控制器: `com.howbuy.crm.wechat.service.controller`
  - 外部接口封装: `com.howbuy.crm.wechat.service.outservice`

## 命名规范

### 通用命名规则
- **类名**: 使用PascalCase（首字母大写的驼峰命名法），如`WechatAuthController`、`WechatCustInfoService`
- **方法名和变量名**: 使用camelCase（首字母小写的驼峰命名法），如`getUserIdByCode`、`wechatAppId`
- **常量**: 使用大写字母和下划线分隔，如`MAX_RETRY_COUNT`、`WECHAT_API_TIMEOUT`
- **包名**: 全小写，使用点分隔，如`com.howbuy.crm.wechat.service`

### 特定组件命名规则

#### 接口和实体类
- **Dubbo接口**: 以`Facade`结尾，如`WechatAuthFacade`、`WechatCustInfoFacade`
- **接口入参**: 接口名+`Request`，如`GetUserIdRequest`、`QueryWechatCustInfoRequest`
- **接口出参**: 接口名+`Response`，如`GetUserIdResponse`、`QueryWechatCustInfoResponse`
- **数据库实体**: 表名+`PO`，如`CmWechatCustInfoPO`、`CmWechatEmpPO`
- **业务对象**: 业务功能+`BO`，如`WechatUserBO`、`WechatGroupBO`
- **视图对象**: 业务功能+`VO`，如`WechatCustInfoVO`、`GetConfigSignatureVO`

#### 服务层组件
- **Dubbo实现类**: 接口名+`Impl`，如`WechatAuthFacadeImpl`、`WechatCustInfoFacadeImpl`
- **Service类**: 业务功能+`Service`，如`WechatAuthService`、`WechatCustInfoService`
- **Repository类**: 表名+`Repository`，如`CmWechatCustInfoRepository`、`CmWechatEmpRepository`
- **Mapper接口**: 表名+`Mapper`，如`CmWechatCustInfoMapper`、`CmWechatEmpMapper`

## 接口定义规范

### Dubbo接口定义
1. 接口必须继承`BaseFacade<Request, Response>`
2. 接口必须使用APIDOC风格注释，包含以下内容：
   - API路径(`@api`)
   - API版本(`@apiVersion`)
   - API组(`@apiGroup`)
   - API名称(`@apiName`)
   - API描述(`@apiDescription`)
   - 请求参数(`@apiParam`)
   - 请求示例(`@apiParamExample`)
   - 响应结果(`@apiSuccess`)
   - 响应示例(`@apiSuccessExample`)

```java
/**
 * @api {DUBBO} com.howbuy.crm.wechat.client.facade.wechatauth.WechatAuthFacade.getUserId()
 * @apiVersion 1.0.0
 * @apiGroup WechatAuthFacadeImpl
 * @apiName getUserId
 * @apiDescription 根据微信code获取用户ID
 * @apiParam (请求体) {String} code 微信授权code
 * @apiParamExample 请求体示例
 * {"code":"08123456789012345678901234567890"}
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccessExample 响应结果示例
 * {"code":"0000","data":{"userId":"123456"},"description":"成功"}
 */
public interface WechatAuthFacade extends BaseFacade<GetUserIdRequest, GetUserIdResponse> {
}
```

### 入参/出参定义规范
1. 请求类必须继承`BaseRequest`
2. 响应类必须定义合理的返回结构
3. 所有字段必须有注释说明用途
4. 使用`@Getter`和`@Setter`注解（不使用`@Data`）
5. 字段命名遵循Java驼峰命名法
6. 敏感字段应使用合适的序列化/反序列化策略

```java
@Getter
@Setter
public class GetUserIdRequest extends BaseRequest {
    /**
     * 微信授权code
     */
    private String code;
    
    /**
     * 微信应用ID
     */
    private String appId;
}
```

## 接口实现规范

### Dubbo接口实现类
1. 实现类必须位于`com.howbuy.crm.wechat.service.facade`包下，子包结构应与接口包保持一致
2. 实现类命名为接口名+`Impl`
3. 使用`@DubboService`注解暴露服务
4. 通过`@Resource`注入所需的Service类
5. 方法实现应简洁，主要负责参数校验和Service层的调用，不包含具体业务逻辑

```java
@DubboService
public class WechatAuthFacadeImpl implements WechatAuthFacade {
    @Resource
    private WechatAuthService wechatAuthService;
    
    @Override
    public Response<GetUserIdResponse> getUserId(GetUserIdRequest request) {
        return Response.ok(wechatAuthService.getUserId(request));
    }
}
```

## 服务层调用规范

### Service层
1. Service类应位于`com.howbuy.crm.wechat.service.service`包下，根据业务功能划分子包
   - 微信认证: `com.howbuy.crm.wechat.service.service.wechatauth`
   - 客户信息: `com.howbuy.crm.wechat.service.service.wechatcustinfo`
   - 群管理: `com.howbuy.crm.wechat.service.service.wechatgroup`
   - 消息管理: `com.howbuy.crm.wechat.service.service.message`
2. 使用`@Service`注解将服务注册到Spring容器
3. 使用`@Slf4j`注解添加日志支持
4. 通过`@Resource`注入Repository类和其他Service类
5. 实现具体业务逻辑，不直接操作数据库
6. 方法应有完整的Javadoc注释

```java
@Service
@Slf4j
public class WechatAuthService {
    @Resource
    private CmWechatCustInfoRepository wechatCustInfoRepository;
    
    /**
     * @description: 根据微信code获取用户ID
     * @param request 微信授权请求参数
     * @return 用户ID响应
     * @author: developer.name
     * @date: 2025-07-19 14:41:00
     * @since JDK 1.8
     */
    public GetUserIdResponse getUserId(GetUserIdRequest request) {
        // 业务逻辑实现
        return new GetUserIdResponse();
    }
}
```

### Repository层
1. Repository类应位于`com.howbuy.crm.wechat.service.repository`包下
2. 使用`@Repository`注解将仓库注册到Spring容器
3. 使用`@Transactional`注解控制事务
4. 通过`@Resource`注入Mapper接口
5. 实现数据库操作逻辑，不包含业务逻辑
6. 方法应有完整的Javadoc注释

```java
@Repository
@Slf4j
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class CmWechatCustInfoRepository {
    @Resource
    private CmWechatCustInfoMapper cmWechatCustInfoMapper;
    
    /**
     * @description: 根据用户ID查询微信客户信息
     * @param userId 用户ID
     * @return 微信客户信息实体
     * @author: developer.name
     * @date: 2025-07-19 14:41:00
     * @since JDK 1.8
     */
    public CmWechatCustInfoPO queryByUserId(String userId) {
        return cmWechatCustInfoMapper.selectByUserId(userId);
    }
    
    /**
     * @description: 保存微信客户信息
     * @param entity 微信客户信息实体
     * @return 影响行数
     * @author: developer.name
     * @date: 2025-07-19 14:41:00
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int save(CmWechatCustInfoPO entity) {
        return cmWechatCustInfoMapper.insert(entity);
    }
}
```

## 事务管理规范

### 事务注解使用原则
1. Repository层方法默认使用`@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)`
2. 数据修改操作使用`@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)`
3. 查询操作不需要修改默认的事务传播行为
4. 复杂业务场景可在Service层添加事务控制

### 事务传播行为说明
- `REQUIRED`: 如果当前存在事务，则加入该事务；如果当前没有事务，则创建一个新的事务
- `SUPPORTS`: 如果当前存在事务，则加入该事务；如果当前没有事务，则以非事务方式执行
- `MANDATORY`: 如果当前存在事务，则加入该事务；如果当前没有事务，则抛出异常
- `REQUIRES_NEW`: 创建一个新的事务，如果当前存在事务，则挂起当前事务
- `NOT_SUPPORTED`: 以非事务方式执行，如果当前存在事务，则挂起当前事务
- `NEVER`: 以非事务方式执行，如果当前存在事务，则抛出异常
- `NESTED`: 如果当前存在事务，则创建一个事务作为当前事务的嵌套事务来执行；如果当前没有事务，则等价于`REQUIRED`

## 注释规范

### 类注释
```java
/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

/**
 * @description: 类功能描述
 * <AUTHOR>
 * @date 2025-07-19 14:41:00
 * @since JDK 1.8
 */
```

### 方法注释
```java
/**
 * @description: 方法功能描述
 * @param paramName 参数说明
 * @return 返回值说明
 * @author: developer.name
 * @date: 2025-07-19 14:41:00
 * @since JDK 1.8
 */
```

## 异常处理规范

1. 使用统一的异常处理机制
2. 业务异常应继承自应用的基础异常类
3. 合理使用自定义异常和错误码
4. 异常信息应包含足够的上下文信息，便于问题定位
5. 不要捕获异常后不处理或仅打印日志
6. 业务异常优先在 `com.howbuy.crm.wechat.service.commom.enums.ExceptionEnum` 查找，如不存在则在ExceptionEnum中新增

```java
try {
    // 业务逻辑
} catch (BusinessException e) {
    log.error("业务处理异常: {}", e.getMessage(), e);
    throw e;
} catch (Exception e) {
    log.error("系统异常: {}", e.getMessage(), e);
    throw new SystemException("系统异常", e);
}
```

## 枚举使用规范
在 `com.howbuy.crm.wechat.client.enums` 和 `com.howbuy.crm.wechat.service.commom.enums` 包下定义项目专属枚举。常用的枚举类型包括：
- 消息状态枚举: `MessageSendStatusEnum`
- 微信应用类型: `WechatAppEnum`
- 响应码枚举: `ResponseCodeEnum`
- 上传类型枚举: `WechatUploadTypeEnum`

## 日志规范

1. 使用SLF4J + Log4j2进行日志记录
2. 日志级别合理使用:
   - ERROR: 系统错误，需要立即关注的问题
   - WARN: 潜在的问题，可能需要关注
   - INFO: 重要业务操作，可用于生产环境问题跟踪
   - DEBUG: 调试信息，仅在开发和测试环境使用

3. 日志内容应包含足够的上下文信息
4. 敏感信息不应记录到日志中
5. 使用占位符而非字符串拼接

```java
// 正确的做法
log.info("处理微信用户, 用户ID: {}, 微信OpenId: {}", userId, openId);

// 错误的做法
log.info("处理微信用户, 用户ID: " + userId + ", 微信OpenId: " + openId);
```

## 微信特定规范

### 微信API调用规范
1. 所有微信API调用必须使用统一的`WechatApiService`
2. 微信access_token必须缓存并定时刷新
3. 微信API调用必须处理频率限制和错误码
4. 敏感信息（如appsecret）必须加密存储

### 微信消息处理规范
1. 微信消息必须实现幂等性处理
2. 微信消息必须记录处理日志
3. 微信消息必须验证消息签名
4. 微信消息必须处理重试机制

## 代码审查重点

在进行代码审查时，应重点关注以下方面：

1. **命名规范**: 类名、方法名、变量名是否符合规范
2. **接口定义**: Dubbo接口定义是否符合规范，包括注释、入参出参等
3. **接口实现**: 实现类是否位于正确的包下，是否使用了正确的注解
4. **服务层调用**: Service与Repository的职责是否分明，方法是否有必要的注释
5. **事务管理**: 是否正确使用了事务注解，事务传播行为是否合适
6. **异常处理**: 是否有统一的异常处理机制，是否合理使用了自定义异常
7. **日志规范**: 是否使用了正确的日志级别，日志内容是否合适
8. **微信规范**: 是否遵循了微信特定的开发规范

每个错误码都应有详细的说明文档，便于开发和运维人员查阅。

## 安全规范

1. 敏感数据（如密码、证件号、微信appsecret）需要加密存储
2. API调用需要进行身份验证和授权
3. 防止SQL注入、XSS等常见安全问题
4. 日志中不应包含敏感信息
5. 错误响应不应暴露系统内部信息
6. 微信相关配置必须安全存储，不能硬编码

## 性能优化指南

1. 合理使用索引提高查询性能
2. 避免N+1查询问题
3. 使用批量操作替代循环单条操作
4. 使用缓存减少数据库访问
   - 微信access_token缓存
   - 微信jsapi_ticket缓存
   - 用户信息缓存
5. 大数据量处理时使用分页查询
6. 合理设置连接池参数
7. 使用异步处理提高并发能力
8. 微信API调用必须考虑频率限制

## 附录
- 业务码对照表
- 常用状态码说明
- 数据库表关系图
- 微信错误码对照表
- 微信API接口文档链接