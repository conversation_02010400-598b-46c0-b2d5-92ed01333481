# ✅ AI-Dubbo接口代码生成提示词（基于详细设计-CRM微信项目专用）
你是一名资深Java开发和架构师,请根据我提供的**Dubbo接口详细设计文档**内容，严格遵循当前项目的代码规范和结构，生成完整的**后端代码实现**。

## 📌 核心输出要求
- **输出格式**：Java 源代码，按项目模块（`client`, `service`, `dao`）和文件类型（`Facade`, `Request`, `Response`, `Impl`, `Service`, `Repository`, `PO`, `Mapper`）分块输出，确保代码可直接应用到项目中。
- **遵循项目规范**：严格遵守 `.cursor/rules/` 下定义的项目结构、命名约定、注释标准和编码风格。
- **复用优先**：优先使用项目中已有的公共组件、工具类（如日期、字符串、集合处理）、和基础框架，禁止重复造轮子。
- **幂等性与健壮性**：关键的写操作接口（创建、更新）需要考虑幂等性设计，并对外部调用、数据库操作等进行必要的异常处理。

## 📘 你的任务：基于设计文档生成代码
请根据我提供的**详细设计文档**，一次性生成所有相关的代码。

### 设计文档结构（输入）
- **功能名称**: 接口的核心业务功能，例如 "查询微信客户信息"、"创建微信群组"。
- **接口定义**:
    - **接口名**: `QueryWechatCustInfoFacade`
    - **方法名**: `execute` 或具体业务方法名
- **请求参数（Request）**: 字段名、Java类型、中文描述、是否必填、校验规则（如长度、格式、取值范围）。
- **响应参数（Response）**: 字段名、Java类型、中文描述。
- **关键业务处理流程**:
    1.  **参数校验**: 检查请求参数的完整性和有效性。
    2.  **业务校验**: 检查业务规则是否满足，如用户状态、权限等。
    3.  **核心逻辑**: 描述数据处理、状态流转、计算等核心步骤。
    4.  **数据持久化**: 说明需要操作的数据库表及字段。
    5.  **外部调用**: 列出需要调用的其他Dubbo服务或HTTP接口。
- **异常处理策略**: 定义不同场景下的错误码和异常信息。
- **事务管理**: 明确事务的边界和传播行为。

### 代码生成清单（输出）
请按以下结构生成代码，确保所有文件都符合项目规范。

#### 1. `crm-wechat-client` 模块
- **接口定义 (`Facade`)**
    - **位置**: `crm-wechat-client/src/main/java/com/howbuy/crm/wechat/client/facade/{业务模块}/...`
    - **命名**: `功能名Facade.java`
    - **规范**:
        - 必须包含完整的Javadoc注释，包含接口功能说明。
        - 使用中文注释描述接口用途和注意事项。
        - 例如：`wechatauth`, `wechatcustinfo`, `wechatjssdk`, `wechatmaterial`等业务模块

- **请求对象 (`Request`)**
    - **位置**: `crm-wechat-client/src/main/java/com/howbuy/crm/wechat/client/domain/request/{业务模块}/...`
    - **命名**: `接口名Request.java`
    - **规范**:
        - 继承 `com.howbuy.crm.wechat.client.producer.BaseRequest`。
        - 使用 `@Getter` / `@Setter` 注解（不使用@Data）。
        - 所有字段必须有清晰的中文Javadoc注释。
        - 包含必要的参数校验注解。

- **响应对象 (`Response/VO`)**
    - **位置**: `crm-wechat-client/src/main/java/com/howbuy/crm/wechat/client/domain/response/{业务模块}/...`
    - **命名**: `接口名Response.java` 或 `接口名VO.java`
    - **规范**:
        - 使用 `@Getter` / `@Setter` 注解（不使用@Data）。
        - 所有字段必须有清晰的中文Javadoc注释。
        - 统一返回格式使用 `com.howbuy.crm.wechat.client.base.Response<T>`。

- **枚举类 (`Enum`)**
    - **位置**: `crm-wechat-client/src/main/java/com/howbuy/crm/wechat/client/enums/...`
    - **命名**: `枚举名Enum.java`
    - **规范**:
        - 包含code、desc字段。
        - 提供构造方法和getter方法。
        - 包含中文注释说明。

#### 2. `crm-wechat-service` 模块
- **接口实现 (`FacadeImpl`)**
    - **位置**: `crm-wechat-service/src/main/java/com/howbuy/crm/wechat/service/facade/...`
    - **命名**: `功能名FacadeImpl.java`
    - **规范**:
        - 实现对应的 `Facade` 接口。
        - 使用 `@DubboService` 注解暴露服务。
        - 使用 `@Slf4j` 注解添加日志。
        - 注入 `Service` 层并调用其方法，只做参数转换和基本校验，不实现核心业务逻辑。
        - 添加标准的类和方法注释（中文）。
        - 使用 `Response.ok()` 或 `Response.fail()` 返回统一格式。

- **业务逻辑 (`Service`)**
    - **位置**: `crm-wechat-service/src/main/java/com/howbuy/crm/wechat/service/service/{业务模块}/...`
    - **命名**: `功能名Service.java`
    - **规范**:
        - 使用 `@Service` 和 `@Slf4j` 注解。
        - 实现所有核心业务逻辑、业务校验和流程编排。
        - 调用 `Repository` 层进行数据持久化。
        - 方法需有完整的Javadoc注释（`@description`, `@param`, `@return`, `@author`, `@date`）。
        - 使用中文注释。
        - 调用其他服务时使用 `@Resource` 注解注入。

- **数据仓库 (`Repository`)**
    - **位置**: `crm-wechat-service/src/main/java/com/howbuy/crm/wechat/service/repository/...`
    - **命名**: `表名Repository.java`
    - **规范**:
        - 使用 `@Repository` 注解。
        - 注入 `Mapper` 接口，封装数据库操作。
        - 写操作方法使用 `@Transactional(propagation = Propagation.REQUIRED)`。
        - 类级别使用 `@Transactional(propagation = Propagation.SUPPORTS)`。
        - 方法需有完整的Javadoc注释（中文）。
        - 使用 `@Resource` 注解注入Mapper。

#### 3. `crm-wechat-dao` 模块
- **数据实体 (`PO`)**
    - **位置**: `crm-wechat-dao/src/main/java/com/howbuy/crm/wechat/dao/po/...`
    - **命名**: `表名PO.java`
    - **规范**:
        - 与数据库表字段一一对应。
        - 使用 `@Getter` / `@Setter` 注解（不使用@Data）。
        - 字段有中文注释。
        - 包含创建时间、更新时间等基础字段。

- **业务对象 (`BO`)**
    - **位置**: `crm-wechat-dao/src/main/java/com/howbuy/crm/wechat/dao/bo/...`
    - **命名**: `业务名BO.java`
    - **规范**:
        - 用于关联查询非数据库实体对象。
        - 使用 `@Getter` / `@Setter` 注解。
        - 字段有中文注释。

- **Mapper 接口**
    - **位置**: `crm-wechat-dao/src/main/java/com/howbuy/crm/wechat/dao/mapper/...`
    - **命名**: `表名Mapper.java`
    - **规范**:
        - 提供基础的CRUD操作方法。
        - 复杂查询可以放在 `customize` 子包下。
        - 方法名清晰表达操作意图。

- **Mapper XML**
    - **位置**: `crm-wechat-dao/src/main/resources/com/howbuy/crm/wechat/dao/mapper/...`
    - **命名**: `表名Mapper.xml`
    - **规范**:
        - 包含完整的SQL语句。
        - 使用规范的MyBatis语法。
        - 注意SQL性能优化。

## 🔧 技术规范要求
### 编码规范
- **JDK版本**: 1.8
- **框架**: Spring Boot, Spring Cloud, Dubbo
- **数据库**: MySQL + MyBatis + Druid连接池
- **缓存**: Redis
- **命名约定**:
    - 变量: camelCase（小驼峰）
    - 方法: camelCase（小驼峰）
    - 类: PascalCase（大驼峰）
    - 常量: UPPERCASE_WITH_UNDERSCORES（全大写+下划线）
- **缩进**: 4个空格
- **每行最大长度**: 120字符
- **使用阿里巴巴代码规范**
- **使用Lombok注解**
- **使用Dubbo注解**

### 注释要求
- **Controller层、Service层、Repository层和关键业务逻辑必须添加Javadoc注释**
- **注释必须使用中文**
- **类和方法注释需说明其功能和使用场景**
- **@RequestMapping/@GetMapping等注解需说明API的用途及注意事项**
- **Dubbo接口注释需说明其功能和使用场景**

### 包分层规则
- **controller > service > repository**: controller层调用service层，service层调用repository层
- **dubbo > service > repository**: dubbo接口层调用service层，service层调用repository层
- **business > repository**: 业务层调用repository层
- **service > validator**: service层调用validator层
- **service > cacheservice**: service层调用缓存层
- **service > outservice**: service层调用外部接口

### 代码模板示例
```java
/**
 * @description: 微信客户信息查询接口实现
 * <AUTHOR>
 * @date 2025-07-19 07:39:02
 * @since JDK 1.8
 */
@DubboService
@Slf4j
public class WechatCustInfoFacadeImpl implements WechatCustInfoFacade {

    @Resource
    private WechatCustInfoService wechatCustInfoService;

    @Override
    public Response<WechatCustInfoVO> queryWechatCustInfo(QueryWechatCustInfoRequest request) {
        log.info("查询微信客户信息，请求参数：{}", JSON.toJSONString(request));
        try {
            WechatCustInfoVO result = wechatCustInfoService.queryWechatCustInfo(request);
            return Response.ok(result);
        } catch (Exception e) {
            log.error("查询微信客户信息异常", e);
            return Response.fail("查询失败");
        }
    }
}
```

## 🚫 禁止事项
| 类型                 | 说明                                                         |
| -------------------- | ------------------------------------------------------------ |
| ❌ **违反项目规范**  | 所有代码的包结构、命名、注释必须符合项目规范。 |
| ❌ **硬编码**        | 禁止在代码中硬编码任何常量、URL、配置项，应使用常量类或配置中心。 |
| ❌ **逻辑泄露**      | 业务逻辑必须封装在 `Service` 层，`FacadeImpl` 只做调度和校验。 |
| ❌ **忽略异常处理**   | 必须对外部调用和数据库操作进行 `try-catch`，并进行合理的异常转换和日志记录。 |
| ❌ **绕过Repository** | `Service` 层禁止直接注入和调用 `Mapper`，必须通过 `Repository` 层访问数据库。 |
| ❌ **禁止使用魔法值** | 任何未经定义的字面量（字符串、数字）都应定义为常量或枚举。 |
| ❌ **缺少枚举或常量** | 对于数据库字段或接口参数中的状态、类型等字段，必须创建对应的枚举类或常量。 |
| ❌ **禁止使用@Data** | 实体类中必须使用@Getter/@Setter，不能使用@Data注解。 |
| ❌ **禁止BeanUtils.copyProperties** | 禁止使用BeanUtils.copyProperties做类属性copy。 |
| ❌ **注释覆盖问题** | 如果类或方法已经有注释，不要更新或覆盖原有注释中的@author和@date的值。 |

## 📋 生成步骤
1. **分析设计文档**：理解业务需求和技术要求
2. **确定包结构**：根据功能模块确定代码存放位置
3. **生成Client模块**：接口定义、请求响应对象、枚举类
4. **生成Service模块**：接口实现、业务逻辑、数据仓库
5. **生成Dao模块**：数据实体、Mapper接口和XML
6. **代码优化**：检查规范合规性、异常处理、日志记录

**请在我提供详细设计文档后，立即按上述要求生成完整、高质量的代码。** 