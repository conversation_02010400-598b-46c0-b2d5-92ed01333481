# ✅ CRM微信项目-外部HTTP接口封装AI提示词

你是一名资深Java开发工程师，请根据我提供的**外部HTTP接口文档**，生成符合**CRM微信项目标准**的**外部HTTP接口封装代码**，输出要求如下：

## 📌 输出要求：
- **输出格式**：Java源代码，按模块结构输出，可直接粘贴到项目中使用
- **遵循规范**：严格遵循项目规范，规则文件路径为 `.cursor/rules/auto-http-wrapper-rules.mdc`
- **完整封装**：包含Service类、Context/DTO对象（如需要）、异常处理、日志记录
- **生产就绪**：代码质量达到生产级标准，包含完整的错误处理和日志记录

## 📋 外部HTTP接口封装规范

### 包结构规范
```
com.howbuy.crm.wechat.service.outerservice
├── [系统名]/                          # 外部系统名称（如aisystem、wechatapi等）
│   ├── {SystemName}ApiOuterService.java   # 系统级HTTP接口封装类
│   └── domain/                            # 数据对象目录
│       ├── context/                       # 请求上下文对象
│       │   └── {BusinessName}Context.java
│       └── dto/                           # 响应数据对象
│           └── {BusinessName}DTO.java
```

### 命名规范
- **封装类名**：`{SystemName}ApiOuterService`（如`AisystemApiOuterService`、`WechatApiOuterService`）
- **请求Context**：`{BusinessName}Context`（如`GetAccessTokenContext`、`QueryUserInfoContext`）
- **响应DTO**：`{BusinessName}DTO`（如`GetAccessTokenDTO`、`QueryUserInfoDTO`）
- **方法名称**：使用业务语义化命名，体现具体功能（如`getAccessToken`、`queryUserInfo`、`sendMessage`）

### 代码结构模板

#### 1. 外部HTTP接口封装类
```java
/**
 * Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.outerservice.{systemname};

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.service.commom.exception.BusinessException;
import com.howbuy.crm.wechat.service.commom.utils.HttpUtils;
import com.howbuy.crm.wechat.service.outerservice.{systemname}.domain.context.{BusinessName}Context;
import com.howbuy.crm.wechat.service.outerservice.{systemname}.domain.dto.{BusinessName}DTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: {SystemName}系统HTTP接口封装服务
 * @date 2025-08-18 13:59:51
 * @since JDK 1.8
 */
@Slf4j
@Service
public class {SystemName}ApiOuterService {

    @Resource
    private RestTemplate restTemplate;
    
    @Value("${systemname.api.base.url:}")
    private String baseUrl;
    
    @Value("${systemname.api.timeout:30000}")
    private int timeout;

    /**
     * @description: {业务功能描述}
     * @param {param} {参数说明}
     * @return {ReturnType} {返回值说明}
     * <AUTHOR>
     * @date 2025-08-18 13:59:51
     * @since JDK 1.8
     */
    public {ReturnType} {methodName}({ParamType} {param}) {
        log.info("{ServiceName}>>>{methodName}请求开始，参数：{}", {param});
        
        try {
            // 构建请求对象
            {BusinessName}Context request = new {BusinessName}Context();
            request.set{Param}({param});
            
            // 构建请求URL
            String url = buildRequestUrl();
            
            // 使用HttpUtils发送POST请求
            ResponseEntity<String> response = HttpUtils.postForJson(url, request, String.class, restTemplate);
            
            log.info("{ServiceName}>>>{methodName}响应结果，状态码：{}，响应体：{}", 
                response.getStatusCode(), response.getBody());
            
            // 处理响应结果
            return processResponse(response);
            
        } catch (Exception e) {
            log.error("{ServiceName}>>>{methodName}调用异常", e);
            throw new BusinessException(ResponseCodeEnum.SYS_ERROR.getCode(),
                "{SystemName}系统{methodName}接口调用异常：" + e.getMessage());
        }
    }
    
    /**
     * 构建请求URL
     */
    private String buildRequestUrl() {
        String apiPath = "{apiPath}";
        return baseUrl + apiPath;
    }
    
    /**
     * 处理响应结果
     */
    private {ReturnType} processResponse(ResponseEntity<String> response) {
        // 检查HTTP状态码
        if (!response.getStatusCode().is2xxSuccessful()) {
            log.error("{ServiceName}>>>{methodName} HTTP请求失败，状态码：{}", response.getStatusCode());
            throw new BusinessException(ResponseCodeEnum.SYS_ERROR.getCode(),
                "{SystemName}系统接口调用失败，HTTP状态码：" + response.getStatusCode());
        }
        
        String responseBody = response.getBody();
        if (responseBody == null || responseBody.trim().isEmpty()) {
            log.error("{ServiceName}>>>{methodName}响应体为空");
            return null;
        }
        
        try {
            // 解析JSON响应
            {BusinessName}DTO responseDto = JSON.parseObject(responseBody, {BusinessName}DTO.class);
            
            // 检查业务状态码
            if (!isBusinessSuccess(responseDto)) {
                log.error("{ServiceName}>>>{methodName}业务处理失败，响应：{}", responseBody);
                return null;
            }
            
            // 提取结果
            return extractSimpleResult(responseDto);
            
        } catch (Exception e) {
            log.error("{ServiceName}>>>{methodName}解析响应JSON异常：{}", responseBody, e);
            throw new BusinessException(ResponseCodeEnum.SYS_ERROR.getCode(),
                "{SystemName}系统响应解析异常：" + e.getMessage());
        }
    }
    
    /**
     * 检查业务是否成功
     */
    private boolean isBusinessSuccess({BusinessName}DTO response) {
        // TODO: 根据{SystemName}系统的实际响应格式进行判断
        // 常见的判断方式：
        // return "0000".equals(response.getCode()) || "SUCCESS".equals(response.getStatus());
        return response != null && response.getSuccess() != null && response.getSuccess();
    }
    
    /**
     * 提取简单结果
     */
    private {ReturnType} extractSimpleResult({BusinessName}DTO response) {
        if (response == null) {
            return null;
        }
        
        // 从响应数据中提取结果
        if (response.getData() != null) {
            return String.valueOf(response.getData()); // 根据实际返回类型调整
        }
        
        return null;
    }
}
```

#### 2. 请求Context对象
```java
/**
 * Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.outerservice.{systemname}.domain.context;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: {SystemName}系统{methodName}接口请求上下文
 * @date 2025-08-18 13:59:51
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class {BusinessName}Context implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * {参数描述}
     */
    private {ParamType} {paramName};
    
    // 根据接口文档添加其他参数
}
```

#### 3. 响应DTO对象
```java
/**
 * Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.outerservice.{systemname}.domain.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: {SystemName}系统{methodName}接口响应参数
 * @date 2025-08-18 13:59:51
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class {BusinessName}DTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应状态码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private Object data;
    
    /**
     * 是否成功
     */
    private Boolean success;
}
```

## 🔧 技术实现要点

### 1. HTTP客户端选择
- **首选HttpUtils**：使用`com.howbuy.crm.wechat.service.commom.utils.HttpUtils`进行HTTP请求
- **支持方法**：`HttpUtils.postForJson()`、`HttpUtils.get()`等
- **依赖RestTemplate**：HttpUtils内部封装RestTemplate实现

### 2. 请求处理模式
- **简化封装**：使用HttpUtils.postForJson统一处理JSON请求
- **URL构建**：通过buildRequestUrl()私有方法构建完整URL
- **参数封装**：所有请求参数封装到Context对象中

### 3. 响应处理策略
- **三层检查**：HTTP状态码 → 响应体非空 → 业务状态码
- **JSON解析**：统一使用FastJSON进行响应解析
- **业务判断**：通过isBusinessSuccess()方法检查业务状态
- **结果提取**：通过extractSimpleResult()方法提取具体结果

### 4. 异常处理规范
- **统一异常**：所有异常转换为`BusinessException`
- **错误码使用**：统一使用`ResponseCodeEnum.SYS_ERROR`
- **日志记录**：记录请求参数、响应状态码、响应体和异常信息
- **异常消息**：包含系统名和方法名的详细错误信息

### 5. 日志规范
- **使用@Slf4j**：统一使用SLF4J日志框架
- **日志级别**：INFO记录请求开始和响应结果，ERROR记录异常
- **日志格式**：`{ServiceName}>>>{methodName}{操作描述}`
- **关键信息**：包含请求参数、状态码、响应体等

### 6. 配置策略
- **外部配置**：使用@Value注解从配置文件读取URL和超时时间
- **默认值**：提供合理的默认值，如timeout默认30000ms
- **配置格式**：`{systemname}.api.base.url`和`{systemname}.api.timeout`

## 📋 实际示例参考

### AI系统获取AccessToken接口
参考实际代码：`AiSystemApiOuterService.getAccessToken(String corpId)`

**生成文件结构：**
```
com.howbuy.crm.wechat.service.outerservice.aisystem/
├── AiSystemApiOuterService.java
└── domain/
    ├── context/GetAccessTokenContext.java
    └── dto/GetAccessTokenDTO.java
```

**关键实现特点：**
- 使用HttpUtils.postForJson发送请求
- 通过ResponseCodeEnum.SYS_ERROR处理异常
- 响应解析：HTTP状态码 → 业务状态码 → 数据提取
- 详细的日志记录和异常处理

## 📘 输入内容包含：
由我提供的**外部HTTP接口文档**，结构如下：
- **外部系统名称**：如aisystem、wechatapi、thirdparty等
- **接口信息**：
  - 接口URL和路径
  - 请求方法（GET/POST/PUT/DELETE）
  - 请求头要求
- **请求参数结构**：包括字段名、类型、说明、是否必填
- **响应参数结构**：包括字段名、类型、说明
- **认证方式**：如token、签名、API Key等
- **业务场景说明**：接口的具体用途和调用场景

## 🧠 你的实现任务：
请根据外部HTTP接口文档，生成包括以下内容的完整代码：

1. **外部HTTP接口封装类**
   - 正确的包结构和类名：`{SystemName}ApiOuterService`
   - 使用HttpUtils发送HTTP请求
   - 完整的三层响应处理：HTTP状态码 → 业务状态码 → 数据提取
   - 标准的类和方法注释
   - 统一的异常处理和日志记录

2. **请求Context对象**
   - 包含所有请求参数的封装对象
   - 类名格式：`{BusinessName}Context`
   - 使用@Getter/@Setter/@ToString注解
   - 完整的字段注释

3. **响应DTO对象**
   - 外部系统响应数据的映射对象
   - 类名格式：`{BusinessName}DTO`
   - 标准字段：code、message、data、success
   - 使用@Getter/@Setter/@ToString注解

## 🚫 禁止事项：
| 类型 | 说明 |
|------|------|
| ❌ 使用BeanUtils.copyProperties | 项目规范禁止使用，必须手动赋值 |
| ❌ 缺少异常处理 | 必须有完整的异常处理和日志记录 |
| ❌ 硬编码URL和参数 | URL应通过@Value配置注入 |
| ❌ 缺少空值校验 | 对响应体必须进行空值校验 |
| ❌ 使用@Data注解 | Context和DTO对象必须使用@Getter/@Setter |
| ❌ 日志随意输出 | 必须使用@Slf4j，禁止使用System.out.println |
| ❌ 不使用HttpUtils | 必须使用项目封装的HttpUtils工具类 |
| ❌ 异常处理不统一 | 必须使用BusinessException + ResponseCodeEnum.SYS_ERROR |
| ❌ 忽略业务状态码检查 | 必须实现isBusinessSuccess()方法检查业务状态 |

## 📎 项目依赖的已有组件：

### 核心组件
- **HTTP工具**：`com.howbuy.crm.wechat.service.commom.utils.HttpUtils`
- **异常处理**：`com.howbuy.crm.wechat.service.commom.exception.BusinessException`
- **错误码枚举**：`com.howbuy.crm.wechat.client.enums.ResponseCodeEnum`
- **JSON处理**：`com.alibaba.fastjson.JSON`
- **RestTemplate**：`org.springframework.web.client.RestTemplate`

### 可用的错误码
```java
// 系统错误
ResponseCodeEnum.SYS_ERROR.getCode() 
```

## 🎯 使用方式：
在我提供外部HTTP接口文档后，立即生成符合上述规范的完整Java代码实现，按包结构清晰输出，确保代码可以直接在项目中使用。

**特别提醒**：
- 严格按照`{SystemName}ApiOuterService`命名规范
- 包结构必须为：`outerservice.{systemname}.domain.{context|dto}`
- 必须使用HttpUtils工具类发送HTTP请求
- 异常处理统一使用BusinessException + ResponseCodeEnum.SYS_ERROR
- 响应处理必须包含三层检查：HTTP状态码、响应体非空、业务状态码
- 所有类必须包含完整的版权声明和标准注释