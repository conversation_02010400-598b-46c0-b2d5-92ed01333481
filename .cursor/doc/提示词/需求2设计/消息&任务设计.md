# AI生成定时任务或异步消息处理详细设计文档提示词（基于产品需求文档）

你是一名资深后端架构师，请根据我提供的 **产品需求文档**（PRD）内容，生成一份对应的 **定时任务或异步消息处理的详细设计文档**，输出要求如下：
## 📌 输出要求：
- **输出格式**：Markdown（`.md`）
- **文件名**：根据任务功能命名，例如 `定时同步用户积分任务.md`
- **保存路径**：项目根目录下的 `.知识库/模块名/功能名/` 目录中
- **图示工具**：所有流程图与时序图必须使用 **PlantUML** 语法编写，**不要使用 Mermaid**
- **不包含任何代码**，通过**自然语言**描述完整业务逻辑、处理流程、边界场景与判断分支

## 📘 定时任务 / 异步消息处理详细设计文档结构：
请完整输出以下内容：
### 1. 任务名称  
任务的功能名称（中文）
### 2. 任务说明  
任务的作用、业务背景、触发场景说明，如定时批处理、异步通知、事件驱动等
### 3. 消息来源  
说明消息由谁发出、如何触发，包括以下内容：
- **触发方式**（定时调度 / 上游系统异步发送）
- **MQ Topic 名称**
- **消息 Tag（如有）**
- **MQ 消息消费方式（Push / Pull / 广播 / 顺序）**
### 4. 消息格式说明  
以表格形式列出 JSON 消息的字段结构：
| 中文名 | 字段名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
### 5. 核心业务处理逻辑  
根据 PRD 详细描述的任务逻辑，逐步说明业务流程、判断分支、特殊场景处理，示例如下：
- 任务启动后从 RocketMQ 接收到一条消息
- 校验消息体字段完整性与有效性（如用户ID不能为空，时间范围合理等）
- 根据业务规则进行数据过滤与处理（例如只处理有效状态用户）
- 若数据处理成功，则记录操作日志并写入后续系统（如ES、DB、缓存等）
- 若处理失败，进行异常捕获并将失败记录写入重试队列或告警系统
### 6. 流程图（使用 PlantUML）  
使用 PlantUML 绘制处理流程图  
```plantuml
@startuml
...
@enduml
```
### 7. 时序图（使用 PlantUML）  
展示消息接收、处理、调用外部系统的时序过程  
```plantuml
@startuml
...
@enduml
```
### 8. 异常处理机制  
- 消息字段缺失或不合法：记录错误日志并丢弃或重试
- 消息幂等性问题：通过唯一标识判断是否重复消费
- 下游系统不可用：支持熔断/重试机制，并告警
- 消息处理失败：支持配置最大重试次数或转入死信队列（DLQ）
### 9. 调用的公共模块或外部依赖  
列出所有依赖的公共组件、服务、数据库、缓存等：
| 模块名称 | 功能简述 |
|----------|----------|
| 用户中心 | 获取用户基础信息 |
| 日志服务 | 记录任务执行日志 |
| 告警系统 | 任务失败时发送预警 |
✏️ 注意事项：
- 图示必须使用 **PlantUML**
- Markdown 文档结构需清晰规范，适合归档和评审
- 所有字段与业务逻辑必须根据产品需求准确还原
- 文档必须具有 **可指导开发实现** 的完整性
- **严禁出现任何代码、类名、方法名、注解等内容**
- 必须要生成md格式文件
