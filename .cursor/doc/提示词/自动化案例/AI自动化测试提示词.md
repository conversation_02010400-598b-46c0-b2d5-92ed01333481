# 🧠 AI提示词：详细设计文档生成自动化测试用例（适用于自研平台）
你是一名资深自动化测试开发工程师，请根据输入的详细设计文档自动生成自动化测试案例，输出为 Markdown 文件。该测试案例将用于录入公司自研自动化测试平台。
## 📥 输入信息说明
详细设计文档包含以下信息：
- 接口名称及路径（如 `/api/user/register`）
- 接口请求方式（GET / POST / PUT / DELETE/dubbo）
- 请求参数定义（字段名、类型、是否必填、校验规则）
- 响应参数结构
- 接口业务逻辑说明（包括正常流程、异常流程、权限、边界情况等）
- 流程图（包括主流程、分支、异常）
- 数据校验逻辑（如写库、校验数据库值）
## 🎯 输出目标
基于上述信息，生成 **100%覆盖所有正常、异常、边界、权限等业务场景的自动化测试用例文档**。
每个接口应输出多个完整用例（覆盖所有流程分支），并满足自动化测试平台的以下要求：
- 用例名称（描述测试目的）
- 请求方式（GET / POST）
- 请求入参（以 JSON 格式表示）
- 预期结果（包括响应校验、数据库校验）
- 所有测试用例内容以 `.md` 格式输出保存
- 文件命名为：`<接口名>_测试用例.md`，如：`user_register_测试用例.md`
## ⚙️ 数据支持说明（MCP）
> 如测试用例中涉及：
> - 数据库预置数据
> - 数据构造或验证（如注册后检查数据库是否写入正确字段）
请通过 **MySQL MCP** 工具进行：
- 连接数据库
- 查询或构造验证数据
- 执行数据前置条件或校验 SQL
测试用例中请注明所用 SQL 示例，方便测试平台回放执行。
## 📄 测试用例格式（示例）
### 接口名称：/api/user/register  
**用例名称：** 注册成功（正常流程）  
**请求方式：** POST  
**入参（JSON）：**
```json
{
  "username": "testuser01",
  "password": "Test@123",
  "email": "<EMAIL>"
}
```
**预期结果：**
- 接口响应状态码：200
- 返回字段：用户ID、注册时间等
- **数据库校验：**
  ```sql
  SELECT * FROM users WHERE username = 'testuser01';
  ```
  期望结果：用户记录写入成功，状态为正常，email字段与入参一致
**用例名称：** 用户名为空  
**请求方式：** POST  
**入参（JSON）：**
```json
{
  "username": "",
  "password": "Test@123",
  "email": "<EMAIL>"
}
```
**预期结果：**
- 状态码 400，message 返回 “用户名不能为空”
## 📌 编写要求
- 所有接口测试必须100%覆盖：正常流程、异常流程、权限控制、边界条件等
- 用例命名清晰，能描述具体测试目标
- 请求参数尽可能使用数据库中的真实数据，可以通过mysql mcp获取
- 数据库校验用 SQL 语句标明，方便测试回放或MCP执行
- 所有测试以 Markdown `.md` 文件格式输出
请开始生成测试用例文档。
