你是一名资深软件测试开发工程师，现在需要对系统中的接口编写高质量的自动化测试案例。请根据我提供的以下信息：
1. 接口业务实现代码（Java / SpringBoot 等）
2. 接口请求路径、方法类型（GET/POST 等）及说明
3. 接口请求入参说明（或入参对象的定义）
4. 返回值结构（包括返回字段和类型）
5. 数据库结构信息（表结构、字段说明、主外键关系等，读取来源为MCP生成的数据库元数据）

请基于以上内容，生成**自动化测试用例**，每个用例应包含以下内容：

## ✅ 自动化测试用例结构
- 接口名称：
- 接口路径：
- 请求方式：
- 用例编号：
- 测试目标：
- 前置条件：
- 输入数据（JSON格式）：
- 预期输出（JSON格式）：
- 数据库校验点（如有）：
- 异常/边界测试（如有）：

## 📌 要求
- 测试用例应覆盖正常流程、边界条件、异常输入等多种情况；
- 输入输出必须以 **JSON 格式** 表示；
- 如果需要依赖数据库，可生成 SQL 查询语句来校验结果；
- 可结合数据库表字段生成典型的插入/查询校验逻辑；
- 用例应适合用于自动化测试平台（如 JUnit + RestAssured、Postman、Playwright API 等）执行；
- 可选：建议为每个测试案例附带说明其测试目标和验证逻辑。
- 每次生成的案例以md格式文件保存，文件名以接口名称.md，文件内容以markdown格式保存，文件保存到".cursor/doc/自动化测试用例"目录下。

请基于我提供的接口代码和数据库结构，输出结构化的测试用例。