你是一名资深后端架构师，请根据我提供的 Java 功能代码（可能包含 Service、Controller、Repository、Facade 等），反向生成一份功能详细设计文档，输出要求如下：

📌 输出要求：
- 输出格式为 Markdown（.md）
- 文件名为当前功能模块名称，例如 用户注册功能设计.md
- 保存路径为项目根目录下的 .知识库/文档/业务模块/ 目录
- 图示必须使用 PlantUML 语法（不要使用 Mermaid）

📘 功能详细设计文档应包含以下内容结构（请完整输出）：

## 1. 功能名称
功能的中文名称和英文标识

## 2. 功能概述
- 功能用途和业务背景
- 解决的问题和价值
- 适用场景和边界

## 3. 功能架构
### 3.1 分层架构图（使用 PlantUML）
```plantuml
@startuml
!define RECTANGLE class
RECTANGLE "Controller Layer" as CL
RECTANGLE "Service Layer" as SL  
RECTANGLE "Repository Layer" as RL
RECTANGLE "Database" as DB
CL --> SL
SL --> RL
RL --> DB
@enduml
```

### 3.2 核心类关系图（使用 PlantUML）
展示主要类之间的依赖和继承关系

## 4. 接口清单
### 4.1 HTTP 接口
| 接口名称 | 请求方式 | URL | 功能说明 |
|---------|----------|-----|----------|
| | | | |

### 4.2 Dubbo 接口
| 接口名称 | 方法签名 | 功能说明 |
|---------|----------|----------|
| | | |

## 5. 数据模型
### 5.1 请求参数模型
| 参数名称 | 英文名 | 类型 | 必填 | 示例值 | 说明 |
|---------|--------|------|------|--------|------|
| | | | | | |

### 5.2 响应参数模型
| 参数名称 | 英文名 | 类型 | 说明 |
|---------|--------|------|------|
| | | | |

### 5.3 核心业务对象
| 对象名称 | 类名 | 主要属性 | 说明 |
|---------|------|----------|------|
| | | | |

## 6. 业务流程设计
### 6.1 主要业务流程（使用 PlantUML）
```plantuml
@startuml
start
:接收请求;
:参数验证;
if (参数是否有效?) then (是)
  :执行业务逻辑;
  :返回成功结果;
else (否)
  :返回参数错误;
endif
stop
@enduml
```

### 6.2 异常流程处理
描述各种异常情况的处理逻辑

## 7. 时序图设计（使用 PlantUML）
```plantuml
@startuml
participant "前端" as FE
participant "Controller" as C
participant "Service" as S
participant "Repository" as R
participant "Database" as DB

FE -> C: 发起请求
C -> S: 调用业务方法
S -> R: 查询/更新数据
R -> DB: 执行SQL
DB -> R: 返回结果
R -> S: 返回数据
S -> C: 返回业务结果
C -> FE: 返回响应
@enduml
```

## 8. 核心业务逻辑说明
### 8.1 主要算法逻辑
详细描述核心业务逻辑、计算规则、判断条件

### 8.2 状态流转规则
如果涉及状态变化，说明状态流转的规则和条件

### 8.3 数据处理规则
数据的转换、校验、过滤规则

## 9. 数据库设计
### 9.1 相关数据表
| 表名 | 中文名 | 主要字段 | 说明 |
|------|--------|----------|------|
| | | | |

### 9.2 表关系图（使用 PlantUML）
```plantuml
@startuml
entity "表1" as T1 {
  * id : varchar(32)
  --
  field1 : varchar(50)
  field2 : int
}

entity "表2" as T2 {
  * id : varchar(32)
  --
  table1_id : varchar(32)
  field3 : varchar(100)
}

T1 ||--o{ T2 : contains
@enduml
```

## 10. 性能设计
### 10.1 缓存策略
- 缓存的数据类型
- 缓存时间设置
- 缓存更新策略

### 10.2 性能指标
- 响应时间要求
- 并发量支持
- 资源消耗评估

## 11. 安全设计
### 11.1 权限控制
- 接口鉴权方式
- 数据权限控制
- 操作权限验证

### 11.2 数据安全
- 敏感数据处理
- 输入参数校验
- SQL注入防护

### 11.3 幂等性设计
- 幂等性要求
- 实现机制
- 防重复提交

## 12. 异常处理设计
### 12.1 异常分类
| 异常类型 | 错误码 | 错误描述 | 处理方式 |
|---------|--------|----------|----------|
| | | | |

### 12.2 日志记录
- 关键节点日志
- 异常日志格式
- 审计日志要求

## 13. 外部依赖
### 13.1 依赖的服务
| 服务名称 | 调用方式 | 功能说明 | 异常处理 |
|---------|----------|----------|----------|
| | | | |

### 13.2 依赖的基础设施
- 数据库依赖
- 缓存依赖
- 消息队列依赖

## 14. 测试设计
### 14.1 测试用例设计
- 正常流程测试
- 异常流程测试
- 边界条件测试

### 14.2 性能测试
- 压力测试指标
- 负载测试场景

✏️ 注意事项：
- 所有图示必须使用 PlantUML 标准语法
- Markdown 文档结构清晰，适合归档和设计评审使用
- 内容要全面覆盖功能的各个方面
- 技术细节要准确，业务逻辑要清晰
- 文档要具备可维护性和可扩展性