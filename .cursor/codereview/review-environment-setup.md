# 多企业微信审查环境设置

## 项目结构扫描结果

### 总体统计
- 项目采用多模块Maven结构
- 主要模块：crm-wechat-client, crm-wechat-service, crm-wechat-dao
- 审查重点：companyNo参数支持和默认值处理

## 文件分类清单

### 1. 控制器层 (Controllers)
**位置**: `crm-wechat-service/src/main/java/com/howbuy/crm/wechat/service/controller/`

- WechatCallbackController.java
- WechatDeptController.java  
- WechatGroupUserController.java
- SendWechatController.java
- WechatTransferEventDealController.java
- WechatExternalUserController.java
- WechatGroupController.java
- WechatCustRelationController.java

**审查重点**: 
- REST端点是否接受companyNo参数
- 默认值处理（companyNo=1）
- 参数验证逻辑

### 2. 服务层 (Services)

#### 2.1 Dubbo服务接口 (Client模块)
**位置**: `crm-wechat-client/src/main/java/com/howbuy/crm/wechat/client/producer/`

- CmWechatGroupQueryService.java
- QueryWechatUserRelationService.java  
- WechatUserTagService.java
- QueryWeChatMemberInfoService.java

#### 2.2 服务实现 (Service模块)
**位置**: `crm-wechat-service/src/main/java/com/howbuy/crm/wechat/service/exposeimpl/`

- WechatUserTagServiceImpl.java
- QueryWechatUserRelationServiceImpl.java
- QueryWeChatMemberInfoServiceImpl.java

#### 2.3 业务服务
**位置**: `crm-wechat-service/src/main/java/com/howbuy/crm/wechat/service/service/`

- WechatFullDeptDataScheduleService.java
- 消息相关服务 (message包下)

**审查重点**:
- Dubbo接口方法签名中的companyNo参数
- 服务实现中的参数传递
- 默认值处理逻辑

### 3. 仓储层 (Repositories & Mappers)

#### 3.1 Repository类
**位置**: `crm-wechat-service/src/main/java/com/howbuy/crm/wechat/service/repository/`

- CmWechatGroupRepository.java
- CmWechatGroupUserRepository.java
- CmWechatExternalInfoRepository.java
- CmWechatRefreshConscustRepository.java
- CommonRepository.java
- CmWechatCorpConfigRepository.java
- CmWechatVoiceRemindRepository.java
- CmWechatDeptRepository.java
- CmWechatEmpRepository.java
- CmWechatCustInfoRepository.java
- CmWechatCustRelationRepository.java

#### 3.2 MyBatis Mapper
**位置**: `crm-wechat-dao/src/main/java/com/howbuy/crm/wechat/dao/mapper/`

- MessageAcceptInfoMapper.java
- MessageSendInfoMapper.java
- 其他mapper文件

**审查重点**:
- SQL查询中的companyNo WHERE条件
- 默认值处理
- 数据隔离验证

### 4. 定时任务 (Scheduled Jobs)
**位置**: `crm-wechat-service/src/main/java/com/howbuy/crm/wechat/service/job/`

- SyncCustHboneRelationJob.java
- CrmAcceptMessageJob.java
- AbstractCommonMessageJob.java
- CrmUpdateMessageSendResultJob.java
- TestJob.java
- SyncChatEmpJob.java
- AbstractBatchMessageJob.java
- SyncChatGroupUserJob.java
- CrmBuildMessageJob.java
- CrmSendMessageJob.java
- SyncChatGroupJob.java

**审查重点**:
- 多企业数据处理逻辑
- 企业特定配置使用
- 批处理中的数据隔离

### 5. 消息相关 (Message System)
**位置**: `crm-wechat-service/src/main/java/com/howbuy/crm/wechat/service/service/message/`

#### 5.1 消息构建服务
- MessageBuildFactory.java
- SmsBuilderService.java
- EmailWithAttachmentBuilderService.java
- RebotBuilderService.java
- NewApplicationBuilderService.java
- AbstractMessageBuildService.java
- MarketReportingBuildService.java
- EmailBuilderService.java
- MessageBuildService.java

#### 5.2 消息发送服务
- MessageSendFactory.java
- MessageSendInfoService.java
- MessageAcceptInfoService.java

#### 5.3 消息相关Job
- CrmAcceptMessageJob.java
- CrmBuildMessageJob.java
- CrmSendMessageJob.java
- CrmUpdateMessageSendResultJob.java

**审查重点**:
- 验证消息功能保持单企业行为
- 确保没有意外的多企业修改
- 向后兼容性检查

## 审查模式和搜索标准

### 1. CompanyNo参数模式
```java
// 查找模式
- 方法参数: "companyNo", "company_no"
- 默认值: "companyNo = 1", "defaultValue = \"1\""
- 注解: "@RequestParam", "@PathVariable"
```

### 2. 默认值处理模式
```java
// 期望的默认值处理
@RequestParam(value = "companyNo", defaultValue = "1") Integer companyNo
// 或
if (companyNo == null) {
    companyNo = 1;
}
```

### 3. SQL查询模式
```sql
-- 期望的WHERE条件
WHERE company_no = #{companyNo}
-- 或
AND company_no = #{companyNo}
```

### 4. 排除模式
- 测试文件: `*Test.java`, `*Tests.java`
- 配置文件: `*Config.java` (部分)
- 枚举文件: `*Enum.java` (部分)
- VO/DTO文件: 需要检查但优先级较低

## 下一步行动
1. 开始逐层审查，从控制器层开始
2. 使用搜索模式识别潜在问题
3. 记录发现的问题和不一致性
4. 生成详细的审查报告
## 审查
标准详细定义

### CompanyNo枚举值
根据CompanyNoEnum.java的定义：
- "1" = 好买财富 (HOWBUY_WEALTH) - **默认值**
- "2" = 好买基金 (HOWBUY_FUND)  
- "3" = 好晓买 (HOWBUY_HXM)

### 关键审查模式

#### 1. Dubbo接口方法签名检查
```java
// 期望模式 - 应该包含CompanyNoEnum参数
public Response<SomeVO> someMethod(CompanyNoEnum companyNoEnum, OtherParams...);

// 或者使用String类型的companyNo
public Response<SomeVO> someMethod(String companyNo, OtherParams...);

// 需要检查的问题：
// - 缺少companyNo/CompanyNoEnum参数
// - 参数位置不一致
// - 默认值处理逻辑
```

#### 2. 控制器端点检查
```java
// 期望模式 - REST接口应支持companyNo参数
@RequestMapping("/some-endpoint")
public Response someEndpoint(
    @RequestParam(value = "companyNo", defaultValue = "1") String companyNo,
    // 其他参数...
) {
    // 实现逻辑
}

// 需要检查的问题：
// - 缺少companyNo参数支持
// - 默认值不是"1"
// - 参数验证逻辑缺失
```

#### 3. 服务层参数传递检查
```java
// 期望模式 - 服务方法应传递companyNo
public void someServiceMethod(String companyNo, OtherParams...) {
    // 调用下层服务时传递companyNo
    repository.someMethod(companyNo, ...);
}

// 需要检查的问题：
// - 参数传递链断裂
// - 硬编码companyNo值
// - 缺少企业隔离逻辑
```

#### 4. 数据库查询检查
```sql
-- 期望模式 - SQL应包含companyNo过滤
SELECT * FROM some_table 
WHERE company_no = #{companyNo}
  AND other_conditions...

-- 需要检查的问题：
-- - 缺少company_no WHERE条件
-- - 硬编码company_no值
-- - 数据隔离不完整
```

#### 5. 定时任务多企业处理检查
```java
// 期望模式 - 定时任务应处理所有企业或接受companyNo参数
@Scheduled(...)
public void someScheduledTask() {
    // 方式1：遍历所有企业
    for (CompanyNoEnum company : CompanyNoEnum.values()) {
        processForCompany(company);
    }
    
    // 方式2：接受companyNo参数
    // processForCompany(companyNo);
}

// 需要检查的问题：
// - 只处理单一企业（通常是好买财富）
// - 缺少企业循环逻辑
// - 数据混合处理
```

#### 6. 消息系统保持不变检查
```java
// 期望模式 - 消息相关代码应保持原样
// 不应该添加companyNo参数或多企业逻辑

// 需要检查的问题：
// - 意外添加了companyNo参数
// - 修改了消息发送逻辑
// - 破坏了向后兼容性
```

### 优先级分类

#### 严重 (Critical)
- Dubbo接口缺少companyNo参数
- 数据库查询缺少company_no过滤导致数据泄露
- 定时任务只处理单一企业但应该处理多企业

#### 高 (High)  
- 控制器端点缺少companyNo参数支持
- 服务层参数传递链断裂
- 默认值不是"1"影响向后兼容性

#### 中 (Medium)
- 参数验证逻辑缺失
- 日志记录不包含企业信息
- 配置处理不支持多企业

#### 低 (Low)
- 代码注释未更新
- 变量命名不一致
- 非关键路径的小问题

### 排除检查的文件类型
- 测试文件：`*Test.java`, `*Tests.java`
- 纯VO/DTO类：只做数据传输，不包含业务逻辑
- 枚举类：`*Enum.java` (除非包含业务逻辑)
- 配置类：`*Config.java` (除非涉及企业特定配置)
- 工具类：`*Util.java`, `*Utils.java` (除非涉及企业逻辑)

## 审查环境设置完成

✅ **任务1完成状态**:
- [x] 扫描项目结构，识别所有需要审查的Java源文件
- [x] 建立审查模式和搜索标准（companyNo参数、默认值等）  
- [x] 创建文件分类清单（控制器、服务、仓储、定时任务、消息相关）
- [x] 定义详细的审查标准和优先级分类
- [x] 确认CompanyNoEnum枚举结构和默认值逻辑

**发现的关键信息**:
1. 项目使用CompanyNoEnum枚举管理企业编码
2. 默认企业是"1"（好买财富）
3. 支持3个企业：好买财富(1)、好买基金(2)、好晓买(3)
4. 多企业重构已完成，需要验证实现的完整性
5. 消息发送功能应保持单企业行为

**下一步**: 可以开始执行任务2 - 控制器层审查