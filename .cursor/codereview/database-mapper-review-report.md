# 数据库操作和Mapper审查报告

## 执行摘要

本报告对CRM微信系统的MyBatis Mapper接口和XML映射文件进行了全面审查，重点检查多企业支持的companyNo过滤实现。审查发现了多个严重的数据隔离问题，需要立即修复以确保企业间数据安全。

### 审查统计
- **审查的Mapper接口**: 15个主要Mapper + 5个自定义Mapper
- **审查的XML映射文件**: 20个
- **发现的问题**: 8个严重问题，3个中等问题
- **符合要求的Mapper**: 12个

## 详细发现

### 🔴 严重问题 (需要立即修复)

#### 1. CmWechatGroupMapper - 缺少企业过滤
**文件**: `CmWechatGroupMapper.xml`
**问题**: `getByChatId`方法缺少companyNo过滤
```xml
<!-- 问题代码 -->
<select id="getByChatId" resultType="com.howbuy.crm.wechat.dao.po.CmWechatGroupPO">
    select * from cm_wechat_group where chat_id = #{chatId}
</select>
```
**风险**: 可能返回其他企业的群组信息，造成数据泄露
**建议修复**:
```xml
<select id="getByChatId" resultType="com.howbuy.crm.wechat.dao.po.CmWechatGroupPO">
    select * from cm_wechat_group 
    where chat_id = #{chatId} AND company_no = #{companyNo}
</select>
```
**接口修改**: 需要在接口方法中添加companyNo参数

#### 2. CmWechatGroupMapper - updateByChatId缺少企业过滤
**文件**: `CmWechatGroupMapper.xml`
**问题**: `updateByChatId`方法只通过chatId更新，缺少companyNo过滤
```xml
<!-- 问题代码 -->
where chat_id = #{chatId}
```
**风险**: 可能更新其他企业的群组数据
**建议修复**: 在WHERE子句中添加`AND company_no = #{companyNo}`

#### 3. CmWechatGroupMapper - deleteByChatId缺少企业过滤
**文件**: `CmWechatGroupMapper.xml`
**问题**: `deleteByChatId`方法缺少companyNo过滤
```xml
<!-- 问题代码 -->
update cm_wechat_group set chat_flag = '1',UPDATE_TIME = #{date,jdbcType=TIMESTAMP} 
where chat_id = #{chatId}
```
**风险**: 可能删除其他企业的群组数据
**建议修复**: 在WHERE子句中添加`AND company_no = #{companyNo}`

#### 4. CmWechatGroupUserMapper - 多个方法缺少企业过滤
**文件**: `CmWechatGroupUserMapper.xml`
**问题**: 以下方法缺少companyNo过滤：
- `listByChatId`
- `updateByChatIdAndUserId`
- `batchDelete`
- `deleteByChatId`
- `selectByChatIdAndUserId`
- `listByChatIdEffective`

**风险**: 可能操作其他企业的群组用户数据
**建议修复**: 所有涉及chat_id的查询都应该添加companyNo过滤

#### 5. CmWechatGroupUserMapper - 跨表查询缺少企业过滤
**文件**: `CmWechatGroupUserMapper.xml`
**问题**: `selectNormalByUserId`和`selectInfoByUserId`方法的跨表查询中缺少companyNo关联
```xml
<!-- 问题代码 -->
from  cm_wechat_group a
left join CM_WECHAT_GROUP_USER b on a.CHAT_ID = b.CHAT_ID
where b.EXTERNAL_USER_ID = #{userId,jdbcType=VARCHAR}
```
**风险**: 可能返回跨企业的群组和用户关联数据
**建议修复**: 添加企业过滤条件：
```xml
from  cm_wechat_group a
left join CM_WECHAT_GROUP_USER b on a.CHAT_ID = b.CHAT_ID AND a.COMPANY_NO = b.COMPANY_NO
where b.EXTERNAL_USER_ID = #{userId,jdbcType=VARCHAR}
AND a.COMPANY_NO = #{companyNo}
```

### 🟡 中等问题

#### 6. 基础CRUD操作缺少企业过滤
**文件**: 多个Mapper的基础操作
**问题**: `selectByPrimaryKey`, `deleteByPrimaryKey`, `updateByPrimaryKey`等基础操作缺少companyNo过滤
**风险**: 通过主键可能访问到其他企业的数据
**建议**: 考虑在业务层面控制，或者重写这些方法添加companyNo参数

#### 7. CmWechatCustInfoMapper - 硬编码企业值
**文件**: `CmWechatCustInfoMapper.xml`
**问题**: `countUserGroupList`和`selectExternalUserIds`方法中存在硬编码的企业值
```xml
<!-- 问题代码 -->
where l.del_flag = '0' and l.company_no = '1'
```
**风险**: 硬编码限制了多企业功能的灵活性
**建议修复**: 使用参数化的companyNo值

### ✅ 正确实现的Mapper

以下Mapper已正确实现多企业支持：

1. **CmWechatCustInfoMapper** - 大部分方法正确实现
   - `selectUserGroupList` ✅
   - `getExternalUserIDByHboneNo` ✅
   - `updateHbOneNoByUnionId` ✅

2. **CmWechatCustRelationMapper** - 完全正确
   - 所有自定义方法都包含companyNo参数和过滤 ✅

3. **CmWechatEmpMapper** - 完全正确
   - `getDeptInfoByEmpId` ✅
   - `selectEmpIdByDeptList` ✅

4. **CustomizeCmWechatCustInfoMapper** - 完全正确
   - 所有方法都正确实现企业过滤 ✅

5. **消息相关Mapper** - 符合要求
   - `MessageAcceptInfoMapper` ✅ (按需求保持单企业)
   - `MessageSendInfoMapper` ✅ (按需求保持单企业)

## 数据隔离验证结果

### 通过验证的表
- `cm_wechat_cust_info` - 正确使用COMPANY_NO过滤
- `cm_wechat_cust_relation` - 正确使用COMPANY_NO过滤
- `cm_wechat_emp` - 正确使用COMPANY_NO过滤

### 存在问题的表
- `cm_wechat_group` - 部分查询缺少COMPANY_NO过滤
- `cm_wechat_group_user` - 多个查询缺少COMPANY_NO过滤

## 建议的修复优先级

### 高优先级 (立即修复)
1. 修复`CmWechatGroupMapper.getByChatId`方法
2. 修复`CmWechatGroupUserMapper`中所有缺少企业过滤的方法
3. 修复跨表查询中的企业关联问题

### 中优先级 (近期修复)
1. 移除硬编码的企业值
2. 考虑重构基础CRUD操作

### 低优先级 (长期优化)
1. 统一Mapper接口的参数命名规范
2. 添加更多的数据验证逻辑

## 测试建议

1. **单元测试**: 为每个修复的方法编写单元测试，验证企业数据隔离
2. **集成测试**: 测试跨表查询的企业过滤逻辑
3. **数据验证**: 在测试环境中验证不同企业数据不会相互访问

## 总结

审查发现了多个严重的数据隔离问题，主要集中在群组相关的Mapper中。这些问题如果不及时修复，可能导致企业间数据泄露。建议立即修复高优先级问题，并建立相应的测试机制确保修复的有效性。

大部分客户信息和员工相关的Mapper已经正确实现了多企业支持，消息系统按照需求保持了单企业行为。