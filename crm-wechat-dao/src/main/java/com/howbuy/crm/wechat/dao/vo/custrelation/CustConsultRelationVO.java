package com.howbuy.crm.wechat.dao.vo.custrelation;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description:(客户 vs 投顾 企业微信关联  查询条件VO)
 * @param
 * @return 
 * @author: haoran.zhang
 * @date: 2023/11/27 9:37
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class CustConsultRelationVO  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户信息-一账通号
     */
    private String hboneNo;

    /**
     * 投顾号
     */
    private String conscode;

    /**
     * companyNo	企业编码
     * 不支持 List中 包含多companyNo 查询。 所以此处不支持 comgpanyNo .
     */
//    private String companyNo;

}