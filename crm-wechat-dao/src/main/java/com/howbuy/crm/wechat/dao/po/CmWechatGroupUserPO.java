/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.dao.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @description: 企微客户群成员表Po类
 * <AUTHOR>
 * @date 2023/10/26 14:11
 * @since JDK 1.8
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmWechatGroupUserPO {

    /**
     * 群id
     */
    private String chatId;
    /**
     * 群成员id
     */
    private String externalUserId;
    private String type;
    /**
     * 群成员在企业微信的唯一标识
     */
    private String unionId;
    /**
     * 群成员加入方式
     */
    private String joinScene;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 群成员加入时间
     */
    private Date joinTime;
    /**
     * 群成员在群里的昵称
     */
    private String groupNickName;
    /**
     * 群成员名字
     */
    private String name;
    /**
     * 群成员是否删除 0在群 1退群
     */
    private String userChatFlag;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 企业编号 1-好买财富 2-好买基金
     */
    private String companyNo;
    /**
     * 邀请人
     */
    private String invitor;

}