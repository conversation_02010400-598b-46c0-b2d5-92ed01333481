package com.howbuy.crm.wechat.dao.vo.message;

import com.howbuy.crm.wechat.dao.vo.PageVo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (消息接收表-查询对象)
 * <AUTHOR>
 * @date 2023/10/7 9:59
 * @since JDK 1.8
 */
@Data
public class MessageAcceptInfoVO  extends PageVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
    * 主键ID
    */
    private Long id;


    /**
     * 业务唯一标识
     */
    private String uniqueId;


    /**
    * 消息类型：1-营销喜报
    */
    private List<String> messageTypeList;



    /**
    * 消息状态：0-未处理；1-已处理；2-处理中
    */
    private List<String> messageStatusList;

}