package com.howbuy.crm.wechat.dao.po;

import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Objects;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.util.DigestUtils;

/**
 * @description: crm-wechat 
 * @author: yu.zhang 
 * @date: 2023/6/28 10:03 
 * @since JDK 1.8
 * @version: 1.0 
 */

/**
 * 企业微信员工表
 */
@Getter
@Setter
@ToString
public class CmWechatEmpPO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 员工的userId
     */
    private String empId;

    /**
     * 员工姓名
     */
    private String empName;

    /**
     * 员工所属主部门id
     */
    private Integer deptId;
    /**
     * 员工工号
     */
    private String empWorkId;
    /**
     * 头像缩略图url
     */
    private String thumbAvatar;
    /**
     * 头像url
     */
    private String avatar;
    /**
     * 员工个人二维码
     */
    private String qrCode;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 企业编码1好买财富2好买基金
     */
    private String companyNo;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 删除标志（0 正常；1 删除）
     */
    private String delFlag;

    /**
     * 删除时间
     */
    private Date delTime;

    private static final long serialVersionUID = 1L;


    /**
     * 通过MD5比较数据是否一致（比较字段empName、deptId、empWorkId、thumbAvatar、avatar、qrCode、email）
     * @param p
     * @return
     */
    public Boolean checkDataIsEqualsByMd5(CmWechatEmpPO p){
        String currrentData = this.empName + this.deptId + this.empWorkId + this.thumbAvatar + this.avatar + this.qrCode + this.email;
        String compareData = p.getEmpName() + p.getDeptId() + p.getEmpWorkId() + p.getThumbAvatar() + p.getAvatar() + p.getQrCode() + p.getEmail();
        String curData = DigestUtils.md5DigestAsHex(currrentData.getBytes(StandardCharsets.UTF_8));
        String compData = DigestUtils.md5DigestAsHex(compareData.getBytes(StandardCharsets.UTF_8));
        return Objects.equals(curData, compData);
    }

}