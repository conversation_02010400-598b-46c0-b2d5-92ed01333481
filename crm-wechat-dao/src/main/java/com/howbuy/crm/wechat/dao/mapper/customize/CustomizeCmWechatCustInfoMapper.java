package com.howbuy.crm.wechat.dao.mapper.customize;

import com.howbuy.crm.wechat.dao.bo.UserGroupInfoBO;
import com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO;
import com.howbuy.crm.wechat.dao.po.CmWechatRelationPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: crm-wechat 
 * @author: yu.zhang
 * @date: 2023/6/25 14:15 
 * @since JDK 1.8
 * @version: 1.0
 */
@Mapper
public interface CustomizeCmWechatCustInfoMapper {

    /**
     * @description:根据外部用户ID查询对应客户信息
     * @param externalUserId	
     * @param companyNo  企微-企业主体
     * @return com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO
     * @author: yu.zhang
     * @date: 2023/6/25 15:34
     * @since JDK 1.8
     */
    List<CmWechatCustInfoPO> listWechatCustByExternalUserId(@Param("externalUserId") String externalUserId,
                                                            @Param("companyNo") String companyNo);

    /**
     * @description:(请在此添加描述)
     * @param companyNo  企微-企业主体
     * @param hboneNo	
     * @return com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO
     * @author: yu.zhang
     * @date: 2023/6/25 15:53
     * @since JDK 1.8
     */
    CmWechatCustInfoPO getExternalUserByHboneNo(@Param("companyNo") String companyNo,
                                                @Param("hboneNo") String hboneNo);


    /**
     * @description:(根据企业IC和香港一账通查询客户信息)
     * @param companyNo  企微-企业主体
     * @param hkHoneNo
     * @return com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO
     * @author: haoran.zhang
     * @date: 2025/8/5 19:12
     * @since JDK 1.8
     */
    CmWechatCustInfoPO getExternalUserByHkHboneNo(@Param("companyNo")String companyNo,
                                                  @Param("hkHoneNo")String hkHoneNo);
    /**
     * @description:根据外部联系人id查询微信客户信息
     * @param externalUserId
     * @param companyNo  企微-企业主体
     * @return com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO
     * <AUTHOR>
     * @date 2024/9/10 13:21
     * @since JDK 1.8
     */
    CmWechatCustInfoPO getWechatCustByExternalUserId(@Param("externalUserId") String externalUserId,
                                                     @Param("companyNo") String companyNo);


    /**
     * @description:(查询添加相关信息内容)
     * @param companyNo  企微-企业主体
     * @param hboneNo
     * @param consCodeList
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatRelationPO>
     * @author: xufanchao
     * @date: 2025/6/10 18:06
     * @since JDK 1.8
     */
    List<CmWechatRelationPO> queryWechatAddRelationList(@Param("companyNo") String companyNo,
                                                        @Param("hboneNo") String hboneNo,
                                                        @Param("consCodeList") List<String> consCodeList);

    /**
     * @param companyNo  企微-企业主体
     * @param externalUserIds
     * @return
     * @description 查询指定部门下的外部客户列表，如果客户没有加群，则只返回客户信息
     * <AUTHOR>
     * @date 2024/5/27 3:58 PM
     * @since JDK 1.8
     */
    List<UserGroupInfoBO> selectUserGroupList(@Param("companyNo") String companyNo,
                                              @Param("externalUserIds") List<String> externalUserIds);

    /**
     * @param companyNo  企微-企业主体
     * @param hbOneNo
     * @param wechatNickName
     * @param deptIdList
     * @return
     * @description
     * <AUTHOR>
     * @date 2024/6/17 5:19 PM
     * @since JDK 1.8
     */
    List<String> selectExternalUserIds(@Param("companyNo") String companyNo,
                                       @Param("hbOneNo") String hbOneNo,
                                       @Param("wechatNickName") String wechatNickName,
                                       @Param("deptIdList") List<Integer> deptIdList);

    /**
     * @param companyNo  企微-企业主体
     * @param hbOneNo        一账通号
     * @param wechatNickName 微信昵称
     * @param deptIdList     部门 ID 列表
     * @return
     * @description 查询指定部门下的外部客户总数
     * <AUTHOR>
     * @date 2024/5/27 4:05 PM
     * @since JDK 1.8
     */
    int countUserGroupList(@Param("companyNo") String companyNo,
                           @Param("hbOneNo") String hbOneNo,
                           @Param("wechatNickName") String wechatNickName,
                           @Param("deptIdList") List<Integer> deptIdList);


    /**
     * @description 根据一账通号获取客户企业微信ID
     * @param companyNo  企微-企业主体
     * @param hbOneNo
     * @return
     * <AUTHOR>
     * @date 2024/5/27 4:17 PM
     * @since JDK 1.8
     */
    String getExternalUserIDByHboneNo(@Param("companyNo") String companyNo,
                                      @Param("hbOneNo")String hbOneNo);

    /**
     * @description 更新一帐通(账户中心mq接受时使用)
     * @param companyNo  企微-企业主体
     * @param unionId
     * @param hbOneNo
     * @return
     * <AUTHOR>
     * @date 2024/6/19 3:19 PM
     * @since JDK 1.8
     */
    int updateHbOneNoByUnionId(@Param("companyNo") String companyNo,
                               @Param("unionId") String unionId,
                               @Param("hbOneNo") String hbOneNo);

    /**
     * @description 更新香港一帐通(账户中心mq接受时使用)
     * @param companyNo  企微-企业主体
     * @param unionId
     * @param hbOneNo
     * @return
     * <AUTHOR>
     * @date 2024/6/19 3:19 PM
     * @since JDK 1.8
     */
    int updateHkHbOneNoByUnionId(@Param("companyNo") String companyNo,
                                 @Param("unionId")String unoinId,
                                 @Param("hkHboneNo")String hkHboneNo);
}