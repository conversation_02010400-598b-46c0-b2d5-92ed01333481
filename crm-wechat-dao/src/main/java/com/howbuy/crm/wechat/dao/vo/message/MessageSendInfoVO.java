package com.howbuy.crm.wechat.dao.vo.message;

import com.howbuy.crm.wechat.dao.vo.PageVo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (消息发送表-查询对象)
 * <AUTHOR>
 * @date 2023/10/7 9:59
 * @since JDK 1.8
 */
@Data
public class MessageSendInfoVO extends PageVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 接收消息ID
     */
    private Long acceptId;

    /**
     * 发送日期
     */
    private String sendDt;

    /**
     * 消息发送通道|方式， 1-企微群机器发送
     */
    private List<String> sendChannelList;

    /**
     * 发送状态， 1-未推送；2-推送中；3-推送成功；4-推送失败；5-重新推送
     */
    private List<String> sendStatusList;

    /**
     * 消息发送次数 - 小于
     */
    private Integer sendTimes;


}