package com.howbuy.crm.wechat.dao.mapper.message;

import com.github.pagehelper.Page;
import com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO;
import com.howbuy.crm.wechat.dao.vo.message.MessageSendInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/10/7 13:19
 * @since JDK 1.8
 */
public interface MessageSendInfoMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(MessageSendInfoPO record);


    int batchInsert(List<MessageSendInfoPO> recordList);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(MessageSendInfoPO record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    MessageSendInfoPO selectByPrimaryKey(Long id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(MessageSendInfoPO record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(MessageSendInfoPO record);


    /**
     * 分页查询 消息发送信息
     * @param acceptInfoVO
     * @return
     */
    Page<MessageSendInfoPO> selectPageByVo(MessageSendInfoVO acceptInfoVO);


    /**
     * 更新 发送状态
     * @param id
     * @param sendStatus
     * @param responseCode
     * @param responseMsg
     * @return
     */
    int updateSendStatus(@Param("id") Long id,
                         @Param("sendStatus") String sendStatus,
                         @Param("responseCode") String responseCode,
                         @Param("responseMsg") String responseMsg,
                         @Param("messageUUID") String messageUUID);


    /**
     * @description: 根据接收信息id 查询发送信息
     * @param acceptId 接收信息id
     * @return com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO 发送信息
     * @author: jin.wang03
     * @date: 2024/3/22 14:41
     * @since JDK 1.8
     */
    List<MessageSendInfoPO> selectByAcceptId(@Param("acceptId")Long acceptId);

    /**
     * @description: 更新[发送中]消息的发送状态
     * @param id
     * @param sendStatus
     * @param responseCode
     * @param responseMsg
     * @return int
     * @author: jin.wang03
     * @date: 2024/10/30 17:33
     * @since JDK 1.8
     */
    int updateStatusForPushing(@Param("id") Long id,
                               @Param("sendStatus") String sendStatus,
                               @Param("responseCode") String responseCode,
                               @Param("responseMsg") String responseMsg);

    /**
     * @description: 查询发送状态为[发送中]的记录，并且发送时间在6小时之前
     * @param queryVo
     * @return com.github.pagehelper.Page<com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO>
     * @author: jin.wang03
     * @date: 2025/3/26 16:06
     * @since JDK 1.8
     */
    Page<MessageSendInfoPO> selectPageByVoAndSixHours(MessageSendInfoVO queryVo);
}