/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.dao.mapper;

import com.howbuy.crm.wechat.dao.po.CmWechatGroupPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @description: 企微客户群ID表Mapper接口类
 * <AUTHOR>
 * @date 2023/10/25 17:05
 * @since JDK 1.8
 */
@Mapper
public interface CmWechatGroupMapper {

    /**
     * @description: 根据客户群id查询客户群信息
     * @param chatId 客户群ID
     * @return com.howbuy.crm.wechat.dao.po.CmWechatGroupPo 客户群信息
     * @author: jin.wang03
     * @date: 2023/10/27 11:27
     * @since JDK 1.8
     */
    CmWechatGroupPO getByChatId(@Param("chatId") String chatId, @Param("companyNo") String companyNo);

    /**
     * @description: 保存客户群信息
     * @param cmWechatGroupPo 客户群信息
     * @return int 保存条数
     * @author: jin.wang03
     * @date: 2023/10/27 11:28
     * @since JDK 1.8
     */
    int save(CmWechatGroupPO cmWechatGroupPo);

    /**
     * @description: 更新客户群信息
     * @param cmWechatGroupPo 客户群信息
     * @return int 更新条数
     * @author: jin.wang03
     * @date: 2023/10/27 11:28
     * @since JDK 1.8
     */
    int updateByChatId(CmWechatGroupPO cmWechatGroupPo);

    /**
     * @description: 查询所有客户群id
     * @return java.util.List<CmWechatGroupPo> 客户群信息列表
     * @author: jin.wang03
     * @date: 2023/10/27 11:28
     * @since JDK 1.8
     */
    List<CmWechatGroupPO> listByCompanyNoList(@Param("companyNoList") List<String> companyNoList);

    /**
     * @param chatId 客户群ID
     * @param date
     * @description: 根据客户群id删除客户群信息
     * @author: jin.wang03
     * @date: 2023/10/27 11:29
     * @since JDK 1.8
     */
    int deleteByChatId(@Param("chatId")String chatId,
                       @Param("date") Date date);

    /**
     * @description: 批量删除客户群信息
     * @param bathDeleteChatIdList 客户群id列表
     * @author: jin.wang03
     * @date: 2023/10/27 11:29
     * @since JDK 1.8
     */
    void batchDeleteByChatId(List<String> bathDeleteChatIdList);

    /**
     * @description: 批量插入
     * @param batchInsertList 批量插入的数据
     * @return void
     * @author: jin.wang03
     * @date: 2023/10/27 11:30
     * @since JDK 1.8
     */
    int batchInsert(List<CmWechatGroupPO> batchInsertList);

    /**
     * @description: 根据companyNo和员工id集合 查询所有客户群id
     * @param companyNo	企业编码
     * @return java.util.List<java.lang.String> 客户群id列表
     * @author: jin.wang03
     * @date: 2023/10/30 15:06
     * @since JDK 1.8
     */
    List<String> listChatIdByCompanyNoAndUserIdList(@Param("companyNo") String companyNo,
                                                    @Param("userIdList") List<String> userIdList);

    /**
     * @description: 根据客户群id列表查询客户群信息
     * @param chatIdList 客户群id列表
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatGroupPo> 客户群信息列表
     * @author: jin.wang03
     * @date: 2023/10/31 17:38
     * @since JDK 1.8
     */
    List<CmWechatGroupPO> listByChatIdList(@Param("chatIdList") List<String> chatIdList);

    /**
     * @description:( 根据员工账号查询所有有他的群)
     * @param companyNo	企业编码
     * @param externalUserId 外部客户ID
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatGroupPO>
     * @author: shuai.zhang
     * @date: 2024/3/19 17:59
     * @since JDK 1.8
     */
    List<CmWechatGroupPO> selectContainUserChat(@Param("companyNo") String companyNo,
                                                @Param("externalUserId") String externalUserId);
}
