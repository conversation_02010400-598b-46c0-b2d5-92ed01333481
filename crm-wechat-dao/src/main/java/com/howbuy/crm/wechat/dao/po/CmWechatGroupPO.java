/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.dao.po;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * @description: 企微客户群ID表
 * <AUTHOR>
 * @date 2023/10/25 17:19
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class CmWechatGroupPO {

    /**
     * 主键
     */
    private int id;
    /**
     * 客户群ID
     */
    private String chatId;
    /**
     * 客户群跟进状态。
     * 0 - 跟进人正常
     * 1 - 跟进人离职
     * 2 - 离职继承中
     * 3 - 离职继承完成
     */
    private String status;
    /**
     * 企业编号 1-好买财富 2-好买基金
     */
    private String companyNo;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除 0-否 1-是
     */
    private String chatFlag;
    /**
     * 群名
     */
    private String chatName;
    /**
     * 群主ID
     */
    private String chatOwner;
    /**
     * 群主部门ID
     */
    private int deptId;
    /**
     * 群主部门名称
     */
    private String deptName;
}