package com.howbuy.crm.wechat.dao.mapper.customize;

import com.howbuy.crm.wechat.dao.bo.CmWechatCustRelationBO;
import com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO;
import com.howbuy.crm.wechat.dao.vo.custrelation.CustConsultRelationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: crm-wechat 
 * @author: yu.zhang
 * @date: 2023/6/25 14:15 
 * @since JDK 1.8
 * @version: 1.0
 */
@Mapper
public interface CustomizeCmWechatCustRelationMapper {

    /**
     * @description:根据外部用户ID、投顾，企业ID查询企业微信关系数据
     * @param externalUserId
     * @param conscode
     * @param companyNo
     * @return com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO
     * @author: yu.zhang
     * @date: 2023/6/25 14:22
     * @since JDK 1.8
     */
    CmWechatCustRelationPO selectByExternalIdAndUserId(@Param("externalUserId") String externalUserId,
                                                       @Param("conscode") String conscode,
                                                       @Param("companyNo") String companyNo);

    /**
     * @description: 根据外部用户ID、企业微信用户ID、企业ID查询企业微信关系数据
     * @param externalUserId 外部用户ID
     * @param consCodeList 企业微信用户ID列表
     * @param companyNo 企业ID
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO>
     * @author: hongdong.xie
     * @date: 2024/8/30 18:19
     * @since JDK 1.8
     */
    List<CmWechatCustRelationPO> selectByExternalIdAndUserIdsList(@Param("externalUserId") String externalUserId,
                                                                  @Param("consCodeList") List<String> consCodeList,
                                                                  @Param("companyNo") String companyNo);

    /**
     * @description: 根据外部用户ID列表和投顾号查询企业微信关系
     * @param companyNo  企微-企业主体
     * @param hbOneNo 一账通号
     * @param conscode 投顾号
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO>
     * <AUTHOR>
     * @date 2024/5/23 11:12
     * @since JDK 1.8
     */
    CmWechatCustRelationBO selectByHbOneNoAndConscode(@Param("companyNo") String companyNo,
                                                      @Param("hbOneNo") String hbOneNo,
                                                      @Param("conscode") String conscode);

    /**
     * @description: 批量查询客户与投顾的企业微信关系
     * @param companyNo  企微-企业主体
     * @param voList 批量查询条件列表
     * @return java.util.List<com.howbuy.crm.wechat.dao.bo.CmWechatCustRelationBO>
     * @author: xufanchao
     * @date: 2024/5/23 11:12
     * @since JDK 1.8
     */
    List<CmWechatCustRelationBO> selectByHbOneNoAndConscodeBatch(@Param("companyNo") String companyNo,
                                                                 @Param("voList") List<CustConsultRelationVO> voList);
}