package com.howbuy.crm.wechat.dao.po;

import java.util.Date;

public class CmWechatGroupUserNewPO {
    /**
    * 客户群id
    */
    private String chatid;

    /**
    * 客户ID
    */
    private String userid;

    /**
    * 客户群名称
    */
    private String chatname;

    /**
    * 客户群-群主
    */
    private String chatowner;

    /**
    * 客户类型 1-企业成员 2 - 外部联系人
    */
    private String type;

    /**
    * 外部联系人在微信开放平台的唯一身份标识（微信unionid）
    */
    private String unionid;

    /**
    * 入群方式。1 - 由成员邀请入群（直接邀请入群） 2 - 由成员邀请入群（通过邀请链接入群）3 - 通过扫描群二维码入群
    */
    private String joinScene;

    /**
    * 群主所属部门ID
    */
    private String departmentid;

    /**
    * 群主所属部门名称
    */
    private String departmentname;

    /**
    * 创建时间
    */
    private Date createdt;

    /**
    * 入群时间
    */
    private Date joinTime;
    /**
     * 退群时间
     */
    private Date leaveTime;

    /**
    * 群状态 0 正常 1删除
    */
    private String chatflag;

    /**
    * 在群里的昵称
    */
    private String groupNickname;

    /**
    * 如果是微信用户，则返回其在微信中设置的名字，如果是企业微信联系人，则返回其设置对外展示的别名或实名
    */
    private String name;

    /**
    * 群成员状态 0在群 1退群
    */
    private String userchatflag;


    public Date getLeaveTime() {
        return leaveTime;
    }

    public void setLeaveTime(Date leaveTime) {
        this.leaveTime = leaveTime;
    }

    public String getChatid() {
        return chatid;
    }

    public void setChatid(String chatid) {
        this.chatid = chatid;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getChatname() {
        return chatname;
    }

    public void setChatname(String chatname) {
        this.chatname = chatname;
    }

    public String getChatowner() {
        return chatowner;
    }

    public void setChatowner(String chatowner) {
        this.chatowner = chatowner;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getJoinScene() {
        return joinScene;
    }

    public void setJoinScene(String joinScene) {
        this.joinScene = joinScene;
    }

    public String getDepartmentid() {
        return departmentid;
    }

    public void setDepartmentid(String departmentid) {
        this.departmentid = departmentid;
    }

    public String getDepartmentname() {
        return departmentname;
    }

    public void setDepartmentname(String departmentname) {
        this.departmentname = departmentname;
    }

    public Date getCreatedt() {
        return createdt;
    }

    public void setCreatedt(Date createdt) {
        this.createdt = createdt;
    }

    public Date getJoinTime() {
        return joinTime;
    }

    public void setJoinTime(Date joinTime) {
        this.joinTime = joinTime;
    }

    public String getChatflag() {
        return chatflag;
    }

    public void setChatflag(String chatflag) {
        this.chatflag = chatflag;
    }

    public String getGroupNickname() {
        return groupNickname;
    }

    public void setGroupNickname(String groupNickname) {
        this.groupNickname = groupNickname;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUserchatflag() {
        return userchatflag;
    }

    public void setUserchatflag(String userchatflag) {
        this.userchatflag = userchatflag;
    }
}