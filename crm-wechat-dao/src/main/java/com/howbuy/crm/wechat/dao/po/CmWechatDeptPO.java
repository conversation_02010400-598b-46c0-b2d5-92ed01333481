package com.howbuy.crm.wechat.dao.po;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description: crm-wechat 
 * @author: yu.z<PERSON> 
 * @date: 2023/6/28 10:03 
 * @since JDK 1.8
 * @version: 1.0 
 */

/**
 * 企业微信部门表
 */
@Getter
@Setter
@ToString
public class CmWechatDeptPO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 部门id
     */
    private Integer deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 父级部门id，根部门id为1
     */
    private Integer parentDeptId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 删除标志（0 正常；1 删除）
     */
    private String delFlag;

    /**
     * 删除时间
     */
    private Date delTime;

    /**
     * 企业编码1好买财富2好买基金
     */
    private String companyNo;

    private static final long serialVersionUID = 1L;
}