package com.howbuy.crm.wechat.dao.bo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description: 客户微信投顾关联关系表
 * @author: xufanchao
 * @date: 2024/5/23 11:12
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class CmWechatCustRelationBO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 一账通号
     */
    private String hbOneNo;

    /**
     * 投顾号
     */
    private String consCode;

    /**
     * 国内的外部应用unionId
     */
    private String gnUnionId;

    /**
     * 海外的外部应用unionId
     */
    private String hwUnionId;
}