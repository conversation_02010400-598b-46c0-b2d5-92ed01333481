package com.howbuy.crm.wechat.dao.po;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description: crm-wechat 
 * @author: yu.z<PERSON> 
 * @date: 2023/6/28 10:03 
 * @since JDK 1.8
 * @version: 1.0 
 */

/**
 * 语音拨号后消息记录操作表
 */
@Getter
@Setter
@ToString
public class CmWechatVoiceRemindPO implements Serializable {
    /**
     * 主键,原Voiceid
     */
    private Long id;

    /**
     * 主叫号码
     */
    private String mobile;

    /**
     * 消息接收人
     */
    private String acceptUserId;

    /**
     * 消息事件
     */
    private Date remindTime;

    /**
     * 消息创建时间
     */
    private Date createTime;

    /**
     * 消息类型
     */
    private String remindType;

    private static final long serialVersionUID = 1L;
}