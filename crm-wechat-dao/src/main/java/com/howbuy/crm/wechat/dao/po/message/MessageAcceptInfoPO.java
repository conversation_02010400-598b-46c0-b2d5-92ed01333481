package com.howbuy.crm.wechat.dao.po.message;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: (消息接收表)
 * <AUTHOR>
 * @date 2023/10/7 9:59
 * @since JDK 1.8
 */
@Data
public class MessageAcceptInfoPO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
    * 主键ID
    */
    private Long id;

    /**
     * 业务唯一标识
     */
    private String uniqueId;

    /**
    * 消息类型：1-营销喜报
    */
    private String messageType;

    /**
    * 消息参数，JSON串
    */
    private String messageParams;

    /**
     * 消息模版id
     */
    private String templateId;

    /**
     * 消息模版id 对应的消息参数
     */
    private String templateParams;

    /**
    * 消息状态：0-未处理；1-已处理；2-处理中
    */
    private String messageStatus;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getMessageParams() {
        return messageParams;
    }

    public void setMessageParams(String messageParams) {
        this.messageParams = messageParams;
    }

    public String getMessageStatus() {
        return messageStatus;
    }

    public void setMessageStatus(String messageStatus) {
        this.messageStatus = messageStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}