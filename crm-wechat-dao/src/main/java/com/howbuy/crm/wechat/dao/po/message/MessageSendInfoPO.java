package com.howbuy.crm.wechat.dao.po.message;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: (消息发送表)
 * <AUTHOR>
 * @date 2023/10/7 13:19
 * @since JDK 1.8
 */
@Data
public class MessageSendInfoPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键ID
    */
    private Long id;

    /**
    * 接收消息ID
    */
    private Long acceptId;

    /**
    * 发送日期
    */
    private String sendDt;

    /**
    * 模板ID
    */
    private String templeteId;


    /**
     * 消息发送通道|方式， 1-企微群机器发送
     */
    private String sendChannel;

    /**
    * 模板参数
    */
    private String templeteParams;

    /**
    * 发送状态， 1-未推送；2-推送中；3-推送成功；4-推送失败；5-重新推送
    */
    private String sendStatus;

    /**
    * 消息发送次数
    */
    private Integer sendTimes;

    /**
    * 接口返回码
    */
    private String responseCode;

    /**
    * 接口返回描述
    */
    private String responseMsg;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
     * 该条消息 在下游消息中心中的唯一标识
     */
    private String messageUUID;

}