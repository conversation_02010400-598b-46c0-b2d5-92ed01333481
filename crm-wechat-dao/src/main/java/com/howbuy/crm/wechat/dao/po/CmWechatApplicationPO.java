package com.howbuy.crm.wechat.dao.po;

import java.util.Date;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/7/24 8:34
 * @since JDK 1.8
 */
/**
 * 企微企业应用信息表
 */
public class CmWechatApplicationPO {

    /**
     * id
     */
    private Long id;
    /**
    * 企业应用编码：Eg:wealth_customer
    */
    private String applicationCode;

    /**
    * 企业应用描述：Eg:[好买财富]企业微信-客户联系
    */
    private String applicationDesc;

    /**
    * 应用归属企业编码:1-好买财富 2-好买基金
    */
    private String companyNo;

    /**
     * 企业应用类型 ： customer-客户联系
     */
    private String applicationType;

    /**
    * 应用接口访问凭证,用于获取access_token
    */
    private String accessSecret;

    /**
    * 企业应用id应用的唯一标识，用于接口区分不同应用，或者回调通知中标识通知来源
    */
    private String agentId;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 修改时间
    */
    private Date modifyTime;

    /**
    * 记录状态:1-正常 2-删除
    */
    private String recStat;

    /**
     * 环境编码:dev-开发环境 gray-灾备环境 prod-生产环境
     */
    private String envCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getApplicationCode() {
        return applicationCode;
    }

    public void setApplicationCode(String applicationCode) {
        this.applicationCode = applicationCode;
    }

    public String getApplicationDesc() {
        return applicationDesc;
    }

    public void setApplicationDesc(String applicationDesc) {
        this.applicationDesc = applicationDesc;
    }

    public String getCompanyNo() {
        return companyNo;
    }

    public void setCompanyNo(String companyNo) {
        this.companyNo = companyNo;
    }

    public String getApplicationType() {
        return applicationType;
    }

    public void setApplicationType(String applicationType) {
        this.applicationType = applicationType;
    }

    public String getAccessSecret() {
        return accessSecret;
    }

    public void setAccessSecret(String accessSecret) {
        this.accessSecret = accessSecret;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRecStat() {
        return recStat;
    }

    public void setRecStat(String recStat) {
        this.recStat = recStat;
    }

    public String getEnvCode() {
        return envCode;
    }

    public void setEnvCode(String envCode) {
        this.envCode = envCode;
    }
}