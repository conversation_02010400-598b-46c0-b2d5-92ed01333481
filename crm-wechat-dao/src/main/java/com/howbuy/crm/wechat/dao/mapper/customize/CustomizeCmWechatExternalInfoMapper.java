package com.howbuy.crm.wechat.dao.mapper.customize;

import com.howbuy.crm.wechat.dao.po.CmWechatExternalInfoPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: crm-wechat 
 * @author: yu.zhang
 * @date: 2023/6/12 19:50 
 * @since JDK 1.8
 * @version: 1.0
 */
@Mapper
public interface CustomizeCmWechatExternalInfoMapper {
    /**
     * @description:(请在此添加描述)
     * @param externalUserID	企业微信外部联系人ID
     * @param changeType	
     * @param companyNo	企业代码
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatExternalInfoPO>
     * @author: yu.zhang
     * @date: 2023/6/12 20:05
     * @since JDK 1.8
     */
    List<CmWechatExternalInfoPO> listCmWechatExternal(@Param("externalUserID") String externalUserID, @Param("changeType") String changeType, @Param("companyNo") String companyNo);
}