package com.howbuy.crm.wechat.dao.po;

import java.util.Date;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/7/24 8:35
 * @since JDK 1.8
 */
/**
 * 企微企业主体信息表
 */
public class CmWechatCompanyPO {

    /**
     * id
     */
    private Long id;
    /**
    * 企业编码:1-好买财富 2-好买基金
    */
    private String companyNo;

    /**
    * 企业编码描述:1-好买财富 2-好买基金
    */
    private String companyDesc;

    /**
     * 企业类型。1-企业内部 2-第三方应用 3-服务商代开发 4-智慧硬件开发
     */
    private String corpType;

    /**
    * 企业Id
    */
    private String corpId;

    /**
    * 企业微信后台，开发者设置的token
    */
    private String token;

    /**
    * 企业微信后台，开发者设置的EncodingAESKey
    */
    private String encodingAesKey;

    /**
     * 企业微信-客户联系-secret
     */
    private String customerSecret;

    /**
     * 环境编码:dev-开发环境 gray-灾备环境 prod-生产环境
     */
    private String envCode;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 修改时间
    */
    private Date modifyTime;

    /**
    * 记录状态:1-正常 2-删除
    */
    private String recStat;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyNo() {
        return companyNo;
    }

    public void setCompanyNo(String companyNo) {
        this.companyNo = companyNo;
    }

    public String getCompanyDesc() {
        return companyDesc;
    }

    public void setCompanyDesc(String companyDesc) {
        this.companyDesc = companyDesc;
    }

    public String getCorpType() {
        return corpType;
    }

    public void setCorpType(String corpType) {
        this.corpType = corpType;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getEncodingAesKey() {
        return encodingAesKey;
    }

    public void setEncodingAesKey(String encodingAesKey) {
        this.encodingAesKey = encodingAesKey;
    }

    public String getCustomerSecret() {
        return customerSecret;
    }

    public void setCustomerSecret(String customerSecret) {
        this.customerSecret = customerSecret;
    }

    public String getEnvCode() {
        return envCode;
    }

    public void setEnvCode(String envCode) {
        this.envCode = envCode;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRecStat() {
        return recStat;
    }

    public void setRecStat(String recStat) {
        this.recStat = recStat;
    }
}