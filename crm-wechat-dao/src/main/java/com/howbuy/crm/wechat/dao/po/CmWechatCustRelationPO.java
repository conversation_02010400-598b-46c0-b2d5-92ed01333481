package com.howbuy.crm.wechat.dao.po;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description: crm-wechat 
 * @author: yu.z<PERSON> 
 * @date: 2023/6/28 10:03 
 * @since JDK 1.8
 * @version: 1.0 
 */

/**
 * 客户微信投顾关联关系表
 */
@Getter
@Setter
@ToString
public class CmWechatCustRelationPO implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 投顾号
     */
    private String conscode;

    /**
     * 状态1新增2删除客户3被客户删除
     */
    private String status;

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 删除时间
     */
    private Date delTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 添加客户的渠道
     */
    private String state;

    /**
     * 企业编码1好买财富2好买基金
     */
    private String companyNo;

    /**
     * 外部应用用户ID
     */
    private String externalUserId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 添加客户的来源
     */
    private String addWay;

    /**
     * 首次添加时间
     */
    private Date firstAddTime;

    private static final long serialVersionUID = 1L;
}