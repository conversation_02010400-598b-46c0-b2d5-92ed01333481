package com.howbuy.crm.wechat.dao.mapper;

import com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO;
import com.howbuy.crm.wechat.dao.po.custrelation.CustConsultRelationPO;
import com.howbuy.crm.wechat.dao.vo.custrelation.CustConsultRelationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: crm-wechat 
 * @author: yu.zhang
 * @date: 2023/6/28 10:03 
 * @since JDK 1.8
 * @version: 1.0
 */
@Mapper
public interface CmWechatCustRelationMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(CmWechatCustRelationPO record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(CmWechatCustRelationPO record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    CmWechatCustRelationPO selectByPrimaryKey(Long id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(CmWechatCustRelationPO record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(CmWechatCustRelationPO record);



    /**
     * @description:(根据 hboneNo vs conscode 批量查询客户投顾关联关系)
     * @param companyNo  企微-企业主体
     * @param voList
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.custrelation.CustConsultRelationPO>
     * @author: haoran.zhang
     * @date: 2023/11/27 9:57
     * @since JDK 1.8
     */
    List<CustConsultRelationPO> selectRelationListByVo(@Param("companyNo")String companyNo,
                                                       @Param("voList") List<CustConsultRelationVO> voList);

    /**
     * @description:(根据客户id获取所有的投顾客户的添加关系)
     * @param companyNo  企微-企业主体
     * @param externalIds
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO>
     * @author: shuai.zhang
     * @date: 2024/3/28 11:29
     * @since JDK 1.8
     */
    List<CmWechatCustRelationPO> getRealtionListByExternalUserId(@Param("companyNo")String companyNo,
                                                                 @Param("externalIds")List<String> externalIds);

    /**
     * @description:(旧的投顾客户关系删除)
     * @param info
     * @return int
     * @author: shuai.zhang
     * @date: 2024/3/28 15:30
     * @since JDK 1.8
     */
    @Deprecated
    int updateCmWechatCustRelationDel(@Param("info")CmWechatCustRelationPO info);

}