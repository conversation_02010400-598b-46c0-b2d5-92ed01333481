package com.howbuy.crm.wechat.dao.po;

import java.util.Date;

/**
 * @Description:
 * @Author: yun.lu
 * Date: 2025/5/21 17:04
*/

/**
 * 客户微信信息表
 */
public class CmWechatCustInfoPO {
    /**
     * id
     */
    private Long id;

    /**
     * 国内一账通号
     */
    private String hboneNo;

    /**
     * 外部应用用户ID
     */
    private String externalUserId;

    /**
     * 微信UnionId
     */
    private String unionid;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 企业编码1好买财富2好买基金
     */
    private String companyNo;

    /**
     * 微信头像
     */
    private String wechatAvatar;

    /**
     * 香港一账通号
     */
    private String hkHboneNo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getExternalUserId() {
        return externalUserId;
    }

    public void setExternalUserId(String externalUserId) {
        this.externalUserId = externalUserId;
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCompanyNo() {
        return companyNo;
    }

    public void setCompanyNo(String companyNo) {
        this.companyNo = companyNo;
    }

    public String getWechatAvatar() {
        return wechatAvatar;
    }

    public void setWechatAvatar(String wechatAvatar) {
        this.wechatAvatar = wechatAvatar;
    }

    public String getHkHboneNo() {
        return hkHboneNo;
    }

    public void setHkHboneNo(String hkHboneNo) {
        this.hkHboneNo = hkHboneNo;
    }
}