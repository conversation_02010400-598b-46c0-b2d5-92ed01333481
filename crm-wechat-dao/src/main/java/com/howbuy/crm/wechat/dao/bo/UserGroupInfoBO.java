/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.dao.bo;

import lombok.Data;

import java.util.Date;

/**
 * @description: 客户群信息BO
 * <AUTHOR>
 * @date 2024/1/29 15:35
 * @since JDK 1.8
 */
@Data
public class UserGroupInfoBO {
    /**
     * 客户群id
     */
    private String hboneNo;

    /**
     * 客户微信昵称
     */
    private String nickName;

    /**
     * 客户群 ID
     */
    private String chatId;

    /**
     * 客户在群状态
     */
    private String userChatFlag;

    /**
     * 添加企微时间
     */
    private Date addTime;
    /**
     * 企微外部客户 ID
     */
    private String externalUserId;


}