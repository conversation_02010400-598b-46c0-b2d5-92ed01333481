package com.howbuy.crm.wechat.dao.mapper.message;

import com.github.pagehelper.Page;
import com.howbuy.crm.wechat.dao.po.message.MessageAcceptInfoPO;
import com.howbuy.crm.wechat.dao.vo.message.MessageAcceptInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/10/7 9:59
 * @since JDK 1.8
 */
public interface MessageAcceptInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MessageAcceptInfoPO record);

    MessageAcceptInfoPO selectByPrimaryKey(Long id);

    /**
     * 根据业务唯一标识查询
     * @param uniqueId
     * @return
     */
    MessageAcceptInfoPO selectByUniqueId(@Param("messageType") String messageType,@Param("uniqueId") String  uniqueId);


    int updateByPrimaryKeySelective(MessageAcceptInfoPO record);

    int updateByPrimaryKey(MessageAcceptInfoPO record);

    Page<MessageAcceptInfoPO> selectPageByVo(MessageAcceptInfoVO acceptInfoVO);

    int batchUpdateStatus(@Param("messageStatus") String messageStatus,@Param("idList")  List<Long> idList);


    /**
     * @description: (根据消息类型和业务唯一标识查询)
     * @param messageType 消息类型
     * @param uniqueIdList 业务唯一标识list
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.message.MessageAcceptInfoPO> 接收到的消息list
     * @author: jin.wang03
     * @date: 2024/3/22 14:07
     * @since JDK 1.8
     */
    List<MessageAcceptInfoPO> selectByTypeAndUniqueIdList(@Param("messageType") String messageType, @Param("uniqueIdList") List<String> uniqueIdList);
}