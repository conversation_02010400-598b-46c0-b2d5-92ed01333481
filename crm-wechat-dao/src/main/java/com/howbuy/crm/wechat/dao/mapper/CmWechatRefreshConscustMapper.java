package com.howbuy.crm.wechat.dao.mapper;

import com.howbuy.crm.wechat.dao.po.CmWechatRefreshConscustPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: shuai.zhang
 * @Date: 2024/3/26
 * @Description: 
*/
public interface CmWechatRefreshConscustMapper {
    int deleteByPrimaryKey(String id);

    int insert(CmWechatRefreshConscustPO record);

    int insertSelective(CmWechatRefreshConscustPO record);

    CmWechatRefreshConscustPO selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CmWechatRefreshConscustPO record);

    int updateByPrimaryKey(CmWechatRefreshConscustPO record);

    int batchInsert(@Param("list") List<CmWechatRefreshConscustPO> list);

    /**
     * @description:( 投顾客户微信刷新状态表 获取当天已处理数据)
     * @param companyNo  企微-企业主体
     * @param nowDate 处理时间YYYYMMDD
     * @param status  处理状态:1-已处理 0-未处理 2-处理完废弃
     * @param dealtype 处理类型: 1-投顾客户列表刷新 2-客户姓名备注刷新
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatRefreshConscustPO>
     * @author: shuai.zhang
     * @date: 2024/3/26 15:38
     * @since JDK 1.8
     */
    List<CmWechatRefreshConscustPO> getRefreshTask(
            @Param("companyNo") String companyNo,
            @Param("nowDate")String nowDate,
            @Param("dealstatus")String status,
            @Param("dealtype")String dealtype);

    /**
     * @description:(插入待处理任务)
     * @param companyNo  企微-企业主体
     * @param nowDate 处理时间YYYYMMDD
     * @param dealType 处理类型: 1-投顾客户列表刷新 2-客户姓名备注刷新
     * @param refreshConscodeList
     * @return int
     * @author: shuai.zhang
     * @date: 2024/3/26 18:38
     * @since JDK 1.8
     */
    int insertRefreshTask(@Param("companyNo") String companyNo,
                          @Param("nowDate")String nowDate,
                          @Param("dealType")String dealType,
                          @Param("refreshConscodeList") List<String> refreshConscodeList);

    /**
     * @description:(更新处理任务状态，从1-已处理更新为：2-处理完废弃)
     * @param companyNo  where条件：企微-企业主体
     * @param nowDate where条件：处理时间YYYYMMDD
     * @param dealType where条件：处理类型: 1-投顾客户列表刷新 2-客户姓名备注刷新
     * @return int
     * @author: shuai.zhang
     * @date: 2024/3/27 18:14
     * @since JDK 1.8
     */
    int discardRefreshTask(@Param("companyNo") String companyNo,
                           @Param("nowDate")String nowDate,
                           @Param("dealType")String dealType);
}