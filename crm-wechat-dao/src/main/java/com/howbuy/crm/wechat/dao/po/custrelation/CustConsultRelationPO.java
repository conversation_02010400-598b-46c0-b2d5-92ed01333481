package com.howbuy.crm.wechat.dao.po.custrelation;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * @description:(客户 vs 投顾 企业微信关联关系表)
 * @param
 * @return 
 * @author: haoran.zhang
 * @date: 2023/11/27 9:37
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class CustConsultRelationPO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private Long id;


    /**
     * 客户信息-外部应用用户ID
     */
    private String externalUserId;


    /**
     * 客户信息-一账通号
     */
    private String hboneNo;

    /**
     * 客户信息-微信UnionId
     */
    private String unionid;

    /**
     * 投顾号
     */
    private String conscode;

    /**
     * 状态1新增  2删除客户   3被客户删除
     */
    private String status;

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 删除时间
     */
    private Date delTime;

    /**
     * 企业编码1好买财富2好买基金
     */
    private String companyNo;


    /**
     * 添加客户的来源
     */
    private String addWay;


    /**
     * 添加客户的渠道
     */
    private String state;


}