package com.howbuy.crm.wechat.dao.mapper;

import com.howbuy.crm.wechat.dao.po.CmWechatDeptChangeHisPO;
import org.apache.ibatis.annotations.Mapper;

/**
 * @description: crm-wechat 
 * @author: yu.zhang
 * @date: 2023/6/28 10:03 
 * @since JDK 1.8
 * @version: 1.0
 */
@Mapper
public interface CmWechatDeptChangeHisMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(CmWechatDeptChangeHisPO record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(CmWechatDeptChangeHisPO record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    CmWechatDeptChangeHisPO selectByPrimaryKey(Long id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(CmWechatDeptChangeHisPO record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(CmWechatDeptChangeHisPO record);
}