package com.howbuy.crm.wechat.dao.po;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: shuai.zhang
 * @Date: 2024/3/26
 * @Description: 
*/
/**
 * 投顾客户微信刷新状态表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmWechatRefreshConscustPO {
    /**
    * 主键
    */
    private String id;

    /**
    * 公司编号
    */
    private String companyNo;

    /**
    * 处理数据
    */
    private String dealData;

    /**
    * 处理时间YYYYMMDD
    */
    private String dealDate;

    /**
    * 处理类型1投顾客户列表刷新2客户姓名备注刷新
    */
    private String dealType;

    /**
    * 处理状态1已处理0未处理2处理完废弃
    */
    private String dealStatus;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新人
    */
    private String modifier;

    /**
    * 更新时间
    */
    private Date updateTime;
}