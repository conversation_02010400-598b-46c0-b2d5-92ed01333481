package com.howbuy.crm.wechat.dao.mapper.customize;

import com.howbuy.crm.wechat.dao.po.CmWechatEmpPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: 自定义员工操作表
 * @author: yu.zhang
 * @date: 2023/6/12 13:07 
 * @since JDK 1.8
 * @version: 1.0
 */
@Mapper
public interface CustomizeCmWechatEmpMapper {

    /**
     * @description:根据员工ID查询对应员工
     * @param empId
     * @param companyNo	企业编码
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatEmpPO>
     * @author: yu.zhang
     * @date: 2023/6/12 16:48
     * @since JDK 1.8
     */
    List<CmWechatEmpPO> listWechatEmpByEmpId(@Param("empId") String empId, @Param("companyNo") String companyNo);

    /**
     * @description:查询全量员工数据
     * @param companyNo	企业编码
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatEmpPO>
     * @author: yu.zhang
     * @date: 2023/7/27 15:56
     * @since JDK 1.8
     */
    List<CmWechatEmpPO> listAllWechatEmp(@Param("companyNo") String companyNo);

    /**
     * @description: 查询所有在职员工的emp_id
     * @param companyNo	企业编码
     * @return java.util.List<java.lang.String> 员工empID
     * @author: jin.wang03
     * @date: 2023/10/25 17:57
     * @since JDK 1.8
     */
    List<String> listAllWechatEmpId(@Param("companyNo") String companyNo);

}