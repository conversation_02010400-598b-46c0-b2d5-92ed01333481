package com.howbuy.crm.wechat.dao.po;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description: crm-wechat 
 * @author: yu.z<PERSON> 
 * @date: 2023/6/28 10:03 
 * @since JDK 1.8
 * @version: 1.0 
 */

/**
 * 企业微信外部关联信息
 */
@Getter
@Setter
@ToString
public class CmWechatExternalInfoPO implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 外部应用用户ID
     */
    private String externalUserId;

    /**
     * 触发方式--参见企业微信API
     */
    private String msgType;

    /**
     * 消息事件--参见企业微信API
     */
    private String changeType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 消息类型--参见企业微信API
     */
    private String event;

    /**
     * 微信UnionId
     */
    private String unionid;

    /**
     * 企业编码1好买财富2好买基金
     */
    private String companyNo;

    private static final long serialVersionUID = 1L;
}