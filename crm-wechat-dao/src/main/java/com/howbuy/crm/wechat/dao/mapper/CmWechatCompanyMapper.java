package com.howbuy.crm.wechat.dao.mapper;

import com.howbuy.crm.wechat.dao.po.CmWechatCompanyPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/7/24 8:35
 * @since JDK 1.8
 */
@Mapper
public interface CmWechatCompanyMapper {
    int deleteByPrimaryKey(String companyNo);

    int insert(CmWechatCompanyPO record);

    int insertSelective(CmWechatCompanyPO record);

    CmWechatCompanyPO selectByPrimaryKey(String companyNo);

    int updateByPrimaryKeySelective(CmWechatCompanyPO record);

    int updateByPrimaryKey(CmWechatCompanyPO record);



    /**
     * 根据corpId查询有效的公司配置信息
     * @param envCode 环境编码
     * @param corpId
     * @return
     */
    CmWechatCompanyPO selectConfigByCorpId(@Param("envCode") String envCode,
                                           @Param("corpId") String corpId);

    /**
     * 根据companyNo查询公司配置信息[包括有效的、无效的]
     * @param envCode 环境编码
     * @param companyNo
     * @return
     */
    CmWechatCompanyPO selectConfigByCompanyNo(@Param("envCode") String envCode,
                                              @Param("companyNo") String companyNo);


    /**
     * 查询有效的公司配置信息列表[包括有效的、无效的]
     * @param envCode 环境编码
     * @return List<CmWechatCompanyPO>
     */
    List<CmWechatCompanyPO> selectConfigList(@Param("envCode") String envCode);
}