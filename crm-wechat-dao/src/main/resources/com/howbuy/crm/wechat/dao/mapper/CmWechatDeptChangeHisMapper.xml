<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.CmWechatDeptChangeHisMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatDeptChangeHisPO">
    <!--@mbg.generated-->
    <!--@Table `cm_wechat_dept_change_his`-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="DEPT_ID" jdbcType="INTEGER" property="deptId" />
    <result column="BEFORE_DEPT_NAME" jdbcType="VARCHAR" property="beforeDeptName" />
    <result column="AFTER_DEPT_NAME" jdbcType="VARCHAR" property="afterDeptName" />
    <result column="CHANGE_TIME" jdbcType="TIMESTAMP" property="changeTime" />
    <result column="COMPANY_NO" jdbcType="VARCHAR" property="companyNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `ID`, `DEPT_ID`, `BEFORE_DEPT_NAME`, `AFTER_DEPT_NAME`, `CHANGE_TIME`, `COMPANY_NO`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from `cm_wechat_dept_change_his`
    where `ID` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from `cm_wechat_dept_change_his`
    where `ID` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatDeptChangeHisPO">
    <!--@mbg.generated-->
    insert into `cm_wechat_dept_change_his` (`ID`, `DEPT_ID`, `BEFORE_DEPT_NAME`, 
      `AFTER_DEPT_NAME`, `CHANGE_TIME`, `COMPANY_NO`
      )
    values (#{id,jdbcType=BIGINT}, #{deptId,jdbcType=INTEGER}, #{beforeDeptName,jdbcType=VARCHAR},
      #{afterDeptName,jdbcType=VARCHAR}, #{changeTime,jdbcType=TIMESTAMP}, #{companyNo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatDeptChangeHisPO">
    <!--@mbg.generated-->
    insert into `cm_wechat_dept_change_his`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `ID`,
      </if>
      <if test="deptId != null">
        `DEPT_ID`,
      </if>
      <if test="beforeDeptName != null">
        `BEFORE_DEPT_NAME`,
      </if>
      <if test="afterDeptName != null">
        `AFTER_DEPT_NAME`,
      </if>
      <if test="changeTime != null">
        `CHANGE_TIME`,
      </if>
      <if test="companyNo != null">
        `COMPANY_NO`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="beforeDeptName != null">
        #{beforeDeptName,jdbcType=VARCHAR},
      </if>
      <if test="afterDeptName != null">
        #{afterDeptName,jdbcType=VARCHAR},
      </if>
      <if test="changeTime != null">
        #{changeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyNo != null">
        #{companyNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatDeptChangeHisPO">
    <!--@mbg.generated-->
    update `cm_wechat_dept_change_his`
    <set>
      <if test="deptId != null">
        `DEPT_ID` = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="beforeDeptName != null">
        `BEFORE_DEPT_NAME` = #{beforeDeptName,jdbcType=VARCHAR},
      </if>
      <if test="afterDeptName != null">
        `AFTER_DEPT_NAME` = #{afterDeptName,jdbcType=VARCHAR},
      </if>
      <if test="changeTime != null">
        `CHANGE_TIME` = #{changeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyNo != null">
        `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR},
      </if>
    </set>
    where `ID` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatDeptChangeHisPO">
    <!--@mbg.generated-->
    update `cm_wechat_dept_change_his`
    set `DEPT_ID` = #{deptId,jdbcType=INTEGER},
      `BEFORE_DEPT_NAME` = #{beforeDeptName,jdbcType=VARCHAR},
      `AFTER_DEPT_NAME` = #{afterDeptName,jdbcType=VARCHAR},
      `CHANGE_TIME` = #{changeTime,jdbcType=TIMESTAMP},
      `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR}
    where `ID` = #{id,jdbcType=BIGINT}
  </update>

</mapper>