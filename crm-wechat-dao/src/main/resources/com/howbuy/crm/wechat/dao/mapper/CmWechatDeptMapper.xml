<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.CmWechatDeptMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatDeptPO">
    <!--@mbg.generated-->
    <!--@Table `cm_wechat_dept`-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="DEPT_ID" jdbcType="INTEGER" property="deptId" />
    <result column="DEPT_NAME" jdbcType="VARCHAR" property="deptName" />
    <result column="PARENT_DEPT_ID" jdbcType="INTEGER" property="parentDeptId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="CHAR" property="delFlag" />
    <result column="DEL_TIME" jdbcType="TIMESTAMP" property="delTime" />
    <result column="COMPANY_NO" jdbcType="VARCHAR" property="companyNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `ID`, `DEPT_ID`, `DEPT_NAME`, `PARENT_DEPT_ID`, `CREATE_TIME`, `UPDATE_TIME`, `DEL_FLAG`, 
    `DEL_TIME`, `COMPANY_NO`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from `cm_wechat_dept`
    where `ID` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from `cm_wechat_dept`
    where `ID` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatDeptPO">
    <!--@mbg.generated-->
    insert into `cm_wechat_dept` (`ID`, `DEPT_ID`, `DEPT_NAME`, 
      `PARENT_DEPT_ID`, `CREATE_TIME`, `UPDATE_TIME`, 
      `DEL_FLAG`, `DEL_TIME`, `COMPANY_NO`
      )
    values (#{id,jdbcType=BIGINT}, #{deptId,jdbcType=INTEGER}, #{deptName,jdbcType=VARCHAR},
      #{parentDeptId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{delFlag,jdbcType=CHAR}, #{delTime,jdbcType=TIMESTAMP}, #{companyNo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatDeptPO">
    <!--@mbg.generated-->
    insert into `cm_wechat_dept`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `ID`,
      </if>
      <if test="deptId != null">
        `DEPT_ID`,
      </if>
      <if test="deptName != null">
        `DEPT_NAME`,
      </if>
      <if test="parentDeptId != null">
        `PARENT_DEPT_ID`,
      </if>
      <if test="createTime != null">
        `CREATE_TIME`,
      </if>
      <if test="updateTime != null">
        `UPDATE_TIME`,
      </if>
      <if test="delFlag != null">
        `DEL_FLAG`,
      </if>
      <if test="delTime != null">
        `DEL_TIME`,
      </if>
      <if test="companyNo != null">
        `COMPANY_NO`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="deptName != null">
        #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="parentDeptId != null">
        #{parentDeptId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
      <if test="delTime != null">
        #{delTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyNo != null">
        #{companyNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatDeptPO">
    <!--@mbg.generated-->
    update `cm_wechat_dept`
    <set>
      <if test="deptId != null">
        `DEPT_ID` = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="deptName != null">
        `DEPT_NAME` = #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="parentDeptId != null">
        `PARENT_DEPT_ID` = #{parentDeptId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        `CREATE_TIME` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `UPDATE_TIME` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        `DEL_FLAG` = #{delFlag,jdbcType=CHAR},
      </if>
      <if test="delTime != null">
        `DEL_TIME` = #{delTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyNo != null">
        `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR},
      </if>
    </set>
    where `ID` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatDeptPO">
    <!--@mbg.generated-->
    update `cm_wechat_dept`
    set `DEPT_ID` = #{deptId,jdbcType=INTEGER},
      `DEPT_NAME` = #{deptName,jdbcType=VARCHAR},
      `PARENT_DEPT_ID` = #{parentDeptId,jdbcType=INTEGER},
      `CREATE_TIME` = #{createTime,jdbcType=TIMESTAMP},
      `UPDATE_TIME` = #{updateTime,jdbcType=TIMESTAMP},
      `DEL_FLAG` = #{delFlag,jdbcType=CHAR},
      `DEL_TIME` = #{delTime,jdbcType=TIMESTAMP},
      `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR}
    where `ID` = #{id,jdbcType=BIGINT}
  </update>

</mapper>