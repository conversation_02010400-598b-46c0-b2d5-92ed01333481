<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.CmWechatEmpChangeHisMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatEmpChangeHisPO">
    <!--@mbg.generated-->
    <!--@Table `cm_wechat_emp_change_his`-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="EMP_ID" jdbcType="VARCHAR" property="empId" />
    <result column="BEFORE_DEPT_ID" jdbcType="INTEGER" property="beforeDeptId" />
    <result column="AFTER_DEPT_ID" jdbcType="INTEGER" property="afterDeptId" />
    <result column="CHANGE_TIME" jdbcType="TIMESTAMP" property="changeTime" />
    <result column="COMPANY_NO" jdbcType="VARCHAR" property="companyNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `ID`, `EMP_ID`, `BEFORE_DEPT_ID`, `AFTER_DEPT_ID`, `CHANGE_TIME`, `COMPANY_NO`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from `cm_wechat_emp_change_his`
    where `ID` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from `cm_wechat_emp_change_his`
    where `ID` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatEmpChangeHisPO">
    <!--@mbg.generated-->
    insert into `cm_wechat_emp_change_his` (`ID`, `EMP_ID`, `BEFORE_DEPT_ID`, 
      `AFTER_DEPT_ID`, `CHANGE_TIME`, `COMPANY_NO`
      )
    values (#{id,jdbcType=BIGINT}, #{empId,jdbcType=VARCHAR}, #{beforeDeptId,jdbcType=INTEGER},
      #{afterDeptId,jdbcType=INTEGER}, #{changeTime,jdbcType=TIMESTAMP}, #{companyNo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatEmpChangeHisPO">
    <!--@mbg.generated-->
    insert into `cm_wechat_emp_change_his`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `ID`,
      </if>
      <if test="empId != null">
        `EMP_ID`,
      </if>
      <if test="beforeDeptId != null">
        `BEFORE_DEPT_ID`,
      </if>
      <if test="afterDeptId != null">
        `AFTER_DEPT_ID`,
      </if>
      <if test="changeTime != null">
        `CHANGE_TIME`,
      </if>
      <if test="companyNo != null">
        `COMPANY_NO`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=VARCHAR},
      </if>
      <if test="beforeDeptId != null">
        #{beforeDeptId,jdbcType=INTEGER},
      </if>
      <if test="afterDeptId != null">
        #{afterDeptId,jdbcType=INTEGER},
      </if>
      <if test="changeTime != null">
        #{changeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyNo != null">
        #{companyNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatEmpChangeHisPO">
    <!--@mbg.generated-->
    update `cm_wechat_emp_change_his`
    <set>
      <if test="empId != null">
        `EMP_ID` = #{empId,jdbcType=VARCHAR},
      </if>
      <if test="beforeDeptId != null">
        `BEFORE_DEPT_ID` = #{beforeDeptId,jdbcType=INTEGER},
      </if>
      <if test="afterDeptId != null">
        `AFTER_DEPT_ID` = #{afterDeptId,jdbcType=INTEGER},
      </if>
      <if test="changeTime != null">
        `CHANGE_TIME` = #{changeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyNo != null">
        `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR},
      </if>
    </set>
    where `ID` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatEmpChangeHisPO">
    <!--@mbg.generated-->
    update `cm_wechat_emp_change_his`
    set `EMP_ID` = #{empId,jdbcType=VARCHAR},
      `BEFORE_DEPT_ID` = #{beforeDeptId,jdbcType=INTEGER},
      `AFTER_DEPT_ID` = #{afterDeptId,jdbcType=INTEGER},
      `CHANGE_TIME` = #{changeTime,jdbcType=TIMESTAMP},
      `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR}
    where `ID` = #{id,jdbcType=BIGINT}
  </update>
</mapper>