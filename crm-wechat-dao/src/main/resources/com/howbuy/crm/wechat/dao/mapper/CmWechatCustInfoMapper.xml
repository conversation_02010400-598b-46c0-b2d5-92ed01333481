<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.CmWechatCustInfoMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO">
    <!--@mbg.generated-->
    <!--@Table cm_wechat_cust_info-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="HBONE_NO" jdbcType="VARCHAR" property="hboneNo" />
    <result column="EXTERNAL_USER_ID" jdbcType="VARCHAR" property="externalUserId" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="NICK_NAME" jdbcType="VARCHAR" property="nickName" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="COMPANY_NO" jdbcType="VARCHAR" property="companyNo" />
    <result column="wechat_avatar" jdbcType="VARCHAR" property="wechatAvatar" />
    <result column="HK_HBONE_NO" jdbcType="VARCHAR" property="hkHboneNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, HBONE_NO, EXTERNAL_USER_ID, UNIONID, NICK_NAME, CREATOR, CREATE_TIME, MODIFIER,
    UPDATE_TIME, COMPANY_NO, wechat_avatar, HK_HBONE_NO
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from cm_wechat_cust_info
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from cm_wechat_cust_info
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO">
    <!--@mbg.generated-->
    insert into cm_wechat_cust_info (ID, HBONE_NO, EXTERNAL_USER_ID,
      UNIONID, NICK_NAME, CREATOR,
      CREATE_TIME, MODIFIER, UPDATE_TIME,
      COMPANY_NO, wechat_avatar, HK_HBONE_NO
      )
    values (#{id,jdbcType=BIGINT}, #{hboneNo,jdbcType=VARCHAR}, #{externalUserId,jdbcType=VARCHAR},
      #{unionid,jdbcType=VARCHAR}, #{nickName,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
      #{companyNo,jdbcType=VARCHAR}, #{wechatAvatar,jdbcType=VARCHAR}, #{hkHboneNo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO">
    <!--@mbg.generated-->
    insert into cm_wechat_cust_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="hboneNo != null">
        HBONE_NO,
      </if>
      <if test="externalUserId != null">
        EXTERNAL_USER_ID,
      </if>
      <if test="unionid != null">
        UNIONID,
      </if>
      <if test="nickName != null">
        NICK_NAME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="companyNo != null">
        COMPANY_NO,
      </if>
      <if test="wechatAvatar != null">
        wechat_avatar,
      </if>
      <if test="hkHboneNo != null">
        HK_HBONE_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="hboneNo != null">
        #{hboneNo,jdbcType=VARCHAR},
      </if>
      <if test="externalUserId != null">
        #{externalUserId,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyNo != null">
        #{companyNo,jdbcType=VARCHAR},
      </if>
      <if test="wechatAvatar != null">
        #{wechatAvatar,jdbcType=VARCHAR},
      </if>
      <if test="hkHboneNo != null">
        #{hkHboneNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO">
    <!--@mbg.generated-->
    update cm_wechat_cust_info
    <set>
      <if test="hboneNo != null">
        HBONE_NO = #{hboneNo,jdbcType=VARCHAR},
      </if>
      <if test="externalUserId != null">
        EXTERNAL_USER_ID = #{externalUserId,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        UNIONID = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        NICK_NAME = #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyNo != null">
        COMPANY_NO = #{companyNo,jdbcType=VARCHAR},
      </if>
      <if test="wechatAvatar != null">
        wechat_avatar = #{wechatAvatar,jdbcType=VARCHAR},
      </if>
      <if test="hkHboneNo != null">
        HK_HBONE_NO = #{hkHboneNo,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO">
    <!--@mbg.generated-->
    update cm_wechat_cust_info
    set HBONE_NO = #{hboneNo,jdbcType=VARCHAR},
      EXTERNAL_USER_ID = #{externalUserId,jdbcType=VARCHAR},
      UNIONID = #{unionid,jdbcType=VARCHAR},
      NICK_NAME = #{nickName,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      COMPANY_NO = #{companyNo,jdbcType=VARCHAR},
      wechat_avatar = #{wechatAvatar,jdbcType=VARCHAR},
      HK_HBONE_NO = #{hkHboneNo,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=BIGINT}
  </update>
</mapper>