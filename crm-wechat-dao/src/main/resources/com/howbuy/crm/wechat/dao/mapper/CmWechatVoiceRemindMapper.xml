<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.CmWechatVoiceRemindMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatVoiceRemindPO">
    <!--@mbg.generated-->
    <!--@Table `cm_wechat_voice_remind`-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="ACCEPT_USER_ID" jdbcType="VARCHAR" property="acceptUserId" />
    <result column="REMIND_TIME" jdbcType="TIMESTAMP" property="remindTime" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="REMIND_TYPE" jdbcType="VARCHAR" property="remindType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `ID`, `MOBILE`, `ACCEPT_USER_ID`, `REMIND_TIME`, `CREATE_TIME`, `REMIND_TYPE`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from `cm_wechat_voice_remind`
    where `ID` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from `cm_wechat_voice_remind`
    where `ID` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatVoiceRemindPO">
    <!--@mbg.generated-->
    insert into `cm_wechat_voice_remind` (`ID`, `MOBILE`, `ACCEPT_USER_ID`, 
      `REMIND_TIME`, `CREATE_TIME`, `REMIND_TYPE`
      )
    values (#{id,jdbcType=BIGINT}, #{mobile,jdbcType=VARCHAR}, #{acceptUserId,jdbcType=VARCHAR}, 
      #{remindTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{remindType,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatVoiceRemindPO">
    <!--@mbg.generated-->
    insert into `cm_wechat_voice_remind`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `ID`,
      </if>
      <if test="mobile != null">
        `MOBILE`,
      </if>
      <if test="acceptUserId != null">
        `ACCEPT_USER_ID`,
      </if>
      <if test="remindTime != null">
        `REMIND_TIME`,
      </if>
      <if test="createTime != null">
        `CREATE_TIME`,
      </if>
      <if test="remindType != null">
        `REMIND_TYPE`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="acceptUserId != null">
        #{acceptUserId,jdbcType=VARCHAR},
      </if>
      <if test="remindTime != null">
        #{remindTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remindType != null">
        #{remindType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatVoiceRemindPO">
    <!--@mbg.generated-->
    update `cm_wechat_voice_remind`
    <set>
      <if test="mobile != null">
        `MOBILE` = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="acceptUserId != null">
        `ACCEPT_USER_ID` = #{acceptUserId,jdbcType=VARCHAR},
      </if>
      <if test="remindTime != null">
        `REMIND_TIME` = #{remindTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        `CREATE_TIME` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remindType != null">
        `REMIND_TYPE` = #{remindType,jdbcType=VARCHAR},
      </if>
    </set>
    where `ID` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatVoiceRemindPO">
    <!--@mbg.generated-->
    update `cm_wechat_voice_remind`
    set `MOBILE` = #{mobile,jdbcType=VARCHAR},
      `ACCEPT_USER_ID` = #{acceptUserId,jdbcType=VARCHAR},
      `REMIND_TIME` = #{remindTime,jdbcType=TIMESTAMP},
      `CREATE_TIME` = #{createTime,jdbcType=TIMESTAMP},
      `REMIND_TYPE` = #{remindType,jdbcType=VARCHAR}
    where `ID` = #{id,jdbcType=BIGINT}
  </update>
</mapper>