<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.CmWechatCustRelationMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO">
    <!--@mbg.generated-->
    <!--@Table `cm_wechat_cust_relation`-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="CONSCODE" jdbcType="VARCHAR" property="conscode" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="DEL_TIME" jdbcType="TIMESTAMP" property="delTime" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="STATE" jdbcType="VARCHAR" property="state" />
    <result column="COMPANY_NO" jdbcType="VARCHAR" property="companyNo" />
    <result column="EXTERNAL_USER_ID" jdbcType="VARCHAR" property="externalUserId" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="ADD_WAY" jdbcType="VARCHAR" property="addWay" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `ID`, `CONSCODE`, `STATUS`, `ADD_TIME`, `DEL_TIME`, `CREATOR`, `CREATE_TIME`, `UPDATE_TIME`, 
    `STATE`, `COMPANY_NO`, `EXTERNAL_USER_ID`, `MODIFIER`, `ADD_WAY`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from `cm_wechat_cust_relation`
    where `ID` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from `cm_wechat_cust_relation`
    where `ID` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO">
    <!--@mbg.generated-->
    insert into `cm_wechat_cust_relation` (`ID`, `CONSCODE`, `STATUS`, 
      `ADD_TIME`, `DEL_TIME`, `CREATOR`, 
      `CREATE_TIME`, `UPDATE_TIME`, `STATE`, 
      `COMPANY_NO`, `EXTERNAL_USER_ID`, `MODIFIER`, 
      `ADD_WAY`, `FIRST_ADD_TIME`)
    values (#{id,jdbcType=BIGINT}, #{conscode,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{delTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{state,jdbcType=VARCHAR}, 
      #{companyNo,jdbcType=VARCHAR}, #{externalUserId,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{addWay,jdbcType=VARCHAR}, sysdate())
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO">
    <!--@mbg.generated-->
    insert into `cm_wechat_cust_relation`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `ID`,
      </if>
      <if test="conscode != null">
        `CONSCODE`,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="addTime != null">
        `ADD_TIME`,
      </if>
      <if test="delTime != null">
        `DEL_TIME`,
      </if>
      <if test="creator != null">
        `CREATOR`,
      </if>
      <if test="createTime != null">
        `CREATE_TIME`,
      </if>
      <if test="updateTime != null">
        `UPDATE_TIME`,
      </if>
      <if test="state != null">
        `STATE`,
      </if>
      <if test="companyNo != null">
        `COMPANY_NO`,
      </if>
      <if test="externalUserId != null">
        `EXTERNAL_USER_ID`,
      </if>
      <if test="modifier != null">
        `MODIFIER`,
      </if>
      <if test="addWay != null">
        `ADD_WAY`,
      </if>
      `FIRST_ADD_TIME`,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="conscode != null">
        #{conscode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delTime != null">
        #{delTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null">
        #{state,jdbcType=VARCHAR},
      </if>
      <if test="companyNo != null">
        #{companyNo,jdbcType=VARCHAR},
      </if>
      <if test="externalUserId != null">
        #{externalUserId,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="addWay != null">
        #{addWay,jdbcType=VARCHAR},
      </if>
      sysdate(),
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO">
    <!--@mbg.generated-->
    update `cm_wechat_cust_relation`
    <set>
      <if test="conscode != null">
        `CONSCODE` = #{conscode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        `ADD_TIME` = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delTime != null">
        `DEL_TIME` = #{delTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        `CREATOR` = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        `CREATE_TIME` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `UPDATE_TIME` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="state != null">
        `STATE` = #{state,jdbcType=VARCHAR},
      </if>
      <if test="companyNo != null">
        `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR},
      </if>
      <if test="externalUserId != null">
        `EXTERNAL_USER_ID` = #{externalUserId,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        `MODIFIER` = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="addWay != null">
        `ADD_WAY` = #{addWay,jdbcType=VARCHAR},
      </if>
    </set>
    where `ID` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO">
    <!--@mbg.generated-->
    update `cm_wechat_cust_relation`
    set `CONSCODE` = #{conscode,jdbcType=VARCHAR},
      `STATUS` = #{status,jdbcType=VARCHAR},
      `ADD_TIME` = #{addTime,jdbcType=TIMESTAMP},
      `DEL_TIME` = #{delTime,jdbcType=TIMESTAMP},
      `CREATOR` = #{creator,jdbcType=VARCHAR},
      `CREATE_TIME` = #{createTime,jdbcType=TIMESTAMP},
      `UPDATE_TIME` = #{updateTime,jdbcType=TIMESTAMP},
      `STATE` = #{state,jdbcType=VARCHAR},
      `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR},
      `EXTERNAL_USER_ID` = #{externalUserId,jdbcType=VARCHAR},
      `MODIFIER` = #{modifier,jdbcType=VARCHAR},
      `ADD_WAY` = #{addWay,jdbcType=VARCHAR}
    where `ID` = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectRelationListByVo" parameterType="com.howbuy.crm.wechat.dao.vo.custrelation.CustConsultRelationVO"
          resultType="com.howbuy.crm.wechat.dao.po.custrelation.CustConsultRelationPO">
    select
    t.ID as id,
    t.CONSCODE as conscode,
    t.STATUS as status,
    t.ADD_TIME as addTime,
    t.DEL_TIME as delTime,
    t.STATUS as status,
    t.COMPANY_NO as companyNo,
    t.EXTERNAL_USER_ID as externalUserId,
    t.ADD_WAY as addWay,
    t.STATE as state,
    i.HBONE_NO as hboneNo,
    i.UNIONID as unionId
    from cm_wechat_cust_relation t
    left join cm_wechat_cust_info  i
    on t.EXTERNAL_USER_ID= i.EXTERNAL_USER_ID AND i.COMPANY_NO = t.COMPANY_NO
    <where>
        <!--排除 已删除的关系数据-->
        AND t.STATUS = '1'
        AND i.COMPANY_NO = #{companyNo,jdbcType=VARCHAR}
        AND
        <foreach collection="voList" item="vo" index="index" separator=" or " open="(" close=")">
          (
           i.HBONE_NO = #{vo.hboneNo,jdbcType=VARCHAR}
          and t.CONSCODE = #{vo.conscode,jdbcType=VARCHAR}
          )
        </foreach>
    </where>
  </select>

  <select id="getRealtionListByExternalUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from cm_wechat_cust_relation
    where STATUS = '1'
      and COMPANY_NO = #{companyNo,jdbcType=VARCHAR}
      AND EXTERNAL_USER_ID IN
    <foreach collection="externalIds" index="index" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    </select>

  <update id="updateCmWechatCustRelationDel">
    UPDATE CM_WECHAT_CUST_RELATION
    SET update_time= now(),
        status  = '4'
    WHERE EXTERNAL_USER_ID = #{info.externalUserId,jdbcType=VARCHAR}
      AND CONSCODE = #{info.conscode,jdbcType=VARCHAR}
      AND COMPANY_NO = #{info.companyNo,jdbcType=VARCHAR}
  </update>

</mapper>