<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.message.MessageSendInfoMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO">
    <!--@mbg.generated-->
    <!--@Table message_send_info-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="ACCEPT_ID" jdbcType="BIGINT" property="acceptId" />
    <result column="SEND_DT" jdbcType="VARCHAR" property="sendDt" />
    <result column="SEND_CHANNEL" jdbcType="VARCHAR" property="sendChannel" />
    <result column="TEMPLETE_ID" jdbcType="VARCHAR" property="templeteId" />
    <result column="TEMPLETE_PARAMS" jdbcType="VARCHAR" property="templeteParams" />
    <result column="SEND_STATUS" jdbcType="VARCHAR" property="sendStatus" />
    <result column="SEND_TIMES" jdbcType="INTEGER" property="sendTimes" />
    <result column="RESPONSE_CODE" jdbcType="VARCHAR" property="responseCode" />
    <result column="RESPONSE_MSG" jdbcType="VARCHAR" property="responseMsg" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="MESSAGE_UUID" jdbcType="VARCHAR" property="messageUUID" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ACCEPT_ID, SEND_DT,SEND_CHANNEL,  TEMPLETE_ID, TEMPLETE_PARAMS, SEND_STATUS, SEND_TIMES, RESPONSE_CODE,
    RESPONSE_MSG, CREATE_TIME, UPDATE_TIME,MESSAGE_UUID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from message_send_info
    where ID = #{id,jdbcType=BIGINT}
  </select>


  <select id="selectPageByVo" parameterType="com.howbuy.crm.wechat.dao.vo.message.MessageSendInfoVO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from message_send_info
    <where>
      <if test="id != null">
        and ID = #{id,jdbcType=BIGINT}
        </if>
      <if test="acceptId != null">
        and ACCEPT_ID = #{acceptId,jdbcType=BIGINT}
        </if>
      <if test="sendDt != null">
        and SEND_DT = #{sendDt,jdbcType=VARCHAR}
      </if>
      <if test="sendTimes != null">
        and SEND_TIMES <![CDATA[ < ]]> #{sendTimes,jdbcType=INTEGER}
      </if>
      <if test="sendChannelList != null and sendChannelList.size() > 0">
        and SEND_CHANNEL in
          <foreach collection="sendChannelList" item="item" index="index" open="(" separator="," close=")">
            #{item}
            </foreach>
      </if>
      <if test="sendStatusList != null and sendStatusList.size() != 0">
        and SEND_STATUS in
          <foreach collection="sendStatusList" item="item" index="index" open="(" separator="," close=")">
            #{item}
            </foreach>
      </if>
    </where>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from message_send_info
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO">
    <!--@mbg.generated-->
    insert into message_send_info (ACCEPT_ID, SEND_DT, SEND_CHANNEL,
      TEMPLETE_ID, TEMPLETE_PARAMS, SEND_STATUS, 
      SEND_TIMES, RESPONSE_CODE, RESPONSE_MSG, 
      CREATE_TIME, UPDATE_TIME)
    values (#{acceptId,jdbcType=BIGINT}, #{sendDt,jdbcType=VARCHAR},#{sendChannel,jdbcType=VARCHAR},
      #{templeteId,jdbcType=VARCHAR}, #{templeteParams,jdbcType=VARCHAR}, #{sendStatus,jdbcType=VARCHAR}, 
      #{sendTimes,jdbcType=INTEGER}, #{responseCode,jdbcType=VARCHAR}, #{responseMsg,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>

  <insert id="batchInsert" parameterType="list">
    <!--@mbg.generated-->
    insert into message_send_info (ACCEPT_ID, SEND_DT,SEND_CHANNEL,
      TEMPLETE_ID, TEMPLETE_PARAMS, SEND_STATUS,
      SEND_TIMES, RESPONSE_CODE, RESPONSE_MSG,
      CREATE_TIME, UPDATE_TIME)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.acceptId,jdbcType=BIGINT}, #{item.sendDt,jdbcType=VARCHAR},#{item.sendChannel,jdbcType=VARCHAR},
      #{item.templeteId,jdbcType=VARCHAR}, #{item.templeteParams,jdbcType=VARCHAR}, #{item.sendStatus,jdbcType=VARCHAR},
      #{item.sendTimes,jdbcType=INTEGER}, #{item.responseCode,jdbcType=VARCHAR}, #{item.responseMsg,jdbcType=VARCHAR},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>


  <insert id="insertSelective" parameterType="com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO">
    <!--@mbg.generated-->
    insert into message_send_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="acceptId != null">
        ACCEPT_ID,
      </if>
      <if test="sendDt != null">
        SEND_DT,
      </if>
      <if test="sendChannel != null and sendChannel != ''">
        SEND_CHANNEL,
      </if>
      <if test="templeteId != null">
        TEMPLETE_ID,
      </if>
      <if test="templeteParams != null">
        TEMPLETE_PARAMS,
      </if>
      <if test="sendStatus != null">
        SEND_STATUS,
      </if>
      <if test="sendTimes != null">
        SEND_TIMES,
      </if>
      <if test="responseCode != null">
        RESPONSE_CODE,
      </if>
      <if test="responseMsg != null">
        RESPONSE_MSG,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="acceptId != null">
        #{acceptId,jdbcType=BIGINT},
      </if>
      <if test="sendDt != null">
        #{sendDt,jdbcType=VARCHAR},
      </if>
      <if test="sendChannel != null and sendChannel != ''">
        #{sendChannel,jdbcType=VARCHAR},
      </if>
      <if test="templeteId != null">
        #{templeteId,jdbcType=VARCHAR},
      </if>
      <if test="templeteParams != null">
        #{templeteParams,jdbcType=VARCHAR},
      </if>
      <if test="sendStatus != null">
        #{sendStatus,jdbcType=VARCHAR},
      </if>
      <if test="sendTimes != null">
        #{sendTimes,jdbcType=INTEGER},
      </if>
      <if test="responseCode != null">
        #{responseCode,jdbcType=VARCHAR},
      </if>
      <if test="responseMsg != null">
        #{responseMsg,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO">
    <!--@mbg.generated-->
    update message_send_info
    <set>
      <if test="acceptId != null">
        ACCEPT_ID = #{acceptId,jdbcType=BIGINT},
      </if>
      <if test="sendDt != null">
        SEND_DT = #{sendDt,jdbcType=VARCHAR},
      </if>
      <if test="sendChannel != null and sendChannel != ''">
        SEND_CHANNEL= #{sendChannel,jdbcType=VARCHAR},
      </if>
      <if test="templeteId != null">
        TEMPLETE_ID = #{templeteId,jdbcType=VARCHAR},
      </if>
      <if test="templeteParams != null">
        TEMPLETE_PARAMS = #{templeteParams,jdbcType=VARCHAR},
      </if>
      <if test="sendStatus != null">
        SEND_STATUS = #{sendStatus,jdbcType=VARCHAR},
      </if>
      <if test="sendTimes != null">
        SEND_TIMES = #{sendTimes,jdbcType=INTEGER},
      </if>
      <if test="responseCode != null">
        RESPONSE_CODE = #{responseCode,jdbcType=VARCHAR},
      </if>
      <if test="responseMsg != null">
        RESPONSE_MSG = #{responseMsg,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO">
    <!--@mbg.generated-->
    update message_send_info
    set ACCEPT_ID = #{acceptId,jdbcType=BIGINT},
      SEND_DT = #{sendDt,jdbcType=VARCHAR},
      SEND_CHANNEL = #{sendChannel,jdbcType=VARCHAR},
      TEMPLETE_ID = #{templeteId,jdbcType=VARCHAR},
      TEMPLETE_PARAMS = #{templeteParams,jdbcType=VARCHAR},
      SEND_STATUS = #{sendStatus,jdbcType=VARCHAR},
      SEND_TIMES = #{sendTimes,jdbcType=INTEGER},
      RESPONSE_CODE = #{responseCode,jdbcType=VARCHAR},
      RESPONSE_MSG = #{responseMsg,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateSendStatus" parameterType="map">
    update message_send_info
    set
    SEND_STATUS = #{sendStatus,jdbcType=VARCHAR},
    SEND_TIMES = SEND_TIMES + 1,
    <if test="responseCode != null">
      RESPONSE_CODE = #{responseCode,jdbcType=VARCHAR},
    </if>
    <if test="responseMsg != null">
      RESPONSE_MSG = #{responseMsg,jdbcType=VARCHAR},
    </if>
    <if test="messageUUID != null">
      MESSAGE_UUID = #{messageUUID,jdbcType=VARCHAR},
    </if>
    UPDATE_TIME = sysdate()
    WHERE ID=#{id,jdbcType=BIGINT}
  </update>

  <select id="selectByAcceptId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from message_send_info
    where ACCEPT_ID = #{acceptId,jdbcType=BIGINT}
    </select>

  <update id="updateStatusForPushing" parameterType="map">
    update message_send_info
    set SEND_STATUS = #{sendStatus,jdbcType=VARCHAR},
    <if test="responseCode != null">
      RESPONSE_CODE = #{responseCode,jdbcType=VARCHAR},
    </if>
    <if test="responseMsg != null">
      RESPONSE_MSG = #{responseMsg,jdbcType=VARCHAR},
    </if>
    UPDATE_TIME = sysdate()
    WHERE ID = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectPageByVoAndSixHours" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from message_send_info
    <where>
      <if test="sendStatusList != null and sendStatusList.size() != 0">
        and SEND_STATUS in
        <foreach collection="sendStatusList" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      and CREATE_TIME >= NOW() - INTERVAL 6 HOUR
    </where>
  </select>
</mapper>