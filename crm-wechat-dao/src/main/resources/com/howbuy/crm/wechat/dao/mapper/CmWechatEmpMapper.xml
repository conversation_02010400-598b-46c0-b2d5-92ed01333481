<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.CmWechatEmpMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatEmpPO">
    <!--@mbg.generated-->
    <!--@Table `cm_wechat_emp`-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="EMP_ID" jdbcType="VARCHAR" property="empId" />
    <result column="EMP_NAME" jdbcType="VARCHAR" property="empName" />
    <result column="DEPT_ID" jdbcType="INTEGER" property="deptId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="CHAR" property="delFlag" />
    <result column="DEL_TIME" jdbcType="TIMESTAMP" property="delTime" />
    <result column="COMPANY_NO" jdbcType="VARCHAR" property="companyNo" />
    <result column="THUMB_AVATAR" jdbcType="VARCHAR" property="thumbAvatar" />
    <result column="AVATAR" jdbcType="VARCHAR" property="avatar" />
    <result column="QR_CODE" jdbcType="VARCHAR" property="qrCode" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="EMP_WORK_ID" jdbcType="VARCHAR" property="empWorkId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `ID`, `EMP_ID`, `EMP_NAME`, `DEPT_ID`, `CREATE_TIME`, `UPDATE_TIME`, `DEL_FLAG`, 
    `DEL_TIME`, `COMPANY_NO`, `EMP_WORK_ID`, `THUMB_AVATAR`, `AVATAR`, `QR_CODE`, `EMAIL`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from `cm_wechat_emp`
    where `ID` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from `cm_wechat_emp`
    where `ID` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatEmpPO">
    <!--@mbg.generated-->
    insert into `cm_wechat_emp` (`ID`, `EMP_ID`, `EMP_NAME`, 
      `DEPT_ID`, `CREATE_TIME`, `UPDATE_TIME`, 
      `DEL_FLAG`, `DEL_TIME`, `COMPANY_NO`, `EMP_WORK_ID`, `THUMB_AVATAR`, `AVATAR`, `QR_CODE`, `EMAIL`
      )
    values (#{id,jdbcType=BIGINT}, #{empId,jdbcType=VARCHAR}, #{empName,jdbcType=VARCHAR}, 
      #{deptId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{delFlag,jdbcType=CHAR}, #{delTime,jdbcType=TIMESTAMP}, #{companyNo,jdbcType=VARCHAR},
      #{empWorkId,jdbcType=VARCHAR}, #{thumbAvatar,jdbcType=VARCHAR},#{avatar,jdbcType=VARCHAR},
      #{qrCode,jdbcType=VARCHAR},#{email,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatEmpPO">
    <!--@mbg.generated-->
    insert into `cm_wechat_emp`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `ID`,
      </if>
      <if test="empId != null">
        `EMP_ID`,
      </if>
      <if test="empName != null">
        `EMP_NAME`,
      </if>
      <if test="deptId != null">
        `DEPT_ID`,
      </if>
      <if test="createTime != null">
        `CREATE_TIME`,
      </if>
      <if test="updateTime != null">
        `UPDATE_TIME`,
      </if>
      <if test="delFlag != null">
        `DEL_FLAG`,
      </if>
      <if test="delTime != null">
        `DEL_TIME`,
      </if>
      <if test="companyNo != null">
        `COMPANY_NO`,
      </if>
      <if test="empWorkId != null">
        `EMP_WORK_ID`,
      </if>
      <if test="thumbAvatar != null">
        `THUMB_AVATAR`,
      </if>
      <if test="avatar != null">
        `AVATAR`,
      </if>
      <if test="qrCode != null">
        `QR_CODE`,
      </if>
      <if test="email != null">
        `EMAIL`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=VARCHAR},
      </if>
      <if test="empName != null">
        #{empName,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
      <if test="delTime != null">
        #{delTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyNo != null">
        #{companyNo,jdbcType=VARCHAR},
      </if>
      <if test="empWorkId != null">
        #{empWorkId,jdbcType=VARCHAR},
      </if>
      <if test="thumbAvatar != null">
        #{thumbAvatar,jdbcType=VARCHAR},
      </if>
      <if test="avatar != null">
        #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="qrCode != null">
        #{qrCode,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatEmpPO">
    <!--@mbg.generated-->
    update `cm_wechat_emp`
    <set>
      <if test="empId != null">
        `EMP_ID` = #{empId,jdbcType=VARCHAR},
      </if>
      <if test="empName != null">
        `EMP_NAME` = #{empName,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        `DEPT_ID` = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        `CREATE_TIME` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `UPDATE_TIME` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delFlag != null">
        `DEL_FLAG` = #{delFlag,jdbcType=CHAR},
      </if>
      <if test="delTime != null">
        `DEL_TIME` = #{delTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyNo != null">
        `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR},
      </if>
      <if test="empWorkId != null">
        `EMP_WORK_ID` = #{empWorkId,jdbcType=VARCHAR},
      </if>
      <if test="thumbAvatar != null">
        `THUMB_AVATAR` = #{thumbAvatar,jdbcType=VARCHAR},
      </if>
      <if test="avatar != null">
        `AVATAR` = #{avatar,jdbcType=VARCHAR},
      </if>
      <if test="qrCode != null">
        `QR_CODE` = #{qrCode,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        `EMAIL` = #{email,jdbcType=VARCHAR},
      </if>
    </set>
    where `ID` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatEmpPO">
    <!--@mbg.generated-->
    update `cm_wechat_emp`
    set `EMP_ID` = #{empId,jdbcType=VARCHAR},
      `EMP_NAME` = #{empName,jdbcType=VARCHAR},
      `DEPT_ID` = #{deptId,jdbcType=INTEGER},
      `CREATE_TIME` = #{createTime,jdbcType=TIMESTAMP},
      `UPDATE_TIME` = #{updateTime,jdbcType=TIMESTAMP},
      `DEL_FLAG` = #{delFlag,jdbcType=CHAR},
      `DEL_TIME` = #{delTime,jdbcType=TIMESTAMP},
      `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR},
      `EMP_WORK_ID` = #{empWorkId,jdbcType=VARCHAR},
      `THUMB_AVATAR` = #{thumbAvatar,jdbcType=VARCHAR},
      `AVATAR` = #{avatar,jdbcType=VARCHAR},
      `QR_CODE` = #{qrCode,jdbcType=VARCHAR},
      `EMAIL` = #{email,jdbcType=VARCHAR}
    where `ID` = #{id,jdbcType=BIGINT}
  </update>

  <select id="getDeptInfoByEmpId" resultType="com.howbuy.crm.wechat.dao.bo.EmpDeptInfoBo">
    select emp.EMP_ID, emp.DEPT_ID, dept.DEPT_NAME
    from cm_wechat_emp emp
           left join cm_wechat_dept dept on emp.DEPT_ID = dept.DEPT_ID
    where emp.EMP_ID = #{empId,jdbcType=VARCHAR}
      and emp.COMPANY_NO = #{companyNo,jdbcType=VARCHAR}
      and emp.DEL_FLAG = '0'  limit 1
  </select>

  <select id="selectEmpIdByDeptList" resultType="java.lang.String">
    SELECT c.emp_id FROM cm_wechat_emp c
    where c.DEL_FLAG=0
    and c.COMPANY_NO=#{companyNo,jdbcType=VARCHAR}
    and c.dept_id in
    <foreach collection="deptIdList" item="deptId" index="index" open="(" separator="," close=")">
      #{deptId}
    </foreach>
  </select>

  <select id="selectAllEnabledEmpId" resultType="java.lang.String">
    SELECT c.emp_id FROM cm_wechat_emp c
    where c.DEL_FLAG=0
    and c.COMPANY_NO=#{companyNo,jdbcType=VARCHAR}
    and c.EMP_ID IS NOT NULL
    and c.EMP_ID != ''
  </select>

</mapper>