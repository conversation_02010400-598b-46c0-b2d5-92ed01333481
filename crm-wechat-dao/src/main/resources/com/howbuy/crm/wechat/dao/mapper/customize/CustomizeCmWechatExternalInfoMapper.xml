<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.customize.CustomizeCmWechatExternalInfoMapper">
    <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatExternalInfoPO">
        <!--@mbg.generated-->
        <!--@Table `cm_wechat_external_info`-->
        <result column="EXTERNAL_USER_ID" jdbcType="VARCHAR" property="externalUserId" />
        <result column="MSG_TYPE" jdbcType="VARCHAR" property="msgType" />
        <result column="CHANGE_TYPE" jdbcType="VARCHAR" property="changeType" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="EVENT" jdbcType="VARCHAR" property="event" />
        <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
        <result column="COMPANY_NO" jdbcType="VARCHAR" property="companyNo" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        `ID`, `EXTERNAL_USER_ID`, `MSG_TYPE`, `CHANGE_TYPE`, `CREATE_TIME`, `EVENT`, `UNIONID`,
        `COMPANY_NO`
    </sql>

    <select id="listCmWechatExternal" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from `cm_wechat_external_info`
        where `EXTERNAL_USER_ID` = #{externalUserID,jdbcType=VARCHAR}
          AND `CHANGE_TYPE` = #{changeType,jdbcType=VARCHAR}
          AND `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR}
    </select>
</mapper>