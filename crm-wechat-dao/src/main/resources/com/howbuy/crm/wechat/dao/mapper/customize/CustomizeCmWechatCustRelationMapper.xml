<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.customize.CustomizeCmWechatCustRelationMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO">
    <!--@mbg.generated-->
    <!--@Table `cm_wechat_cust_relation`-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="CONSCODE" jdbcType="VARCHAR" property="conscode" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="DEL_TIME" jdbcType="TIMESTAMP" property="delTime" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="STATE" jdbcType="VARCHAR" property="state" />
    <result column="COMPANY_NO" jdbcType="VARCHAR" property="companyNo" />
    <result column="EXTERNAL_USER_ID" jdbcType="VARCHAR" property="externalUserId" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="ADD_WAY" jdbcType="VARCHAR" property="addWay" />
    <result column="FIRST_ADD_TIME" jdbcType="TIMESTAMP" property="firstAddTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `ID`, `CONSCODE`, `STATUS`, `ADD_TIME`, `DEL_TIME`, `CREATOR`, `CREATE_TIME`, `UPDATE_TIME`,
    `STATE`, `COMPANY_NO`, `EXTERNAL_USER_ID`, `MODIFIER`, `ADD_WAY`, `FIRST_ADD_TIME`
  </sql>
  <select id="selectByExternalIdAndUserId" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from `cm_wechat_cust_relation`
    where `CONSCODE` = #{conscode,jdbcType=VARCHAR}
    AND `EXTERNAL_USER_ID` = #{externalUserId,jdbcType=VARCHAR}
    AND `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR}
  </select>

  <select id="selectByExternalIdAndUserIdsList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from cm_wechat_cust_relation
    where external_user_id = #{externalUserId,jdbcType=VARCHAR}
    and COMPANY_NO = #{companyNo,jdbcType=VARCHAR}
    <if test="consCodeList != null and consCodeList.size() > 0">
      and conscode in
      <foreach collection="consCodeList" item="conscode" open="(" close=")" separator=",">
        #{conscode}
      </foreach>
    </if>
    </select>

  <select id="selectByHbOneNoAndConscode" resultType="com.howbuy.crm.wechat.dao.bo.CmWechatCustRelationBO">
    SELECT (SELECT cwci.UNIONID
            FROM cm_wechat_cust_relation cwcr
            LEFT JOIN cm_wechat_cust_info cwci
            ON cwcr.EXTERNAL_USER_ID = cwci.EXTERNAL_USER_ID AND cwci.COMPANY_NO = cwcr.COMPANY_NO
            WHERE cwci.hbone_no = #{hbOneNo,jdbcType=VARCHAR}
              AND cwcr.CONSCODE = #{conscode,jdbcType=VARCHAR}
              AND cwci.COMPANY_NO = #{companyNo,jdbcType=VARCHAR}
              and cwcr.status = '1'
              LIMIT 1
          ) AS gnUnionId,

           (SELECT cwci.UNIONID
            FROM cm_wechat_cust_relation cwcr
            LEFT JOIN cm_wechat_cust_info cwci
            ON cwcr.EXTERNAL_USER_ID = cwci.EXTERNAL_USER_ID AND cwci.COMPANY_NO = cwcr.COMPANY_NO
            WHERE cwci.hk_hbone_no = #{hbOneNo,jdbcType=VARCHAR}
              AND cwcr.CONSCODE = #{conscode,jdbcType=VARCHAR}
              AND cwci.COMPANY_NO = #{companyNo,jdbcType=VARCHAR}
              and cwcr.status = '1'
              LIMIT 1
           ) AS hwUnionId
  </select>

  <resultMap id="resultMap" type="com.howbuy.crm.wechat.dao.bo.CmWechatCustRelationBO">
    <id column="hbOneNo" property="hbOneNo" jdbcType="VARCHAR"/>
    <id column="consCode" property="consCode" jdbcType="VARCHAR"/>
    <id column="gnUnionId" property="gnUnionId" jdbcType="VARCHAR"/>
    <id column="hwUnionId" property="hwUnionId" jdbcType="VARCHAR"/>
  </resultMap>
  <select id="selectByHbOneNoAndConscodeBatch"  resultMap="resultMap">
    SELECT
      vo.hboneNo as hbOneNo,
      vo.conscode as consCode,
      (SELECT cwci.UNIONID
       FROM cm_wechat_cust_relation cwcr
       LEFT JOIN cm_wechat_cust_info cwci
       ON cwcr.EXTERNAL_USER_ID = cwci.EXTERNAL_USER_ID AND cwci.COMPANY_NO = cwcr.COMPANY_NO
       WHERE cwci.hbone_no = vo.hboneNo
       AND cwcr.CONSCODE = vo.conscode
       AND cwci.COMPANY_NO = #{companyNo,jdbcType=VARCHAR}
       LIMIT 1
      ) AS gnUnionId,
      (SELECT cwci.UNIONID
       FROM cm_wechat_cust_relation cwcr
       LEFT JOIN cm_wechat_cust_info cwci
       ON cwcr.EXTERNAL_USER_ID = cwci.EXTERNAL_USER_ID AND cwci.COMPANY_NO = cwcr.COMPANY_NO
       WHERE cwci.hk_hbone_no = vo.hboneNo
       AND cwcr.CONSCODE = vo.conscode
       AND cwci.COMPANY_NO = #{companyNo,jdbcType=VARCHAR}
       LIMIT 1
      ) AS hwUnionId
    FROM (
      <foreach collection="voList" item="vo" separator="UNION ALL">
        SELECT 
          #{vo.hboneNo,jdbcType=VARCHAR} as hboneNo,
          #{vo.conscode,jdbcType=VARCHAR} as conscode
      </foreach>
    ) vo
  </select>

</mapper>