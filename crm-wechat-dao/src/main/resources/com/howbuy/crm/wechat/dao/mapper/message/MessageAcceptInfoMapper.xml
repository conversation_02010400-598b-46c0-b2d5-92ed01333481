<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.message.MessageAcceptInfoMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.message.MessageAcceptInfoPO">
    <!--@mbg.generated-->
    <!--@Table message_accept_info-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="UNIQUE_ID" jdbcType="VARCHAR" property="uniqueId" />
    <result column="MESSAGE_TYPE" jdbcType="VARCHAR" property="messageType" />
    <result column="MESSAGE_PARAMS" jdbcType="VARCHAR" property="messageParams" />
    <result column="MESSAGE_STATUS" jdbcType="VARCHAR" property="messageStatus" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="TEMPLATE_ID" jdbcType="VARCHAR" property="templateId" />
    <result column="TEMPLATE_PARAMS" jdbcType="VARCHAR" property="templateParams" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, UNIQUE_ID,MESSAGE_TYPE, MESSAGE_PARAMS, MESSAGE_STATUS, CREATE_TIME, UPDATE_TIME,TEMPLATE_ID,TEMPLATE_PARAMS
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from message_accept_info
    where ID = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByUniqueId" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from message_accept_info
    where
    UNIQUE_ID = #{uniqueId,jdbcType=VARCHAR}
    AND MESSAGE_TYPE = #{messageType,jdbcType=VARCHAR}
    </select>


  <select id="selectPageByVo" parameterType="com.howbuy.crm.wechat.dao.vo.message.MessageAcceptInfoVO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from message_accept_info
    <where>
      <if test="id !=null  ">
        AND ID = #{id,jdbcType=BIGINT}
      </if>
      <if test="uniqueId !=null  ">
        AND UNIQUE_ID = #{uniqueId,jdbcType=VARCHAR}
      </if>
      <if test="messageStatusList != null and messageStatusList.size() != 0  ">
        AND MESSAGE_STATUS in
        <foreach collection="messageStatusList" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="messageTypeList != null and messageTypeList.size() != 0  ">
        AND MESSAGE_TYPE in
        <foreach collection="messageTypeList" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from message_accept_info
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.wechat.dao.po.message.MessageAcceptInfoPO">
    <!--@mbg.generated-->
    insert into message_accept_info (UNIQUE_ID, MESSAGE_TYPE, MESSAGE_PARAMS,
      MESSAGE_STATUS, CREATE_TIME, UPDATE_TIME,TEMPLATE_ID,TEMPLATE_PARAMS
      )
    values (#{uniqueId,jdbcType=VARCHAR}, #{messageType,jdbcType=VARCHAR}, #{messageParams,jdbcType=VARCHAR},
      #{messageStatus,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
    ,#{templateId,jdbcType=VARCHAR},#{templateParams,jdbcType=VARCHAR}
      )
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.wechat.dao.po.message.MessageAcceptInfoPO">
    <!--@mbg.generated-->
    update message_accept_info
    <set>
      <if test="uniqueId != null">
        UNIQUE_ID = #{uniqueId,jdbcType=VARCHAR},
        </if>
      <if test="messageType != null">
        MESSAGE_TYPE = #{messageType,jdbcType=VARCHAR},
      </if>
      <if test="messageParams != null">
        MESSAGE_PARAMS = #{messageParams,jdbcType=VARCHAR},
      </if>
      <if test="messageStatus != null">
        MESSAGE_STATUS = #{messageStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="templateId != null">
        TEMPLATE_ID = #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="templateParams != null">
        TEMPLATE_PARAMS = #{templateParams,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.wechat.dao.po.message.MessageAcceptInfoPO">
    <!--@mbg.generated-->
    update message_accept_info
    set
      UNIQUE_ID = #{uniqueId,jdbcType=VARCHAR},
      MESSAGE_TYPE = #{messageType,jdbcType=VARCHAR},
      MESSAGE_PARAMS = #{messageParams,jdbcType=VARCHAR},
      MESSAGE_STATUS = #{messageStatus,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      TEMPLATE_ID = #{templateId,jdbcType=TIMESTAMP},
    TEMPLATE_PARAMS = #{templateParams,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=BIGINT}
  </update>

  <update id="batchUpdateStatus" parameterType="map">
    update message_accept_info
    set
    MESSAGE_STATUS = #{messageStatus,jdbcType=VARCHAR},
    UPDATE_TIME = sysdate()
    where ID in
    <foreach collection="idList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    </update>

  <select id="selectByTypeAndUniqueIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from message_accept_info
    where
    MESSAGE_TYPE = #{messageType,jdbcType=VARCHAR}
    AND UNIQUE_ID in
    <foreach collection="uniqueIdList" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
</mapper>