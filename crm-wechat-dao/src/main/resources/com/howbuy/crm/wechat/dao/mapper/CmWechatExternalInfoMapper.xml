<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.CmWechatExternalInfoMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatExternalInfoPO">
    <!--@mbg.generated-->
    <!--@Table `cm_wechat_external_info`-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="EXTERNAL_USER_ID" jdbcType="VARCHAR" property="externalUserId" />
    <result column="MSG_TYPE" jdbcType="VARCHAR" property="msgType" />
    <result column="CHANGE_TYPE" jdbcType="VARCHAR" property="changeType" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="EVENT" jdbcType="VARCHAR" property="event" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="COMPANY_NO" jdbcType="VARCHAR" property="companyNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `ID`, `EXTERNAL_USER_ID`, `MSG_TYPE`, `CHANGE_TYPE`, `CREATE_TIME`, `EVENT`, `UNIONID`, 
    `COMPANY_NO`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from `cm_wechat_external_info`
    where `ID` = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from `cm_wechat_external_info`
    where `ID` = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatExternalInfoPO">
    <!--@mbg.generated-->
    insert into `cm_wechat_external_info` (`ID`, `EXTERNAL_USER_ID`, `MSG_TYPE`, 
      `CHANGE_TYPE`, `CREATE_TIME`, `EVENT`, 
      `UNIONID`, `COMPANY_NO`)
    values (#{id,jdbcType=BIGINT}, #{externalUserId,jdbcType=VARCHAR}, #{msgType,jdbcType=VARCHAR}, 
      #{changeType,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{event,jdbcType=VARCHAR}, 
      #{unionid,jdbcType=VARCHAR}, #{companyNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatExternalInfoPO">
    <!--@mbg.generated-->
    insert into `cm_wechat_external_info`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `ID`,
      </if>
      <if test="externalUserId != null">
        `EXTERNAL_USER_ID`,
      </if>
      <if test="msgType != null">
        `MSG_TYPE`,
      </if>
      <if test="changeType != null">
        `CHANGE_TYPE`,
      </if>
      <if test="createTime != null">
        `CREATE_TIME`,
      </if>
      <if test="event != null">
        `EVENT`,
      </if>
      <if test="unionid != null">
        `UNIONID`,
      </if>
      <if test="companyNo != null">
        `COMPANY_NO`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="externalUserId != null">
        #{externalUserId,jdbcType=VARCHAR},
      </if>
      <if test="msgType != null">
        #{msgType,jdbcType=VARCHAR},
      </if>
      <if test="changeType != null">
        #{changeType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="event != null">
        #{event,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="companyNo != null">
        #{companyNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatExternalInfoPO">
    <!--@mbg.generated-->
    update `cm_wechat_external_info`
    <set>
      <if test="externalUserId != null">
        `EXTERNAL_USER_ID` = #{externalUserId,jdbcType=VARCHAR},
      </if>
      <if test="msgType != null">
        `MSG_TYPE` = #{msgType,jdbcType=VARCHAR},
      </if>
      <if test="changeType != null">
        `CHANGE_TYPE` = #{changeType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        `CREATE_TIME` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="event != null">
        `EVENT` = #{event,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        `UNIONID` = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="companyNo != null">
        `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR},
      </if>
    </set>
    where `ID` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatExternalInfoPO">
    <!--@mbg.generated-->
    update `cm_wechat_external_info`
    set `EXTERNAL_USER_ID` = #{externalUserId,jdbcType=VARCHAR},
      `MSG_TYPE` = #{msgType,jdbcType=VARCHAR},
      `CHANGE_TYPE` = #{changeType,jdbcType=VARCHAR},
      `CREATE_TIME` = #{createTime,jdbcType=TIMESTAMP},
      `EVENT` = #{event,jdbcType=VARCHAR},
      `UNIONID` = #{unionid,jdbcType=VARCHAR},
      `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR}
    where `ID` = #{id,jdbcType=BIGINT}
  </update>
</mapper>