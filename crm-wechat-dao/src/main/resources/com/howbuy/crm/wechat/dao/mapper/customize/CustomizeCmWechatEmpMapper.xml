<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.customize.CustomizeCmWechatEmpMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatEmpPO">
    <!--@mbg.generated-->
    <!--@Table `cm_wechat_emp`-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="EMP_ID" jdbcType="VARCHAR" property="empId" />
    <result column="EMP_NAME" jdbcType="VARCHAR" property="empName" />
    <result column="DEPT_ID" jdbcType="INTEGER" property="deptId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="CHAR" property="delFlag" />
    <result column="DEL_TIME" jdbcType="TIMESTAMP" property="delTime" />
    <result column="COMPANY_NO" jdbcType="VARCHAR" property="companyNo" />
    <result column="THUMB_AVATAR" jdbcType="VARCHAR" property="thumbAvatar" />
    <result column="AVATAR" jdbcType="VARCHAR" property="avatar" />
    <result column="QR_CODE" jdbcType="VARCHAR" property="qrCode" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="EMP_WORK_ID" jdbcType="VARCHAR" property="empWorkId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `ID`, `EMP_ID`, `EMP_NAME`, `DEPT_ID`, `CREATE_TIME`, `UPDATE_TIME`, `DEL_FLAG`,
    `DEL_TIME`, `COMPANY_NO`, `THUMB_AVATAR`, `AVATAR`, `QR_CODE`, `EMAIL`, `EMP_WORK_ID`
  </sql>

  <select id="listWechatEmpByEmpId" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from `cm_wechat_emp`
    where `EMP_ID` = #{empId,jdbcType=VARCHAR} AND `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR}
  </select>

  <select id="listAllWechatEmp" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from `cm_wechat_emp`
    where DEL_FLAG = '0'
    AND COMPANY_NO = #{companyNo,jdbcType=VARCHAR}
  </select>

  <select id="listAllWechatEmpId" parameterType="string" resultType="string">
    SELECT EMP_ID
    FROM CM_WECHAT_EMP
    WHERE DEL_FLAG = '0'
    AND COMPANY_NO = #{companyNo,jdbcType=VARCHAR}
  </select>
</mapper>