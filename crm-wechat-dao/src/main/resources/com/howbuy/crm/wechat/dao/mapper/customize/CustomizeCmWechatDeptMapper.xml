<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.customize.CustomizeCmWechatDeptMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatDeptPO">
    <!--@mbg.generated-->
    <!--@Table `cm_wechat_dept`-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="DEPT_ID" jdbcType="INTEGER" property="deptId" />
    <result column="DEPT_NAME" jdbcType="VARCHAR" property="deptName" />
    <result column="PARENT_DEPT_ID" jdbcType="INTEGER" property="parentDeptId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEL_FLAG" jdbcType="CHAR" property="delFlag" />
    <result column="DEL_TIME" jdbcType="TIMESTAMP" property="delTime" />
    <result column="COMPANY_NO" jdbcType="VARCHAR" property="companyNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `ID`, `DEPT_ID`, `DEPT_NAME`, `PARENT_DEPT_ID`, `CREATE_TIME`, `UPDATE_TIME`, `DEL_FLAG`,
    `DEL_TIME`, `COMPANY_NO`
  </sql>
  <select id="listWechatDeptByDeptId" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from `cm_wechat_dept`
    where `DEPT_ID` = #{deptId,jdbcType=INTEGER} AND `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR}
  </select>

  <select id="listAllWechatDept" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from `cm_wechat_dept`
    where `COMPANY_NO` = #{companyNo,jdbcType=VARCHAR}
  </select>

</mapper>