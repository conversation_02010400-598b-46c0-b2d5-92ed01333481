/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.outerservice.wechatapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.wechat.client.base.ReturnMessageDto;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.service.commom.enums.WechatApplicationTypeEnum;
import com.howbuy.crm.wechat.service.commom.exception.BusinessException;
import com.howbuy.crm.wechat.service.commom.utils.ApplicationUtilityDTO;
import com.howbuy.crm.wechat.service.commom.utils.CorpUtilityDTO;
import com.howbuy.crm.wechat.service.service.config.BaseConfigServce;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description: (基于companyNo -企业主体 的 企微outerService abstract处理类)
 * <AUTHOR>
 * @date 2025/7/16 19:14
 * @since JDK 1.8
 */
@Slf4j
public abstract class AbsCompanyBasedWechatOuterService {

//   业务成功code标识：         "errcode": 0,
     private static final String SUCCESS_CODE = "0";
//   业务成功msg标识：         "errmsg": "ok",
     private static final String SUCCESS_MSG = "ok";



    //NOTICE : 企微outerService abstract处理类
    //原因： 实际企微api底层调用， 依赖的是 WechatApplicationEnum .

    @Autowired
    private WeChatCommonOuterService weChatCommonOuterService;

    @Autowired
    private BaseConfigServce baseConfigServce;


    /**
     * @description:(根据[企微-企业主体]获取应用：[企业微信-客户联系])
     * @param companyNo	   企微-企业主体
     * @return com.howbuy.crm.wechat.client.enums.WechatApplicationEnum
     * @author: haoran.zhang
     * @date: 2025/7/16 19:16
     * @since JDK 1.8
     */
    public ApplicationUtilityDTO getCustApplicationEnum(String companyNo) {
        WechatApplicationTypeEnum typeEnum = WechatApplicationTypeEnum.CUSTOMER;
        ApplicationUtilityDTO utilityDTO = baseConfigServce.getApplicationUtilityByType(companyNo, typeEnum);
        if (utilityDTO==null){
            log.error("companyNo:{}, 无默认应用类型：{} Application指定！",companyNo,typeEnum.getDescription());
            throw new BusinessException(ResponseCodeEnum.SYS_ERROR);
        }
        return utilityDTO;
    }


    public ApplicationUtilityDTO buildApplicationByCompanyNo(String companyNo) {
        // 不再基于： 配置 企微-客户联系 应用， 在 企微-应用配置表中 。
//        WechatApplicationTypeEnum typeEnum = WechatApplicationTypeEnum.CUSTOMER;
//        ApplicationUtilityDTO utilityDTO = baseConfigServce.getApplicationUtilityByType(companyNo, typeEnum);
//        if (utilityDTO==null){
//            log.error("companyNo:{}, 无默认应用类型：{} Application指定！",companyNo,typeEnum.getDescription());
//            throw new BusinessException(ResponseCodeEnum.SYS_ERROR);
//        }
        CorpUtilityDTO corpUtilityDTO= baseConfigServce.getCorpUtilityDTO(companyNo);
        if (corpUtilityDTO==null){
            log.error("companyNo:{}, 未配置！",companyNo);
            throw new BusinessException(ResponseCodeEnum.SYS_ERROR);
        }
        if (StringUtils.isEmpty(corpUtilityDTO.getCustomerSecret())){
            log.error("companyNo:{}, 未配置[企微-客户联系]的secret",companyNo);
            throw new BusinessException(ResponseCodeEnum.SYS_ERROR);
        }

        //构建 utilityDTO . 基类使用
        ApplicationUtilityDTO utilityDTO = new ApplicationUtilityDTO();
        //应用信息
        WechatApplicationTypeEnum applyTypeEnum = WechatApplicationTypeEnum.CUSTOMER ;
        utilityDTO.setApplicationCode(String.join("_",
                corpUtilityDTO.getCompanyNo(),
                applyTypeEnum.getCode()));
        utilityDTO.setApplicationType(applyTypeEnum.getCode());
        utilityDTO.setAgentId(null);
        //[企业微信-内建应用-客户联系] 的 配置调整为 企业 配置
        utilityDTO.setAccessSecret(corpUtilityDTO.getCustomerSecret());

        //[企业微信-内建应用-客户联系]构建的缓存后缀名称 ：  companyNo_customer_envCode
        utilityDTO.setCacheSuffixName(String.join("_",
                corpUtilityDTO.getCompanyNo(),
                applyTypeEnum.getCode(),
                corpUtilityDTO.getEnvCode()));

        //补充该公司信息
        utilityDTO.setCompanyNo(corpUtilityDTO.getCompanyNo());
        utilityDTO.setCorpId(corpUtilityDTO.getCorpId());
        utilityDTO.setToken(corpUtilityDTO.getToken());
        utilityDTO.setEncodingAesKey(corpUtilityDTO.getEncodingAesKey());
        utilityDTO.setEnvCode(corpUtilityDTO.getEnvCode());
        utilityDTO.setCorpType(corpUtilityDTO.getCorpType());
        return utilityDTO;
    }


    /**
     * @param interactPath url相对路径
     * @param companyNo 企业微信corpNoEnum
     * @param paramMap     参数
     * @param method       参数 get  post
     * @return java.lang.String
     * @description:与微信服务端交互封装接口 该接口适用于通过corpId及默认的secret 历史代码适配处理
     * <AUTHOR>
     * @date 2024/9/19 13:42
     * @since JDK 1.8
     */
    public String requestWithString(String interactPath,
                                           String companyNo,
                                           Map paramMap,
                                           String method) {
        //NOTICE : 从 企微-应用 数据库配置， 获取 应用类型 = customer -客户联系  的 应用配置
        //该设计， 不再使用
//        ApplicationUtilityDTO utilityDTO=getCustApplicationEnum(companyNo);
        // 根据 companyNo 构建 ： 企微应用 对象 。
          ApplicationUtilityDTO utilityDTO=buildApplicationByCompanyNo(companyNo);
        return weChatCommonOuterService.requestWechatWithApplication(interactPath, utilityDTO, paramMap, method);
    }

    /**
     * 企微返回信息 是否成功对象
     * @param wxRespString 企微返回信息
     * @return ReturnMessageDto<String>
     */
    public ReturnMessageDto<String> isSuccess(String wxRespString) {
        if(StringUtils.isEmpty(wxRespString)){
            return ReturnMessageDto.fail("返回信息为空");
        }
        JSONObject jsonObject = JSON.parseObject(wxRespString);
        //目前只用 code 判断：0 成功，其他失败
        if(SUCCESS_CODE.equals(jsonObject.getString("errcode"))){
            return ReturnMessageDto.ok();
        }
        return ReturnMessageDto.fail(jsonObject.getString("errmsg"));
    }

    /**
     * @description: 解析JSON中的整数列表
     * @param jsonObject JSON对象
     * @param fieldName 字段名
     * @return List<Integer> 整数列表
     * <AUTHOR>
     * @date 2025/7/18
     * @since JDK 1.8
     */
    public static List<Integer> parseIntegerList(JSONObject jsonObject, String fieldName) {
        JSONArray jsonArray = jsonObject.getJSONArray(fieldName);
        if (jsonArray == null || jsonArray.isEmpty()) {
            return new ArrayList<>();
        }

        List<Integer> result = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            Object item = jsonArray.get(i);
            if (item instanceof Integer) {
                result.add((Integer) item);
            } else if (item instanceof String) {
                try {
                    result.add(Integer.parseInt((String) item));
                } catch (NumberFormatException e) {
                    log.warn("解析整数失败: {}", item);
                }
            }
        }
        return result;
    }

    /**
     * @description: 解析JSON中的字符串列表
     * @param jsonObject JSON对象
     * @param fieldName 字段名
     * @return List<String> 字符串列表
     * <AUTHOR>
     * @date 2025/7/18
     * @since JDK 1.8
     */
    public static List<String> parseStringList(JSONObject jsonObject, String fieldName) {
        JSONArray jsonArray = jsonObject.getJSONArray(fieldName);
        if (jsonArray == null || jsonArray.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> result = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            Object item = jsonArray.get(i);
            if (item != null) {
                result.add(item.toString());
            }
        }
        return result;
    }

}