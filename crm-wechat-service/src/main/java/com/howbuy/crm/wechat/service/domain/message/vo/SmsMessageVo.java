/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.domain.message.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (短信 发送消息 - 参数)
 * @date 2023/10/12 10:23
 * @since JDK 1.8
 */
@Data
public class SmsMessageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 接收人手机号密文
     */
    private String encryPhone;

    /**
     * 模板内容
     */
    private String templateParams;

}