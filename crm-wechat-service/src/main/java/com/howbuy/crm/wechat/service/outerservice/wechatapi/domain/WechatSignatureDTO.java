/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.outerservice.wechatapi.domain;

import lombok.Data;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/9/4 19:35
 * @since JDK 1.8
 */
@Data
public class WechatSignatureDTO {
    /**
     * 时间戳
     */
    private String timestamp;
    /**
     * 随机字符
     */
    private String nonceStr;
    /**
     * 签名
     */
    private String signature;

    /**
     * app-时间戳
     */
    private String appTimestamp;
    /**
     * app-随机字符
     */
    private String appNonceStr;
    /**
     * app-签名
     */
    private String appSignature;
}
