package com.howbuy.crm.wechat.service.repository;

import com.github.pagehelper.page.PageMethod;
import com.howbuy.crm.wechat.dao.bo.EmpDeptInfoBo;
import com.howbuy.crm.wechat.dao.mapper.CmWechatEmpChangeHisMapper;
import com.howbuy.crm.wechat.dao.mapper.CmWechatEmpMapper;
import com.howbuy.crm.wechat.dao.mapper.customize.CustomizeCmWechatEmpMapper;
import com.howbuy.crm.wechat.dao.po.CmWechatEmpChangeHisPO;
import com.howbuy.crm.wechat.dao.po.CmWechatEmpPO;
import com.howbuy.crm.wechat.service.commom.enums.ValidEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: crm-wechat 
 * @author: yu.zhang
 * @date: 2023/6/12 16:24 
 * @since JDK 1.8
 * @version: 1.0
 */
@Repository
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class CmWechatEmpRepository {

    @Autowired
    private CmWechatEmpMapper cmWechatEmpMapper;
    @Autowired
    private CustomizeCmWechatEmpMapper customizeCmWechatEmpMapper;
    @Autowired
    private CmWechatEmpChangeHisMapper cmWechatEmpChangeHisMapper;
    @Autowired
    private CommonRepository commonRepository;

    /**
     * @description:根据员工编号查询员工信息
     * @param empId
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatEmpPO>
     * @author: yu.zhang
     * @date: 2023/6/12 17:08
     * @since JDK 1.8
     */
    public List<CmWechatEmpPO> listWechatEmpByEmpId(String empId, String companyNo) {
        return customizeCmWechatEmpMapper.listWechatEmpByEmpId(empId ,companyNo);
    }

    /**
     * @description: 查询所有在职员工的emp_id
     * @return java.util.List<String>
     * @author: jin.wang03
     * @date: 2023/6/12 17:08
     * @since JDK 1.8
     */
    public List<String> listWechatEmpId(String companyNo) {
        Assert.notNull(companyNo, "企微-企业主体不能为空！");
        List<String> resultList = new ArrayList<>();
        int page = 1;
        while (true) {
            PageMethod.startPage(page, 1000);
            List<String> empIdList = customizeCmWechatEmpMapper.listAllWechatEmpId(companyNo);
            if (CollectionUtils.isEmpty(empIdList)) {
                break;
            }
            resultList.addAll(empIdList);
            page++;
        }
        return resultList;
    }

    /**
     * @description:新增企业微信员工
     * @param wechatEmp
     * @return int
     * @author: yu.zhang
     * @date: 2023/6/12 16:26
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int insertWechatEmp(CmWechatEmpPO wechatEmp) {
        wechatEmp.setId(commonRepository.getWechatEmpIdBySeq());
        wechatEmp.setDelFlag(ValidEnum.VALID.getKey());
        wechatEmp.setCreateTime(new Date());
        return cmWechatEmpMapper.insertSelective(wechatEmp);
    }

    /**
     * @description:修改部门员工记录
     * @param wechatEmp
     * @param newEmpId
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/12 17:02
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateWechatEmpByEmpId(CmWechatEmpPO wechatEmp, String newEmpId) {
        List<CmWechatEmpPO> wechatEmpList = this.listWechatEmpByEmpId(wechatEmp.getEmpId(),wechatEmp.getCompanyNo());

        if (CollectionUtils.isNotEmpty(wechatEmpList)) {
            wechatEmpList.forEach(cmWechatEmp -> {
                // 插入员工修改记录（修改了主部门才记修改记录）
                if (wechatEmp.getDeptId() != null) {
                    CmWechatEmpChangeHisPO wechatEmpChangeHis = new CmWechatEmpChangeHisPO();
                    wechatEmpChangeHis.setId(commonRepository.getWechatEmpIdBySeq());
                    wechatEmpChangeHis.setBeforeDeptId(cmWechatEmp.getDeptId());
                    wechatEmpChangeHis.setEmpId(wechatEmp.getEmpId());
                    wechatEmpChangeHis.setAfterDeptId(wechatEmp.getDeptId());
                    wechatEmpChangeHis.setChangeTime(new Date());
                    cmWechatEmpChangeHisMapper.insertSelective(wechatEmpChangeHis);
                }

                cmWechatEmp.setEmpId(newEmpId);
                cmWechatEmp.setEmpName(wechatEmp.getEmpName());
                cmWechatEmp.setDeptId(wechatEmp.getDeptId());
                this.updateWechatEmp(cmWechatEmp);
            });
        }
    }

    /**
     * @description:修改部门员工记录
     * @param wechatEmp
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/12 17:02
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateWechatEmp(CmWechatEmpPO wechatEmp) {
        wechatEmp.setUpdateTime(new Date());
        cmWechatEmpMapper.updateByPrimaryKeySelective(wechatEmp);
    }

    /**
     * @description:删除员工信息
     * @param empId
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/12 17:07
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void delWechatEmpByEmpId(String empId, String companyNo) {
        List<CmWechatEmpPO> wechatEmpList = this.listWechatEmpByEmpId(empId,companyNo);
        if (CollectionUtils.isNotEmpty(wechatEmpList)) {
            wechatEmpList.forEach(cmWechatEmp -> {
                cmWechatEmp.setDelTime(new Date());
                cmWechatEmp.setDelFlag(ValidEnum.INVALID.getKey());
                cmWechatEmpMapper.updateByPrimaryKey(cmWechatEmp);
            });
        }
    }

    /**
     * @description:批量将企业微信返回的员工列表数据与表中现有数据进行merge
     * @param wechatEmpList
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/27 9:17
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void batchMergeWechatEmp(List<CmWechatEmpPO> wechatEmpList) {
        if (CollectionUtils.isNotEmpty(wechatEmpList)) {
            wechatEmpList.forEach(wechatEmp -> {
                List<CmWechatEmpPO> cmWeChatEmpList = this.listWechatEmpByEmpId(wechatEmp.getEmpId(),wechatEmp.getCompanyNo());
                if (CollectionUtils.isNotEmpty(cmWeChatEmpList)) {
                    //按照删除状态分组
                    Map<String, List<CmWechatEmpPO>> mapList = cmWeChatEmpList.stream()
                            .collect(Collectors.groupingBy(CmWechatEmpPO::getDelFlag));
                    //如果有效的list有值且长度大于1 ,那就只保存并更新创建时间最大的那个,其他都delete
                    //如果有效的list有值且长度等于1,比较并更新
                    //如果有效的list 长度为0,插入
                    List<CmWechatEmpPO> cmWeChatEmpListValid = mapList.get(ValidEnum.VALID.getKey())==null?new ArrayList<>():mapList.get(ValidEnum.VALID.getKey());
                    int validSize = cmWeChatEmpListValid.size();
                    if(validSize==0){
                        this.insertWechatEmp(wechatEmp);
                    }else if(validSize==1){
                        CmWechatEmpPO cmWechatEmp = cmWeChatEmpListValid.get(0);
                        updateData(cmWechatEmp, wechatEmp);
                    }else if(validSize>1){
                        cmWeChatEmpListValid.sort(Comparator.comparing(CmWechatEmpPO::getCreateTime));
                        CmWechatEmpPO cmWechatEmp = cmWeChatEmpListValid.get(validSize-1);
                        cmWeChatEmpListValid.remove(cmWechatEmp);
                        cmWeChatEmpListValid.forEach(wechatEmpPO -> {
                            cmWechatEmpMapper.deleteByPrimaryKey(wechatEmpPO.getId());
                        });
                        updateData(cmWechatEmp, wechatEmp);
                    }
                } else {
                    //如果表里没有直接插
                    this.insertWechatEmp(wechatEmp);
                }
            });
        }
    }

    /**
     * @description:比对数据判断是否需要修改
     * @param cmWechatEmp 数据库中存储数据
     * @param wechatEmp  需要修改数据
     * @return java.lang.Boolean
     * @author: yu.zhang
     * @date: 2023/6/29 11:21
     * @since JDK 1.8
     */
    private Boolean needUpdate(CmWechatEmpPO cmWechatEmp,CmWechatEmpPO wechatEmp){
        return Objects.equals(ValidEnum.VALID.getKey(), cmWechatEmp.getDelFlag())
                && !cmWechatEmp.checkDataIsEqualsByMd5(wechatEmp);
    }

    /**
     * 修改数据逻辑
     * @param cmWechatEmp
     * @param wechatEmp
     */
    private void updateData(CmWechatEmpPO cmWechatEmp,CmWechatEmpPO wechatEmp){
        if (Boolean.TRUE.equals(needUpdate(cmWechatEmp,wechatEmp))) {
            cmWechatEmp.setEmpName(wechatEmp.getEmpName());
            cmWechatEmp.setDeptId(wechatEmp.getDeptId());
            cmWechatEmp.setEmpWorkId(wechatEmp.getEmpWorkId());
            cmWechatEmp.setThumbAvatar(wechatEmp.getThumbAvatar());
            cmWechatEmp.setAvatar(wechatEmp.getAvatar());
            cmWechatEmp.setQrCode(wechatEmp.getQrCode());
            cmWechatEmp.setEmail(wechatEmp.getEmail());
            this.updateWechatEmp(cmWechatEmp);
        }
    }

    /**
     * @description:逻辑删除未被及时更新的员工数据
     * @param companyNo	企业编码
     * @param fullDeptUserDetailList
     * @return int
     * @author: yu.zhang
     * @date: 2023/6/27 13:55
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void removeStaleWechatEmp(String companyNo,
                                     List<CmWechatEmpPO> fullDeptUserDetailList) {

        List<CmWechatEmpPO> cmWechatEmp = customizeCmWechatEmpMapper.listAllWechatEmp(companyNo);

        List<CmWechatEmpPO> removeWechat = cmWechatEmp.stream().filter(remove -> fullDeptUserDetailList.stream()
                        .noneMatch(all -> Objects.equals(remove.getEmpId(), all.getEmpId()) && Objects.equals(remove.getCompanyNo(), all.getCompanyNo())))
                .collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(removeWechat)){
            removeWechat.forEach(wechatEmp->{
                wechatEmp.setDelTime(new Date());
                wechatEmp.setDelFlag(ValidEnum.INVALID.getKey());
                cmWechatEmpMapper.updateByPrimaryKey(wechatEmp);
            });
        }
    }

    /**
     * @description: 根据员工编号查询员工信息
     * @param companyNo	企业编码
     * @param empId	员工userId
     * @return com.howbuy.crm.wechat.dao.bo.EmpDeptInfoBo 员工信息
     * @author: jin.wang03
     * @date: 2023/10/30 10:55
     * @since JDK 1.8
     */
    public EmpDeptInfoBo getDeptInfoByEmpId(String companyNo,String empId) {
        return cmWechatEmpMapper.getDeptInfoByEmpId(companyNo,empId);
    }
}
