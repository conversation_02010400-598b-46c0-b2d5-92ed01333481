/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.utils;

import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * @description: 日期工具类
 * <AUTHOR>
 * @date 2023/3/15 13:23
 * @since JDK 1.8
 */
public class DateUtils {
    private DateUtils(){}
    /**
     * yyyyMMdd
     */
    public static final String YYYYMMDD = "yyyyMMdd";
    /**
     * yyyy/MM
     */
    public static final String YYM = "yyyy/MM";
    /**
     * yyyy/MM/dd
     */
    public static final String YYMD = "yyyy/MM/dd";
    /**
     * yyyyMMddHHmmss
     */
    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    /**
     * yyyy-MM-dd HH:mm:ss
     */
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    /**
     * yyyy-MM-dd HH:mm:ss.SSS
     */
    public static final String YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss.SSS";
    /**
     * HHmmss
     */
    public static final String HHMMSS = "HHmmss";

    /**
     * @description: 日期格式化
     * @param date	LocalDate类型的日期
     * @param pattern	需要格式化的格式
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2023/3/15 13:28
     * @since JDK 1.8
     */
    public static String formatToString(LocalDateTime date, String pattern){
        if (date == null || StringUtils.isEmpty(pattern)) {
            return null;
        }

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(pattern);
        return date.format(dtf);
    }

    /**
     * @description:将短时间格式字符串转换为时间 YYYYMMDD
     * @param strDate
     * @return java.util.Date
     * @author: yu.zhang
     * @date: 2023/4/28 19:10
     * @since JDK 1.8
     */
    public static Date strToDate(String strDate) {
        SimpleDateFormat formatter = new SimpleDateFormat(YYYYMMDD);
        ParsePosition pos = new ParsePosition(0);
        return formatter.parse(strDate, pos);
    }
    /**
     * @param date
     * @param pattern
     * @return java.lang.String
     * @description:java.util.Date日期格式转换
     * @author: yu.zhang
     * @date: 2023/3/21 13:39
     * @since JDK 1.8
     */
    public static String dateFormatToString(Date date, String pattern){
        if (date == null || StringUtils.isEmpty(pattern)) {
            return null;
        }
        LocalDateTime ldt = date.toInstant()
                .atZone( ZoneId.systemDefault() )
                .toLocalDateTime();

        return formatToString(ldt,pattern);
    }

    /**
     * @description:计算时间区间相差的天数
     * @param startDate
     * @param endDate
     * @return java.lang.String
     * @author: yu.zhang
     * @date: 2023/4/19 17:17
     * @since JDK 1.8
     */
    public static String standardDeviation(String startDate, String endDate) {
        DateFormat dft = new SimpleDateFormat(YYYYMMDD);

        try {
            Date star = dft.parse(startDate);
            Date endDay=dft.parse(endDate);
            Long starTime=star.getTime();
            Long endTime=endDay.getTime();
            //时间戳相差的毫秒数
            Long num=endTime-starTime;
            return String.valueOf(num/24/60/60/1000);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }
    /**
     * @description:(获取当前时间)
     * @param format
     * @return java.lang.String
     * @author: shuai.zhang
     * @date: 2023/6/5 10:57
     * @since JDK 1.8
     */
    public static String getCurrentDate(String format) {
        return formatToString(LocalDateTime.now(),format);
    }

    /**
     * getStrNextMonthByDate:获取指定增加月份的日期
     *
     * @param d
     *            时间
     * @param month
     *            增加月份
     * @return yyyyMMdd
     * <AUTHOR>
     * @date 2016-9-20 下午5:28:36
     */
    public static String getStrNextMonthByDate(Date d, int month) {
        Calendar cal = Calendar.getInstance();
        if (d != null) {
            cal.setTime(d);
        }
        cal.add(Calendar.MONTH, month);
        SimpleDateFormat sdf = new SimpleDateFormat(YYYYMMDD);
        return sdf.format(cal.getTime());
    }
}