package com.howbuy.crm.wechat.service.outerservice.wechatapi.sendwechat;

import cn.hutool.http.HttpUtil;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.commom.utils.ApplicationUtilityDTO;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WeChatCommonOuterService;
import com.howbuy.crm.wechat.service.service.config.BaseConfigServce;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Map;

/**
 * @classname: SendMsgServer
 * @author: yu.zhang
 * @description: 发送企业微信消息接口
 * @creatdate: 2021-02-10 11:00
 * @since: JDK1.8
 */
@Slf4j
@Service
@Transactional
public class WechatMessageSendOuterServer {

    @Autowired
    private WeChatCommonOuterService weChatCommonOuterService;

    @Autowired
    private BaseConfigServce baseConfigServce;


    /**
     * @description:(请在此添加描述)
     * @param paramMap 消息体
     * @param applicationCode 发送消息的内建应用code
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/27 14:59
     * @since JDK 1.8
     */
    public void sendMsgByApplicationCode(Map<String, Object> paramMap,
                                         String applicationCode) {
        ApplicationUtilityDTO utilityDTO= baseConfigServce.getApplicationUtilityDTO(applicationCode);
        sendMsgByApplication(paramMap, utilityDTO);
    }

    /**
     * @description:(请在此添加描述)
     * @param paramMap 消息体
     * @param utilityDTO  企业微信-企业应用-组件DTO
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/27 14:59
     * @since JDK 1.8
     */
    private void sendMsgByApplication(Map<String, Object> paramMap, ApplicationUtilityDTO utilityDTO) {
        Assert.notNull(utilityDTO, "企业微信-企业应用-组件DTO不能为空");
        log.info("companyNo:{}, applicationCode:{} ,发送消息sendMsgByType开始:{}" ,
                utilityDTO.getCompanyNo(),
                utilityDTO.getApplicationCode(),
                paramMap);
        try {

            // 使用正确的AgentId而不是CorpId
            paramMap.put("agentid", utilityDTO.getAgentId());

            paramMap.put("enable_duplicate_check", "0");
            paramMap.put("duplicate_check_interval", "30");

            String token = weChatCommonOuterService.getAccessToken(utilityDTO);

            StringBuilder sendUrl = new StringBuilder();
            sendUrl.append(Constants.INTERACT_DOMAIN_URL).append(String.format(Constants.MSG_PATH, token));

            String resp = HttpUtil.post(sendUrl.toString(), paramMap);
            log.info("获取到的token:{},请求数据:{},发送微信的响应数据:{}", token, paramMap, resp);
        } catch (Exception e) {
            log.error("发送消息sendMsgByType Exception:{}", e.getMessage());
        }
    }

}
