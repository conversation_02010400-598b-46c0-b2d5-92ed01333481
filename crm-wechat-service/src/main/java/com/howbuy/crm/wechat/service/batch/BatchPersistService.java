package com.howbuy.crm.wechat.service.batch;

import java.util.List;

/**
 * @description: 批量操作执行器，提供批量操作的基础方法，业务方只需要实现对应的接口即可，然后调用对应的方法即可
 * <AUTHOR>
 * @date 2023/10/7 9:59
 * @since JDK 1.8
 */
public interface BatchPersistService<Po> {

    /**
     * 每个批次执行{@link com.howbuy.crm.wechat.service.commom.constant.Constants#BATCH_COUNT}条，
     * 即为 dataList集合最大条数
     */
    int doBusiness(List<Po> dataList);
}
