/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.constant;

/**
 * @description: CRM账户服务接口路径常量类，用于存放CRM账户服务接口路径常量
 * <AUTHOR>
 * @date 2023/12/14 13:39
 * @since JDK 1.8
 */
public class CrmAccountPathConstant {


    /**
     * 查询所有需要刷新客户关系的投顾企微账号
     */
    public static final String GET_ALL_NEEDREFRESH_WECHATCONSCODE = "/consultantinfo/getallneedrefreshwechatconscode";
    /**
     * 根据投顾codelist查询投顾企微账号
     */
    public static final String GET_WECHATCONSCODES_BYCONSCODES = "/consultantinfo/getwechatconscodesbyconscodes";


}