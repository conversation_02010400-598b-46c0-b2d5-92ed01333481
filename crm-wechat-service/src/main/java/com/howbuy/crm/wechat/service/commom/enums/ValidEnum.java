package com.howbuy.crm.wechat.service.commom.enums;

import java.util.stream.Stream;

/**
 * @description: 数据有效无效枚举
 * <AUTHOR>
 * @date 2023/3/8 17:11
 * @since JDK 1.8
 */
public enum ValidEnum {

    INVALID("1", "无效"),
    VALID("0", "有效");

    private final String key;
    private final String desc;

    public static ValidEnum getValidEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        ValidEnum radarEnum = getValidEnum(code);
        return radarEnum == null ? null : radarEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    ValidEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}