/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.enums;

/**
 * @description: 调度任务返回结果
 * <AUTHOR>
 * @date 2023/3/8 17:11
 * @since JDK 1.8
 */
public enum QuartzResultEnum {
    /**
     * 0-超时
     */
    TIMEOUT(0, "超时"),

    /**
     * 1-成功
     */
    SUCCESS(1, "成功"),

    /**
     * 2-失败
     */
    FAIL(2, "失败");

    /**
     * 代码
     */
    private final int code;

    /**
     * 描述
     */
    private final String desc;

    QuartzResultEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
