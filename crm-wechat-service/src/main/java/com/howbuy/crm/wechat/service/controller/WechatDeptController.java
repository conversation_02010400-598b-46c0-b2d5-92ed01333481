package com.howbuy.crm.wechat.service.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.howbuy.crm.wechat.service.service.WechatFullDeptDataScheduleService;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by shucheng on 2022/1/19 11:12
 */
@Slf4j
@Controller
@RequestMapping("/wechatdept")
public class WechatDeptController {

    @Autowired
    private WechatFullDeptDataScheduleService wechatFullDeptDataScheduleService;


    /**
     * @api {GET} /wechatdept/execute executeScheduleService()
     * @apiVersion 1.0.0
     * @apiGroup WechatDeptController
     * @apiName executeScheduleService()
     * @apiDescription 手动执行任务（产线使用compareInsertAll）
     * @apiSuccess (响应结果) {String} response
     * @apiSuccessExample 响应结果示例
     * "jVOxpVGaVl"
     */
    @ResponseBody
    @GetMapping("/execute")
    public String executeScheduleService(String companyNos) {

//        List<String> companyNoEnumList = Lists.newArrayList(Constants.COMPANY_NO_HOWBUY_FUND, Constants.DEFAULT_COMPANY_NO);
        //companyNos ： 1,2
        if(StringUtil.isNotNullStr(companyNos)){
            List<String> companyNoList=
                    Arrays.stream(companyNos.split(","))
                            .map(String::trim)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
            wechatFullDeptDataScheduleService.executeSync(companyNoList);
        }

        return "success";
    }
}
