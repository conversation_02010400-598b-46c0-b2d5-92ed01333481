package com.howbuy.crm.wechat.service.controller;

import com.howbuy.crm.wechat.client.base.ReturnMessageDto;
import com.howbuy.crm.wechat.service.domain.message.QueryMessageSendStatusDTO;
import com.howbuy.crm.wechat.service.domain.message.vo.QueryMessageSendStatusVO;
import com.howbuy.crm.wechat.service.service.SendWechatServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 消息发送controller,用于接收对应发送消息的http接口
 * @classname: SendWX
 * @author: yu.zhang
 * @creatdate: 2021-01-20 11:29
 * @since: JDK1.8
 */
@Slf4j
@RestController
@Validated
@RequestMapping("/wechatmsgsend")
public class SendWechatController {

    @Autowired
    private SendWechatServer sendWechatServer;

    /**
     * @api {GET} /wechatmsgsend/sendvoiceremind sendVoiceRemind()
     * @apiVersion 1.0.0
     * @apiGroup SendWechatController
     * @apiName sendVoiceRemind()
     * @apiParam (请求参数) {String} type
     * @apiParam (请求参数) {String} userid
     * @apiParam (请求参数) {String} mobile
     * @apiParam (请求参数) {String} date
     * @apiParamExample 请求参数示例
     * date=dPmUwO&mobile=B&type=iOzP&userid=t
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * null
     */
    @GetMapping("/sendvoiceremind")
    public void sendVoiceRemind(String type, String userid, String mobile, String date) {
        log.info("send开始:" + userid);
        sendWechatServer.sendVoiceRemindMsgByType(type, userid, mobile, date);
    }


    /**
     * @api {POST} /wechatmsgsend/querysendstatus querySendStatus()
     * @apiVersion 1.0.0
     * @apiGroup SendWechatController
     * @apiName querySendStatus()
     * @apiDescription 查询消息的发送状态
     * @apiParam (请求体) {String} messageType 消息类型
     * @apiParam (请求体) {Array} uniqueIdList 业务uniquedId列表
     * @apiParamExample 请求体示例
     * {"messageType":"zdHVY7","uniqueIdList":["L9n20"]}
     * @apiSuccess (响应结果) {String} returnCode 返回代码
     * @apiSuccess (响应结果) {String} returnMsg 返回信息体
     * @apiSuccess (响应结果) {Object} returnObject 自定义返回对象
     * @apiSuccess (响应结果) {Array} returnList 返回列表
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"kgP","returnMsg":"2Z5","returnObject":{},"returnList":[{}]}
     */
    @PostMapping("/querysendstatus")
    public ReturnMessageDto<Map<String, String>> querySendStatus(@RequestBody QueryMessageSendStatusDTO dto) {
        return sendWechatServer.querySendStatus(dto.getMessageType(), dto.getUniqueIdList());
    }


    /**
     * @api {POST} /wechatmsgsend/querysendstatuswithmemo querySendStatusWithMemo()
     * @apiVersion 1.0.0
     * @apiGroup SendWechatController
     * @apiName querySendStatusWithMemo()
     * @apiDescription 查询消息发送状态及备注
     * @apiParam (请求体) {String} messageType 消息类型
     * @apiParam (请求体) {Array} uniqueIdList 业务uniquedId列表
     * @apiParamExample 请求体示例
     * {"messageType":"F","uniqueIdList":["Jw"]}
     * @apiSuccess (响应结果) {String} returnCode 返回代码
     * @apiSuccess (响应结果) {String} returnMsg 返回信息体
     * @apiSuccess (响应结果) {Object} returnObject 自定义返回对象
     * @apiSuccess (响应结果) {Array} returnObject.resultList
     * @apiSuccess (响应结果) {String} returnObject.resultList.uniqueId 唯一标识
     * @apiSuccess (响应结果) {String} returnObject.resultList.sendStatus 发送状态
     * @apiSuccess (响应结果) {String} returnObject.resultList.memo 备注
     * @apiSuccess (响应结果) {Array} returnList 返回列表
     * @apiSuccess (响应结果) {Array} returnList.resultList
     * @apiSuccess (响应结果) {String} returnList.resultList.uniqueId 唯一标识
     * @apiSuccess (响应结果) {String} returnList.resultList.sendStatus 发送状态
     * @apiSuccess (响应结果) {String} returnList.resultList.memo 备注
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"OQgk7W","returnMsg":"2L","returnObject":{"resultList":[{"memo":"e5pBPK","sendStatus":"DLMJ","uniqueId":"uIfEcXkR"}]},"returnList":[{"resultList":[{"memo":"UyZ","sendStatus":"6yq8iOQ","uniqueId":"SAI6UTk9"}]}]}
     */
    @PostMapping("/querysendstatuswithmemo")
    public ReturnMessageDto<QueryMessageSendStatusVO> querySendStatusWithMemo(@RequestBody QueryMessageSendStatusDTO dto) {
        return sendWechatServer.querySendStatusWithMemo(dto.getMessageType(), dto.getUniqueIdList());
    }


    /**
     * @api {GET} /wechatmsgsend/sendMessageByIds sendMessageByIds()
     * @apiVersion 1.0.0
     * @apiGroup SendWechatController
     * @apiName sendMessageByIds()
     * @apiDescription 根据指定的send_ids，手动重发消息（补偿逻辑）
     * @apiParam (请求参数) {String} ids
     * @apiParamExample 请求参数示例
     * ids=8AUCSP28Ur
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * null
     */
    @GetMapping("/sendMessageByIds")
    public void sendMessageByIds(@RequestParam String ids) {
        sendWechatServer.sendMessageByIds(ids);
    }


    /**
     * @api {GET} /wechatmsgsend/buildMessageByIds buildMessageByIds()
     * @apiVersion 1.0.0
     * @apiGroup SendWechatController
     * @apiName buildMessageByIds()
     * @apiDescription 根据指定的send_ids，手动构建消息（补偿逻辑）
     * @apiParam (请求参数) {String} ids
     * @apiParamExample 请求参数示例
     * ids=9T8sGHIKUe
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * null
     */
    @GetMapping("/buildMessageByIds")
    public void buildMessageByIds(@RequestParam String ids) {
        sendWechatServer.buildMessageByIds(ids);
    }

}