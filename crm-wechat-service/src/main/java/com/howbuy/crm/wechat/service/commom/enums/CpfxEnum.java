/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.enums;

/**
 * <AUTHOR>
 * @description: 产品分销渠道-枚举值
 * @date 2023/3/8 17:11
 * @since JDK 1.8
 */
public enum CpfxEnum {

    /**
     * 1-好买
     */
    HB("1", "好买"),


    /**
     * 2-好臻
     */
    HZ("2", "好臻"),

    /**
     * 3-海外
     */
    HW("3", "海外"),
    ;

    /**
     * 代码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    CpfxEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code直接返回 整个枚举类型
     *
     * @param code 系统返回参数编码
     * @return PreOccupyTypeEnum
     */
    public static CpfxEnum getEnum(String code) {
        for (CpfxEnum statusEnum : CpfxEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
