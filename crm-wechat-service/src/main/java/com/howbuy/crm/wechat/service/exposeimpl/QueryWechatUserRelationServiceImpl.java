/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.exposeimpl;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.producer.userrelation.QueryWechatUserRelationService;
import com.howbuy.crm.wechat.client.producer.userrelation.request.QueryWechatUserRelationRequest;
import com.howbuy.crm.wechat.client.producer.userrelation.response.QueryWechatUserRelationResponse;
import com.howbuy.crm.wechat.service.service.custrelation.WechatCustRelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @description:  根据一账通号查询客户与企业微信用户关系服务实现类
 * <AUTHOR>
 * @date 2024/8/30 18:01
 * @since JDK 1.8
 */
@DubboService
@Slf4j
public class QueryWechatUserRelationServiceImpl implements QueryWechatUserRelationService {

    @Autowired
    private WechatCustRelationService wechatCustRelationService;

    @Override
    public Response<QueryWechatUserRelationResponse> execute(QueryWechatUserRelationRequest queryWechatUserRelationRequest) {
        return wechatCustRelationService.queryWechatUserRelation(queryWechatUserRelationRequest);
    }
}