/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.outerservice.crm;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.wechat.service.commom.utils.RestTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;

/**
 * @description:(crm-account  abstract调用服务类)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2024/1/16 10:57
 * @since JDK 1.8
 */
@Slf4j
@Service
public class AbstractCrmAccountOuterService {

    @Value("${GLOBAL_CRM_ACCOUNT}")
    private String crmAccountPath;

    private static RestTemplate restTemplate;


    @Value("${CRM_ACCOUNT_SERVER_TIMEOUT}")
    private String timeout;


    @PostConstruct
    public void init() {
        restTemplate = new RestTemplate(RestTemplateUtil.getClientHttpRequestFactory(Integer.parseInt(timeout)));
        
    }


    /**
     * @description:(post 提交请求 处理)
     * @param path	
     * @param jsonObject	
     * @param responseType
     * @return T
     * @author: haoran.zhang
     * @date: 2024/1/16 11:09
     * @since JDK 1.8
     */
    public <T, V> T postEntity(String path, V jsonObject, ParameterizedTypeReference<T> responseType) {
        String fullUrl = crmAccountPath + path;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<V> httpEntity = new HttpEntity<>(jsonObject, headers);
        ResponseEntity<T> response = restTemplate.exchange(fullUrl, HttpMethod.POST, httpEntity, responseType);
        log.info("POST请求crm_account_remote.url:{},param:{},response:{}", path, JSON.toJSONString(jsonObject), JSON.toJSONString(response));
        if (HttpStatus.OK.equals(response.getStatusCode())) {
            return response.getBody();
        }
        return null;
    }

    public <T, V> T getEntity(String path, ParameterizedTypeReference<T> responseType) {
        String fullUrl = crmAccountPath + path;
        ResponseEntity<T> response = restTemplate.exchange(fullUrl, HttpMethod.GET, null, responseType);
        log.info("GET请求crm_account_remote.url:{},response:{}", path, JSON.toJSONString(response));
        if (HttpStatus.OK.equals(response.getStatusCode())) {
            return response.getBody();
        }
        return null;
    }

}