package com.howbuy.crm.wechat.service.outerservice.wechatapi.domain;

import com.howbuy.crm.wechat.service.commom.constant.Constants;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 企业微信API返回通用DTO
 * @date 2025-06-18 16:06:07
 * @since JDK 1.8
 */
@Data
public class ExternalWeChatBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 返回码
     */
    private String errcode;

    /**
     * 对返回码的文本描述内容
     */
    private String errmsg;


    /**
     * 企微调用，是否成功
     * @return true: 成功 false: 失败
     */
    public boolean isSuccess() {
        return Constants.SUCCESS_CODE.equals(errcode);
    }

}