/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.job;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.wechat.client.enums.AcceptMessageStatusEnum;
import com.howbuy.crm.wechat.client.enums.AcceptMessageTypeEnum;
import com.howbuy.crm.wechat.dao.po.message.MessageAcceptInfoPO;
import com.howbuy.crm.wechat.service.domain.message.AcceptMessageJobDTO;
import com.howbuy.crm.wechat.service.service.message.MessageAcceptInfoService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.empire.commons.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * @description: (发送消息 任务消息接收处理器)
 * <AUTHOR>
 * @date 2023/10/7 9:59
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CrmAcceptMessageJob extends AbstractCommonMessageJob {

    /**
     * crm发送消息 接收消息队列
     */
    @Value("${crm_accept_message_channel}")
    private String crmAcceptMessageChannel;

    @Autowired
    private MessageAcceptInfoService messageAcceptInfoService;

    public CrmAcceptMessageJob() {
    }

    @Override
    protected String getQuartMessageChannel() {
        return crmAcceptMessageChannel;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        //消息格式：{"type":"","params":"","uniqueId":"","hbOneNo":""}
        log.info("[CrmAcceptMessageJob]接收消息:{}", message);
        AcceptMessageJobDTO acceptMessageJobDTO = JSON.parseObject((String) message.getContent(), AcceptMessageJobDTO.class);
        log.info("[CrmAcceptMessageJob]接收消息转化为dto:{}", acceptMessageJobDTO);
        //校验type-消息类型
        AcceptMessageTypeEnum messageTypeEnum = AcceptMessageTypeEnum.getEnum(acceptMessageJobDTO.getType());
        if (messageTypeEnum == null) {
            log.error("消息类型不存在：{}", JSON.toJSONString(acceptMessageJobDTO));
            return;
        }
        //注意：uniqueId 在数据库保证唯一 。 uniqueId 外部如果为空， 赋值 uuid .
        String uniqueId = acceptMessageJobDTO.getUniqueId();
        //uniqueId 外部赋值，标识 业务唯一标识，校验重复后，该消息不落库。忽略处理
        if (StringUtils.isNotEmpty(uniqueId)) {
            MessageAcceptInfoPO existsPo = messageAcceptInfoService.selectByUniqueId(messageTypeEnum.getCode(), uniqueId);
            //消息已存在， 入库， 标记为：无需处理 。忽略处理
            if (existsPo != null) {
                log.warn("消息已存在，忽略处理：{}", acceptMessageJobDTO);
                return;
            }
        } else {
            uniqueId = UUID.randomUUID().toString();
        }

        //校验： hbOneNo   暂不处理
        MessageAcceptInfoPO acceptInfo = new MessageAcceptInfoPO();
        acceptInfo.setUniqueId(uniqueId);
        acceptInfo.setMessageParams(acceptMessageJobDTO.getParams());
        acceptInfo.setMessageType(messageTypeEnum.getCode());
        acceptInfo.setTemplateId(acceptMessageJobDTO.getTemplateId());
        acceptInfo.setTemplateParams(acceptMessageJobDTO.getTemplateParams());
        acceptInfo.setMessageStatus(AcceptMessageStatusEnum.UNPROCESSED.getCode());
        messageAcceptInfoService.insert(acceptInfo);
    }
}