package com.howbuy.crm.wechat.service.domain.message;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description:香港账户绑定消息
 * @Author: yun.lu
 * Date: 2025/5/22 13:58
 */
@Data
public class HkAcctBindMessageDTO implements Serializable {
    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * OUTER_ACCT - 外部账号
     */
    private String outerAcct;

    /**
     * OUTER_SYS_TYPE - 外部系统类型
     */
    private String outerSysType;

    /**
     * BIND_STATUS - 绑定状态:0-已绑定 1-已解绑
     */
    private String bindStatus;



}
