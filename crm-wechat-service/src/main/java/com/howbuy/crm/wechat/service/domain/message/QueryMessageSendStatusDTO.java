/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.domain.message;

import lombok.Data;

import java.util.List;

/**
 * @description: 查询推送消息的发送状态
 * <AUTHOR>
 * @date 2024/3/22 16:19
 * @since JDK 1.8
 */

@Data
public class QueryMessageSendStatusDTO {

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 业务uniquedId列表
     */
    private List<String> uniqueIdList;

}