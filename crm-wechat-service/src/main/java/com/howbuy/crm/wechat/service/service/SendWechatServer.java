package com.howbuy.crm.wechat.service.service;

import com.google.common.collect.Maps;
import com.howbuy.crm.wechat.client.base.ReturnMessageDto;
import com.howbuy.crm.wechat.client.enums.AcceptMessageStatusEnum;
import com.howbuy.crm.wechat.client.enums.MessageSendStatusEnum;
import com.howbuy.crm.wechat.client.enums.WechatAppEnum;
import com.howbuy.crm.wechat.dao.po.CmWechatVoiceRemindPO;
import com.howbuy.crm.wechat.dao.po.message.MessageAcceptInfoPO;
import com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO;
import com.howbuy.crm.wechat.service.domain.message.vo.QueryMessageSendStatusVO;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.sendwechat.WechatMessageSendOuterServer;
import com.howbuy.crm.wechat.service.repository.CmWechatVoiceRemindRepository;
import com.howbuy.crm.wechat.service.service.config.BaseConfigServce;
import com.howbuy.crm.wechat.service.service.message.MessageAcceptInfoService;
import com.howbuy.crm.wechat.service.service.message.MessageSendInfoService;
import com.howbuy.crm.wechat.service.service.message.build.MessageBuildFactory;
import com.howbuy.crm.wechat.service.service.message.send.MessageSendFactory;
import crm.howbuy.base.utils.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.empire.commons.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @classname: SendMsgServer
 * @author: yu.zhang
 * @description: 发送企业微信消息接口
 * @creatdate: 2021-02-10 11:00
 * @since: JDK1.8
 */
@Slf4j
@Service
@Transactional
public class SendWechatServer {

    @Autowired
    private WechatMessageSendOuterServer wechatMessageSendOuterServer;

    @Autowired
    private CmWechatVoiceRemindRepository cmWechatVoiceRemindRepository;

    @Autowired
    private MessageAcceptInfoService messageAcceptInfoServicel;

    @Autowired
    private MessageSendInfoService messageSendInfoService;

    @Autowired
    private MessageSendFactory messageSendfactory;

    @Autowired
    private MessageBuildFactory messageBuildfactory;

    @Autowired
    private BaseConfigServce baseConfigServce;

    /**
     * 发送状态 1-发送中
     */
    private static final String SENDING_STATUS = "1";

    /**
     * 发送状态 2-发送成功
     */
    private static final String PUSH_SUCCESS_STATUS = "2";

    /**
     * 发送状态 3-发送失败
     */
    private static final String PUSH_FAIL_STATUS = "3";

    /**
     * 系统问题
     */
    private static final String SYSTEM_PROBLEM = "系统问题";

    //TODO: 重构计划标记：
    // cm_wechat_voice_remind 表 新增字段： app_code . 映射枚举: WechatApplicationEnum.code

    /**
     * sendVoiceRemindMsgByType
     * @param type
     * @param userid
     * @param mobile
     * @return void
     * @Author: yu.zhang on 2021/6/30 17:37
     */
    public void sendVoiceRemindMsgByType(String type, String userid, String mobile, String date) {
        log.info("发送消息sendVoiceRemindMsgByType开始type:" + type + ",userid:" + userid + ",mobile:" + mobile + ",date:" + date);
        try {

            if (!StringUtils.isAnyEmpty(type, userid, mobile, date)) {
                Map<String, Object> paramMap = Maps.newHashMap();
                Map<Object, Object> contentMap = Maps.newHashMap();
                //拼装消息体
                Date showdate = DateTimeUtil.strToDate(date, "yyyyMMddHHmmss");
                String showdt = DateTimeUtil.fmtDate(showdate, "yyyy年MM月dd日 HH:mm:ss");
                String newmsg = "您有未接来电\n号码： " + mobile + "\n时间： " + showdt;
                contentMap.put("content", newmsg);

                paramMap.put("text", contentMap);
                paramMap.put("touser", userid);
                paramMap.put("msgtype", type);
//                paramMap.put("agentid", WechatConfig.wealthCorpid);

                //此处。 固定 发送 使用  [好买财富]企业微信-自建应用-商路通
                //WechatAppEnum 历史枚举。已经暴露给consumer端， 此处 使用历史枚举
                wechatMessageSendOuterServer.sendMsgByApplicationCode(paramMap, WechatAppEnum.WEALTH_RHSLT.getKey());

                CmWechatVoiceRemindPO wechatVoiceRemind = new CmWechatVoiceRemindPO();
                wechatVoiceRemind.setMobile(mobile);
                wechatVoiceRemind.setAcceptUserId(userid);
                wechatVoiceRemind.setCreateTime(DateUtils.getDateNow());
                wechatVoiceRemind.setRemindType(type);
                wechatVoiceRemind.setRemindTime(showdate);
                cmWechatVoiceRemindRepository.insertWechatExternal(wechatVoiceRemind);

            }

        } catch (Exception e) {
            log.error("发送消息sendMsgByType Exception:" + e.getMessage(), e);
        }
    }

    /**
     * @description: 查询发送状态
     * @param messageType 消息类型
     * @param uniqueIdList	业务唯一标识list
     * @return com.howbuy.crm.wechat.client.base.ReturnMessageDto<java.util.Map<java.lang.String,java.lang.String>> 每条消息的发送状态
     * @author: jin.wang03
     * @date: 2024/3/22 14:01
     * @since JDK 1.8
     */
    public ReturnMessageDto<Map<String, String>> querySendStatus(String messageType, List<String> uniqueIdList) {
        log.info("查询发送状态开始,messageType:{},uniqueIdList:{}", messageType, uniqueIdList);
        Map<String, String> reusltMap = new HashMap<>();
        if (StringUtils.isBlank(messageType) || CollectionUtils.isEmpty(uniqueIdList)) {
            return ReturnMessageDto.ok("", reusltMap);
        }
        List<MessageAcceptInfoPO> messageAcceptInfoPOS = messageAcceptInfoServicel.selectByTypeAndUniqueIdList(messageType, uniqueIdList);
        log.info("查询发送状态,messageType:{},uniqueIdList:{},messageAcceptInfoPOS:{}", messageType, uniqueIdList, messageAcceptInfoPOS);
        if (CollectionUtils.isEmpty(messageAcceptInfoPOS)) {
            // 没有查询到数据，返回发送中状态 --在队列里面，还没来得及消费
            uniqueIdList.forEach(uniqueId -> reusltMap.put(uniqueId, SENDING_STATUS));
            return ReturnMessageDto.ok("", reusltMap);
        }

        for (MessageAcceptInfoPO messageAcceptInfoPO : messageAcceptInfoPOS) {
            Long id = messageAcceptInfoPO.getId();
            String uniqueId = messageAcceptInfoPO.getUniqueId();

            if (AcceptMessageStatusEnum.UNPROCESSED.getCode().equals(messageAcceptInfoPO.getMessageStatus())
                    || AcceptMessageStatusEnum.PROCESSING.getCode().equals(messageAcceptInfoPO.getMessageStatus())) {
                // 消息接收表中的状态为0-未处理 或 2-处理中,则为发送中状态
                reusltMap.put(uniqueId, SENDING_STATUS);
            } else if (AcceptMessageStatusEnum.PROCESSED.getCode().equals(messageAcceptInfoPO.getMessageStatus())) {
                // 消息接收表中的状态为1-已处理,则查询发送表中的状态
                List<MessageSendInfoPO> messageSendInfoPOs = messageSendInfoService.selectByAcceptId(id);
                log.info("查询发送状态,messageType:{},uniqueId:{},messageSendInfoPO:{}", messageType, uniqueId, messageSendInfoPOs);
                if (CollectionUtils.isNotEmpty(messageSendInfoPOs)) {
                    // ！！！一个接收信息对应多个发送信息，取第一个发送信息，判断发送状态
                    MessageSendInfoPO messageSendInfoPO = messageSendInfoPOs.get(0);
                    if (MessageSendStatusEnum.UNPUSH.getCode().equals(messageSendInfoPO.getSendStatus())
                            || MessageSendStatusEnum.PUSHING.getCode().equals(messageSendInfoPO.getSendStatus()) ) {
                        // 发送状态为0-未推送 或 1-推送中,则为发送中状态
                        reusltMap.put(uniqueId, SENDING_STATUS);
                    } else if (MessageSendStatusEnum.PUSH_SUCCESS.getCode().equals(messageSendInfoPO.getSendStatus())) {
                        // 发送状态为3-推送成功,则为发送成功状态
                        reusltMap.put(uniqueId, PUSH_SUCCESS_STATUS);
                    } else if (MessageSendStatusEnum.PUSH_FAIL.getCode().equals(messageSendInfoPO.getSendStatus())) {
                        // 当发送失败时，需要判断重发次数
                        if (messageSendInfoPO.getSendTimes() < 3) {
                            // 重发次数小于3次，为发送中状态
                            reusltMap.put(uniqueId, SENDING_STATUS);
                        } else {
                            // 重发次数大于等于3次，为发送失败状态
                            reusltMap.put(uniqueId, PUSH_FAIL_STATUS);
                        }
                    }
                } else {
                    // 没有查询到发送表数据，返回发送失败状态
                    reusltMap.put(uniqueId, PUSH_FAIL_STATUS);
                }
            }
        }

        // 未查询到的数据,返回发送中状态
        for (String uniqueId : uniqueIdList) {
            if (!reusltMap.containsKey(uniqueId)) {
                reusltMap.put(uniqueId, SENDING_STATUS);
                log.info("查询发送状态,未查询到数据,messageType:{},uniqueId:{}", messageType, uniqueId);
            }
        }

        log.info("查询发送状态结束,messageType:{},uniqueIdList:{},reusltMap:{}", messageType, uniqueIdList, reusltMap);
        return ReturnMessageDto.ok("", reusltMap);
    }



    /**
     * @description: 查询消息发送状态及备注
     * @param messageType
     * @param uniqueIdList
     * @return com.howbuy.crm.wechat.client.base.ReturnMessageDto<com.howbuy.crm.wechat.service.domain.message.vo.QueryMessageSendStatusVO>
     * @author: jin.wang03
     * @date: 2024/10/31 9:10
     * @since JDK 1.8
     */
    public ReturnMessageDto<QueryMessageSendStatusVO> querySendStatusWithMemo(String messageType, List<String> uniqueIdList) {
        log.info("查询发送状态开始,messageType:{},uniqueIdList:{}", messageType, uniqueIdList);
        QueryMessageSendStatusVO queryMessageSendStatusVO = new QueryMessageSendStatusVO();
        if (StringUtils.isBlank(messageType) || CollectionUtils.isEmpty(uniqueIdList)) {
            return ReturnMessageDto.ok("", queryMessageSendStatusVO);
        }
        List<MessageAcceptInfoPO> messageAcceptInfoPOS = messageAcceptInfoServicel.selectByTypeAndUniqueIdList(messageType, uniqueIdList);
        log.info("查询发送状态,messageType:{},uniqueIdList:{},messageAcceptInfoPOS:{}", messageType, uniqueIdList, messageAcceptInfoPOS);
        if (CollectionUtils.isEmpty(messageAcceptInfoPOS)) {
            // 没有查询到数据，返回发送中状态 --在队列里面，还没来得及消费
            uniqueIdList.forEach(uniqueId -> {
                QueryMessageSendStatusVO.MessageSendStatusVO messageSendStatusVO = new QueryMessageSendStatusVO.MessageSendStatusVO();
                messageSendStatusVO.setUniqueId(uniqueId);
                messageSendStatusVO.setSendStatus(SENDING_STATUS);
                queryMessageSendStatusVO.getResultList().add(messageSendStatusVO);
            });
            return ReturnMessageDto.ok("", queryMessageSendStatusVO);
        }

        for (MessageAcceptInfoPO messageAcceptInfoPO : messageAcceptInfoPOS) {
            Long id = messageAcceptInfoPO.getId();
            String uniqueId = messageAcceptInfoPO.getUniqueId();
            QueryMessageSendStatusVO.MessageSendStatusVO messageSendStatusVO = new QueryMessageSendStatusVO.MessageSendStatusVO();
            messageSendStatusVO.setUniqueId(uniqueId);

            if (AcceptMessageStatusEnum.UNPROCESSED.getCode().equals(messageAcceptInfoPO.getMessageStatus())
                    || AcceptMessageStatusEnum.PROCESSING.getCode().equals(messageAcceptInfoPO.getMessageStatus())) {
                // 消息接收表中的状态为0-未处理 或 2-处理中,则为发送中状态
                messageSendStatusVO.setSendStatus(SENDING_STATUS);
            } else if (AcceptMessageStatusEnum.PROCESSED.getCode().equals(messageAcceptInfoPO.getMessageStatus())) {
                // 消息接收表中的状态为1-已处理,则查询发送表中的状态
                List<MessageSendInfoPO> messageSendInfoPOs = messageSendInfoService.selectByAcceptId(id);
                log.info("查询发送状态,messageType:{},uniqueId:{},messageSendInfoPO:{}", messageType, uniqueId, messageSendInfoPOs);
                if (CollectionUtils.isNotEmpty(messageSendInfoPOs)) {
                    // ！！！一个接收信息对应多个发送信息，取第一个发送信息，判断发送状态
                    MessageSendInfoPO messageSendInfoPO = messageSendInfoPOs.get(0);
                    if (MessageSendStatusEnum.UNPUSH.getCode().equals(messageSendInfoPO.getSendStatus())
                            || MessageSendStatusEnum.PUSHING.getCode().equals(messageSendInfoPO.getSendStatus()) ) {
                        // 发送状态为0-未推送 或 1-推送中,则为发送中状态
                        messageSendStatusVO.setSendStatus(SENDING_STATUS);
                    } else if (MessageSendStatusEnum.PUSH_SUCCESS.getCode().equals(messageSendInfoPO.getSendStatus())) {
                        // 发送状态为3-推送成功,则为发送成功状态
                        messageSendStatusVO.setSendStatus(PUSH_SUCCESS_STATUS);
                    } else if (MessageSendStatusEnum.PUSH_FAIL.getCode().equals(messageSendInfoPO.getSendStatus())) {
                        // 当发送失败时，需要判断重发次数
                        if (messageSendInfoPO.getSendTimes() < 3) {
                            // 重发次数小于3次，为发送中状态
                            messageSendStatusVO.setSendStatus(SENDING_STATUS);
                        } else {
                            // 重发次数大于等于3次，为发送失败状态
                            messageSendStatusVO.setSendStatus(PUSH_FAIL_STATUS);
                            messageSendStatusVO.setMemo(transferMemo(messageSendInfoPO.getResponseMsg()));
                        }
                    }
                } else {
                    // 没有查询到发送表数据，返回发送失败状态
                    messageSendStatusVO.setSendStatus(PUSH_FAIL_STATUS);
                    messageSendStatusVO.setMemo(SYSTEM_PROBLEM);
                }
            }

            queryMessageSendStatusVO.getResultList().add(messageSendStatusVO);
        }

        // 未查询到的数据,返回发送中状态
        if (uniqueIdList.size() > queryMessageSendStatusVO.getResultList().size()) {
            List<String> hasUniqueIdList = queryMessageSendStatusVO.getResultList().stream()
                    .map(QueryMessageSendStatusVO.MessageSendStatusVO::getUniqueId).collect(Collectors.toList());

            for (String uniqueId : uniqueIdList) {
                if (!hasUniqueIdList.contains(uniqueId)) {
                    QueryMessageSendStatusVO.MessageSendStatusVO messageSendStatusVO = new QueryMessageSendStatusVO.MessageSendStatusVO();
                    messageSendStatusVO.setUniqueId(uniqueId);
                    messageSendStatusVO.setSendStatus(SENDING_STATUS);
                    queryMessageSendStatusVO.getResultList().add(messageSendStatusVO);
                    log.info("查询发送状态,未查询到数据,messageType:{},uniqueId:{}", messageType, uniqueId);
                }
            }
        }


        log.info("查询发送状态结束,messageType:{},uniqueIdList:{},queryMessageSendStatusVO:{}", messageType, uniqueIdList, queryMessageSendStatusVO);
        return ReturnMessageDto.ok("", queryMessageSendStatusVO);
    }


    /**
     * @description: 转义消息中心发送失败原因
     * @param memo
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2024/10/31 9:38
     * @since JDK 1.8
     */
    private String transferMemo(String memo) {
        if (StringUtils.isBlank(memo)) {
            return "系统问题";
        }
        if (memo.contains("黑名单")) {
            return "客户在发送消息的黑名单中";
        }
        if (memo.contains("超时") || memo.contains("超限")) {
            return "网络连接超时或者发送次数达到上限";
        }
        if (memo.contains("渲染")) {
            return "邮件中的资料附件存在问题";
        }

        return "系统问题";
    }


    /**
     * @description: 根据指定的send_ids发送消息
     * @param ids
     * @return void
     * @author: jin.wang03
     * @date: 2025/3/6 18:24
     * @since JDK 1.8
     */
    public void sendMessageByIds(String ids) {
        if(StringUtils.isBlank(ids)) {
            return;
        }

        String[] split = ids.split(",");
        for (String id : split) {
            MessageSendInfoPO messageAcceptInfoPO = messageSendInfoService.selectByPrimaryKey(Long.valueOf(id));
            if(messageAcceptInfoPO == null) {
                continue;
            }
            messageSendfactory.sendMessage(messageAcceptInfoPO);
        }
    }


    /**
     * @description: 根据指定的send_ids构建消息
     * @param ids
     * @return void
     * @author: jin.wang03
     * @date: 2025/6/30 16:37
     * @since JDK 1.8
     */
    public void buildMessageByIds(String ids) {
        if(StringUtils.isBlank(ids)) {
            return;
        }

        String[] split = ids.split(",");
        for (String id : split) {
            MessageAcceptInfoPO messageAcceptInfoPO = messageAcceptInfoServicel.selectByPrimaryKey(Long.valueOf(id));
            if(messageAcceptInfoPO == null) {
                continue;
            }
            messageBuildfactory.buidMessage(messageAcceptInfoPO);
        }
    }

}
