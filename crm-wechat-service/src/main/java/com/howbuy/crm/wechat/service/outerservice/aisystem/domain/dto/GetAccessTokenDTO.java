/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.outerservice.aisystem.domain.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @description: AI_SYSTEM系统getAccessToken接口响应参数
 * @date 2025-08-18 09:39:32
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class GetAccessTokenDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据 (accessToken)
     */
    @JSONField(name = "access_token")
    private String accessToken;

    @JSONField(name = "corp_id")
    private String corpId;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
}