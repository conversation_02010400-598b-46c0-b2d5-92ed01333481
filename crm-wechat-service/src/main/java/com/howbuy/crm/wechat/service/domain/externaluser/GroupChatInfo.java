package com.howbuy.crm.wechat.service.domain.externaluser;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @description: 客户群跟进状态列表
 * @author: haoran.zhang
 * @create: 2021-08-31 13:56
 **/
@Data
@Accessors(chain =true)
public class GroupChatInfo {

    /**
     * 客户群ID
     */
    private String chatId;

    /**
     * 群名
     */
    private String  chatName;

    /**
     * 群主ID
     */
    private String chatOwner;

    /**
     * 群的创建时间
     */
    private Date createTime;

    /**
     * 群公告
     */
    private String notice;

    /**
     * 群成员列表
     */
    List<GroupChatMemberInfo> chatMemberList= Lists.newArrayList();

    /**
     * 群管理员userid列表
     */
    private List<String> adminUserIdList=Lists.newArrayList();
}
