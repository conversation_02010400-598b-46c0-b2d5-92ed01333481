/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.outerservice.aisystem.domain.context;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @description: AI_SYSTEM系统getAccessToken接口请求参数
 * @date 2025-08-18 09:39:32
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class GetAccessTokenContext implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 企业ID
     */
    @JSONField(name = "corp_id")
    private String corpId;
    
    // TODO: 根据实际接口文档添加其他请求参数
}