package com.howbuy.crm.wechat.service.commom.utils;

import com.howbuy.dfile.HFileService;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Path;
import java.util.Objects;

/**
 * 基于pa 文件sdk封装常用文件操作
 */
public class FileUtil {

    private static final Logger logger = LoggerFactory.getLogger(FileUtil.class);

    private FileUtil() {
        //
    }

    /**
     * txt数据文件字段分隔符 ‘|’
     */
    public static final String FIELD_SEPARATOR = "|";

    public static final String URL_SEPARATOR = "/";


    /**
     *
     * @param fileName
     * @return
     */
    public static File getFile(String fileName){
        return new File(fileName); //NOSONAR
    }


    /**
     * @description:(请在此添加描述)
     * @param inputStream
     * @return byte[]
     * @author: jin.wang03
     * @date: 2024/6/28 14:46
     * @since JDK 1.8
     */
    public static byte[] inputStreamToByteArray(InputStream inputStream) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        byte[] buffer = new byte[1024]; // 缓冲区大小，可根据实际情况调整
        int length;

        // 读取输入流中的数据到缓冲区，直到没有更多数据可读
        while ((length = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, length);
        }

        // 关闭输入流和输出流（如果它们需要被关闭的话，注意ByteArrayOutputStream不需要关闭）
        inputStream.close();

        // 获取字节数组
        return outputStream.toByteArray();
    }


    /**
     * 读取文件
     */
    public static ByteArrayOutputStream read(String fileName) {
        int len;
        FileInputStream inputStream = null;
        ByteArrayOutputStream outStream = null;
        try {
            inputStream = new FileInputStream(getFile(fileName));
            outStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            while ((len = inputStream.read(buffer)) != -1) {
                outStream.write(buffer, 0, len);
            }
        } catch (Exception e) {
            logger.error("Exception:", e);
        } finally {
            IOUtils.closeQuietly(inputStream);
            IOUtils.closeQuietly(outStream);
        }
        return outStream;
    }

    /**
     * <p>Title: getContent</p>
     * <p>Description:根据文件路径读取文件转出byte[] </p>
     *
     * @param filePath 文件路径
     * @return 字节数组
     */
    public static byte[] getContent(String filePath) throws IOException {
        File file = getFile(filePath);
        long fileSize = file.length();
        if (fileSize > Integer.MAX_VALUE) {
            logger.info("file too big...");
            return null;
        }
        FileInputStream fi = new FileInputStream(file);
        byte[] buffer = new byte[(int) fileSize];
        int offset = 0;
        int numRead;
        while (offset < buffer.length
                && (numRead = fi.read(buffer, offset, buffer.length - offset)) >= 0) {
            offset += numRead;
        }
        // 确保所有数据均被读取
        if (offset != buffer.length) {
            throw new IOException("Could not completely read file "
                    + file.getName());
        }
        fi.close();
        return buffer;
    }

    public static void deleteOnExist(File file) {
        if (Objects.nonNull(file) && file.exists()) {
            file.delete();
        }
    }


    /**
     * 业务 映射 nacos 配置的key
     * @param importType
     * @return
     */
//    public static String getFileReadStoreKey( 业务枚举 importType) {
//        switch (importType) {
//            //  业务--> nacos配置的key
//                return NACOS_CONFIG_DFILE_KEY ;//
//            default:
//                return importType.name();
//        }
//    }


    /**
     * @description: 从文件url获取字节数组
     * @param urlStr
     * @return byte[]
     * @author: jin.wang03
     * @date: 2024/7/4 10:31
     * @since JDK 1.8
     */
    public static byte[] getBytesFromUrl(String urlStr) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(5000);

        if (connection.getResponseCode() != HttpURLConnection.HTTP_OK) {
            throw new RuntimeException("Failed : HTTP error code : "
                    + connection.getResponseCode());
        }

        InputStream is = connection.getInputStream();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length;

        while ((length = is.read(buffer)) != -1) {
            baos.write(buffer, 0, length);
        }

        is.close();
        connection.disconnect();

        return baos.toByteArray();
    }
}