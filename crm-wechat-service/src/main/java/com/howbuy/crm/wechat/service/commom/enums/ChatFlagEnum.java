/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 客户群状态枚举
 * <AUTHOR>
 * @date 2025-06-18 14:25:51
 * @since JDK 1.8
 */
@Getter
@AllArgsConstructor
public enum ChatFlagEnum {

    /**
     * 正常
     */
    NORMAL("0", "正常"),

    /**
     * 删除
     */
    DELETED("1", "删除");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return ChatFlagEnum
     */
    public static ChatFlagEnum getByCode(String code) {
        for (ChatFlagEnum chatFlagEnum : ChatFlagEnum.values()) {
            if (chatFlagEnum.getCode().equals(code)) {
                return chatFlagEnum;
            }
        }
        return null;
    }

    /**
     * 判断是否为删除状态
     *
     * @param code 状态码
     * @return boolean
     */
    public static boolean isDeleted(String code) {
        return DELETED.getCode().equals(code);
    }

    /**
     * 判断是否为正常状态
     *
     * @param code 状态码
     * @return boolean
     */
    public static boolean isNormal(String code) {
        return NORMAL.getCode().equals(code);
    }
} 