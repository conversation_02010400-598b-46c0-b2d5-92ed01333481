/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.message.build;

import com.google.common.collect.Lists;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.wechat.client.enums.AcceptMessageTypeEnum;
import com.howbuy.crm.wechat.client.enums.MessageSendChannelEnum;
import com.howbuy.crm.wechat.client.enums.MessageSendStatusEnum;
import com.howbuy.crm.wechat.dao.po.message.MessageAcceptInfoPO;
import com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description: 企微机器人发送数据构建器
 * <AUTHOR>
 * @date 2024/2/6 1:58 PM
 * @since JDK 1.8
 */
@Service
@Slf4j
public class RebotBuilderService extends AbstractMessageBuildService{

    @Override
    public List<AcceptMessageTypeEnum> getMsgTypeList() {
        return Lists.newArrayList(AcceptMessageTypeEnum.ROBOT);
    }

    @Override
    public List<MessageSendInfoPO> buildSpecificMessage(MessageAcceptInfoPO acceptInfo) {
        List<MessageSendInfoPO> resList = new ArrayList<>();
        MessageSendInfoPO messageSendInfoPO = new MessageSendInfoPO();
        messageSendInfoPO.setAcceptId(acceptInfo.getId());
        messageSendInfoPO.setSendDt(DateUtil.formatNowDate(DateUtil.SHORT_DATE_PATTERN));
        //发送状态为0-未推送，消息发送次数设置为默认值0
        messageSendInfoPO.setSendStatus(MessageSendStatusEnum.UNPUSH.getCode());
        messageSendInfoPO.setSendTimes(0);
        messageSendInfoPO.setCreateTime(new Date());
        messageSendInfoPO.setSendChannel(MessageSendChannelEnum.BOT_MESSAGE.getCode());
        messageSendInfoPO.setTempleteId(null);
        // 此处传入的参数为给消息中心的参数
        messageSendInfoPO.setTempleteParams(acceptInfo.getMessageParams());
        resList.add(messageSendInfoPO);
        return resList;
    }
}