package com.howbuy.crm.wechat.service.domain.callback;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 企业微信群事件DTO
 * @author: shuai.zhang
 * @date: 2023/6/8 18:34 
 * @since JDK 1.8
 * @version: 1.0
 */
@Getter
@Setter
@ToString
public class ChatEventDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 应用ID
     */
    private String corpId;
    /**
     * 企业微信CorpID
     */
    private String toUserName;
    /**
     * 此事件该值固定为sys，表示该消息由系统生成
     */
    private String fromUserName;
    /**
     * 消息创建时间 （整型）
     */
    private String createTime;
    /**
     * 消息的类型
     */
    private String msgType;
    /**
     * 事件的类型
     */
    private String event;
    /**
     * 触发类型
     */
    private String changeType;
    /**
     * 群ID
     */
    private String chatId;
    /**
     * 变更详情。目前有以下几种：
     * add_member : 成员入群
     * del_member : 成员退群
     * change_owner : 群主变更
     * change_name : 群名变更
     * change_notice : 群公告变更
     */
    private String updateDetail;
    /**
     * 当是成员入群时有值。表示成员的入群方式
     * 0 - 由成员邀请入群（包括直接邀请入群和通过邀请链接入群）
     * 3 - 通过扫描群二维码入群
     */
    private String joinScene;
    /**
     * 	当是成员退群时有值。表示成员的退群方式
     * 0 - 自己退群
     * 1 - 群主/群管理员移出
     */
    private String quitScene;
    /**
     * 当是成员入群或退群时有值。表示成员变更数量
     */
    private Integer memChangeCnt;
    /**
     * 当是成员入群或退群时有值。变更的成员列表
     */
    //private List<String> MemChangeList;
    /**
     * 当是成员入群或退群时有值。 变更前的群成员版本号
     */
    private String lastMemVer;
    /**
     * 当是成员入群或退群时有值。变更后的群成员版本号
     */
    private String curMemVer;
}
