/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.message.send;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.crm.wechat.client.enums.MessageSendChannelEnum;
import com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.domain.message.SendResultDTO;
import com.howbuy.crm.wechat.service.domain.message.vo.BotMessageVo;
import com.howbuy.crm.wechat.service.outerservice.messagecenter.CompanySendOuterService;
import com.howbuy.crm.wechat.service.service.message.MessageSendInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: (发送消息工厂类    用于 调用接口  发送消息 )
 * <AUTHOR>
 * @date 2023/10/7 16:27
 * @since JDK 1.8
 */
@Component
@Slf4j
public class MessageSendFactory  {

    @Autowired
    private CompanySendOuterService companySendOuterService;

    @Autowired
    private MessageSendInfoService messageSendInfoService;

    /**
     * @description:构建待发送消息
     * @param sendInfo 待发送消息
     * @return void
     * @throws
     * @since JDK 1.8
     */
    public void sendMessage(MessageSendInfoPO sendInfo){
        SendResultDTO result = sendMessageByChannel(sendInfo);
        int updateCount = messageSendInfoService.updateStatusAfterSend(result, sendInfo.getId());
        log.info("更新消息发送结果，消息id:{},发送结果:{},更新条数:{}",sendInfo.getId(),JSON.toJSONString(result),updateCount);
    }


    /**
     * @description:根据消息发送渠道发送消息
     * @param sendInfo 待发送消息
     * @return void
     * @throws
     * @since JDK 1.8
     */
    public SendResultDTO sendMessageByChannel(MessageSendInfoPO sendInfo){
        MessageSendChannelEnum channelEnum=MessageSendChannelEnum.getEnum(sendInfo.getSendChannel());
        if (channelEnum == null) {
            throw new BusinessException(Constants.SYSTEM_ERROR,"未知消息类型！");
        }
        SendResultDTO resultDTO;
        switch (channelEnum) {
            case BOT_MESSAGE:
                resultDTO = sendBotMessage(sendInfo);
                break;
            case NEW_APPLICATION_MESSAGE:
                resultDTO = sendNewApplicationMessage(sendInfo);
                break;
            case EMAIL:
                resultDTO = sendEmailMessage(sendInfo);
                break;
            case SMS:
                resultDTO = sendSmsMessage(sendInfo);
                break;
            case EMAIL_WITH_ATTACHMENT:
                resultDTO = sendEmailWithAttachmentMessage(sendInfo);
                break;
            case HB_FUND_APP:
                resultDTO = sendHbAppMessage(sendInfo);
                break;
            case AUTO_CHANNEL_BY_HBONENO:
                resultDTO = sendAutoChannelMessage(sendInfo);
                break;
            default:
                throw new BusinessException(Constants.SYSTEM_ERROR, "未知消息类型！");
        }
        return resultDTO;
    }


    /**
     * @description 自建应用发送企微消息
     * @param sendInfo
     * @return
     * <AUTHOR>
     * @date 2023/11/29 1:44 PM
     * @since JDK 1.8
     */
    private SendResultDTO sendNewApplicationMessage(MessageSendInfoPO sendInfo) {
        SendResultDTO resultDTO = new SendResultDTO();
        try {
            resultDTO = companySendOuterService.newApplicationSendMsg(sendInfo);
        } catch (Exception e) {
            log.error(String.format("消息发送，acceptId:%s,sendId:%s 发送异常！",sendInfo.getAcceptId(),sendInfo.getId()),e);
            resultDTO.setSuccess(false);
            resultDTO.setMsg("系统异常！");
        }
        return resultDTO;
    }

    /**
     * @description: 发送email
     * @param sendInfo
     * @return com.howbuy.crm.wechat.service.domain.message.SendResultDTO
     * @author: jin.wang03
     * @date: 2024/5/27 18:09
     * @since JDK 1.8
     */
    private SendResultDTO sendEmailMessage(MessageSendInfoPO sendInfo) {
        SendResultDTO resultDTO = new SendResultDTO();
        try {
            resultDTO = companySendOuterService.sendEmail(sendInfo);
        } catch (Exception e) {
            resultDTO.setSuccess(false);
            resultDTO.setMsg(e.getMessage());
        }
        return resultDTO;
    }


    /**
     * @description:
     * @param sendInfo
     * @return com.howbuy.crm.wechat.service.domain.message.SendResultDTO
     * @author: jin.wang03
     * @date: 2024/10/28 15:33
     * @since JDK 1.8
     */
    private SendResultDTO sendEmailWithAttachmentMessage(MessageSendInfoPO sendInfo) {
        SendResultDTO resultDTO = new SendResultDTO();
        try {
            resultDTO = companySendOuterService.endEmailWithAttachment(sendInfo);
        } catch (Exception e) {
            log.error("调用消息中心CompanySendOuterService接口失败！", e);
            resultDTO.setSuccess(false);
            resultDTO.setMsg(e.getMessage());
        }
        return resultDTO;
    }


    /**
     * @description: 发送短信
     * @param sendInfo
     * @return com.howbuy.crm.wechat.service.domain.message.SendResultDTO
     * @author: jin.wang03
     * @date: 2024/5/27 18:09
     * @since JDK 1.8
     */
    private SendResultDTO sendSmsMessage(MessageSendInfoPO sendInfo) {
        SendResultDTO resultDTO = new SendResultDTO();
        try {
            resultDTO = companySendOuterService.sendSms(sendInfo);
        } catch (Exception e) {
            resultDTO.setSuccess(false);
            resultDTO.setMsg(e.getMessage());
        }
        return resultDTO;
    }

    /**
     * @description: 在好买基金App中，给客户发送消息
     * @param sendInfo
     * @return com.howbuy.crm.wechat.service.domain.message.SendResultDTO
     * @author: jin.wang03
     * @date: 2025/6/19 15:45
     * @since JDK 1.8
     */
    private SendResultDTO sendHbAppMessage(MessageSendInfoPO sendInfo) {
        SendResultDTO resultDTO = new SendResultDTO();
        try {
            resultDTO = companySendOuterService.sendHbApp(sendInfo);
        } catch (Exception e) {
            resultDTO.setSuccess(false);
            resultDTO.setMsg(e.getMessage());
        }
        return resultDTO;
    }


    /**
     * @description: 根据hbOneNo自动选择发送渠道
     * @param sendInfo
     * @return com.howbuy.crm.wechat.service.domain.message.SendResultDTO
     * @author: jin.wang03
     * @date: 2025/6/19 17:57
     * @since JDK 1.8
     */
    private SendResultDTO sendAutoChannelMessage(MessageSendInfoPO sendInfo) {
        SendResultDTO resultDTO = new SendResultDTO();
        try {
            resultDTO = companySendOuterService.sendAutoChannel(sendInfo);
        } catch (Exception e) {
            resultDTO.setSuccess(false);
            resultDTO.setMsg(e.getMessage());
        }
        return resultDTO;
    }




    public SendResultDTO sendBotMessage(MessageSendInfoPO sendInfo){
        SendResultDTO resultDTO=new SendResultDTO();
        try {
            BotMessageVo messageVo= JSON.parseObject(sendInfo.getTempleteParams(),BotMessageVo.class);
            resultDTO= companySendOuterService.sendBotMessage(messageVo);
        }catch (Exception e){
            resultDTO.setSuccess(false);
            resultDTO.setMsg(e.getMessage());
        }
        return resultDTO;
    }



}