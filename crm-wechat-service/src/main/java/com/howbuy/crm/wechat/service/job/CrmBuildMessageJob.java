/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.job;

import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.howbuy.crm.wechat.client.enums.AcceptMessageStatusEnum;
import com.howbuy.crm.wechat.dao.po.message.MessageAcceptInfoPO;
import com.howbuy.crm.wechat.dao.vo.message.MessageAcceptInfoVO;
import com.howbuy.crm.wechat.service.batch.NewBatchOperateExecutor;
import com.howbuy.crm.wechat.service.batch.QueryPageService;
import com.howbuy.crm.wechat.service.service.message.MessageAcceptInfoService;
import com.howbuy.crm.wechat.service.service.message.build.MessageBuildFactory;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: (发送消息 构建消息任务消息接收处理器)
 * <AUTHOR>
 * @date 2023/10/7 9:59
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CrmBuildMessageJob extends AbstractBatchMessageJob {

    /**
     * crm发送消息 接收消息队列
     */
    @Value("${crm_build_message_channel}")
    private String crmbuildMessageChannel;

    @Resource
    private NewBatchOperateExecutor newBatchOperateExecutor;

    @Autowired
    private MessageAcceptInfoService messageAcceptInfoService;

    @Autowired
    private MessageBuildFactory messageBuildfactory;

    public CrmBuildMessageJob() {
    }

    @Override
    protected String getQuartMessageChannel() {
        return crmbuildMessageChannel;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
//        查询消息接收表（message_accept_info）中状态为0-未处理的记录list
        MessageAcceptInfoVO acceptInfoVO=new MessageAcceptInfoVO();
        acceptInfoVO.setMessageStatusList(Lists.newArrayList(AcceptMessageStatusEnum.UNPROCESSED.getCode()));
        newBatchOperateExecutor.poolExecute(acceptInfoVO, new QueryPageService<MessageAcceptInfoVO, MessageAcceptInfoPO>() {
            @Override
            public int doBusiness(List<MessageAcceptInfoPO> dataList) {
                messageBuildfactory.buidMessageList(dataList);
//                //遍历list，根据消息接收表中的消息id，--> 构建 消息发送表（message_send_info）中的消息
//                dataList.forEach(messageAcceptInfoPO -> {
//                    //构建消息发送表中的消息
//                    messageBuildfactory.buidMessage(messageAcceptInfoPO);
//                });
                return dataList.size();
            }

            @Override
            public Page<MessageAcceptInfoPO> queryPage(MessageAcceptInfoVO queryVo) {
                return messageAcceptInfoService.selectPageByVo(queryVo);
            }
        });

    }

}