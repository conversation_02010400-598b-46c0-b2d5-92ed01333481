package com.howbuy.crm.wechat.service.outerservice.wechatapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.crm.wechat.client.base.ReturnMessageDto;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.domain.ExternalUserInfoDTO;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.vo.WechatQueryUserVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 企业内部开发- 服务端API- 通讯录管理- 成员管理 [/user/]
 * <a href="https://developer.work.weixin.qq.com/document/path/90196">...</a>
 * @author: yu.zhang
 * @date: 2023/6/19 10:50 
 * @since JDK 1.8
 * @version: 1.0
 */
@Slf4j
@Service
public class WechatUserOuterService  extends AbsCompanyBasedWechatOuterService {


    /**
     *通讯录管理-- 成员管理-- 获取部门成员详情列表
     * 请求地址：https://qyapi.weixin.qq.com/cgi-bin/user/list?access_token=ACCESS_TOKEN&department_id=DEPARTMENT_ID&fetch_child=FETCH_CHILD
     *  【重要】fetch_child=1：递归调用 已从api中删除
     * 【重要】从2022年8月15日10点开始，“企业管理后台 - 管理工具 - 通讯录同步”的新增IP将不能再调用此接口，企业可通过「获取成员ID列表」和「获取部门ID列表」接口获取userid和部门ID列表。查看调整详情。
     */
    public static final String GET_USER_DETAIL_LIST = "user/list";



    /**
     *通讯录管理-- 成员管理-- 读取成员
     * 请求地址：https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=ACCESS_TOKEN&userid=USERID
     */
    public static final String GET_USER_DETAIL = "/user/get";

    /**
     * @param companyNoEnum  企微-企业主体
     * @param userId 用户ID
     * @return 企业微信成员信息
     * @description: 查询企业微信成员信息
     * <AUTHOR>
     * @date 2025-06-18 16:06:07
     * @since JDK 1.8
     */
    public ExternalUserInfoDTO queryUserByCompanyNo(String companyNoEnum,String userId) {
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("userid",userId);
        String responseStr = requestWithString(GET_USER_DETAIL, companyNoEnum, paramsMap, Constants.METHOD_GET);

        return transferExternalUser(responseStr);
        // return JSON.parseObject(responseStr, ExternalUserInfoDTO.class);
    }



    /**
     * @description: 将企业微信API返回的JSON字符串转换为ExternalUserInfoDTO对象
     * @param responseStr 企业微信API返回的JSON字符串
     * @return ExternalUserInfoDTO 企业微信成员信息DTO
     * <AUTHOR>
     * @date 2025/7/18
     * @since JDK 1.8
     */
    private ExternalUserInfoDTO transferExternalUser(String responseStr) {

//        {
//            "errcode": 0,
//                "errmsg": "ok",
//                "userid": "zhangsan",
//                "name": "张三",
//                "department": [1, 2],
//            "order": [1, 2],
//            "position": "后台工程师",
//                "mobile": "13800000000",
//                "gender": "1",
//                "email": "<EMAIL>",
//                "biz_mail":"<EMAIL>",
//                "is_leader_in_dept": [1, 0],
//            "direct_leader":["lisi"],
//            "avatar": "http://wx.qlogo.cn/mmopen/ajNVdqHZLLA3WJ6DSZUfiakYe37PKnQhBIeOQBO4czqrnZDS79FH5Wm5m4X69TBicnHFlhiafvDwklOpZeXYQQ2icg/0",
//                "thumb_avatar": "http://wx.qlogo.cn/mmopen/ajNVdqHZLLA3WJ6DSZUfiakYe37PKnQhBIeOQBO4czqrnZDS79FH5Wm5m4X69TBicnHFlhiafvDwklOpZeXYQQ2icg/100",
//                "telephone": "020-123456",
//                "alias": "jackzhang",
//                "address": "广州市海珠区新港中路",
//                "open_userid": "xxxxxx",
//                "main_department": 1,
//                "extattr": {
//            "attrs": [
//            {
//                    "type": 0,
//                    "name": "文本名称",
//                    "text": {
//                "value": "文本"
//            }
//            },
//            {
//                "type": 1,
//                    "name": "网页名称",
//                    "web": {
//                "url": "http://www.test.com",
//                        "title": "标题"
//            }
//            }
//		]
//        },
//            "status": 1,
//                "qr_code": "https://open.work.weixin.qq.com/wwopen/userQRCode?vcode=xxx",
//                "external_position": "产品经理",
//                "external_profile": {
//            "external_corp_name": "企业简称",
//                    "wechat_channels": {
//                "nickname": "视频号名称",
//                        "status": 1
//            },
//            "external_attr": [{
//                "type": 0,
//                        "name": "文本名称",
//                        "text": {
//                    "value": "文本"
//                }
//            },
//            {
//                "type": 1,
//                    "name": "网页名称",
//                    "web": {
//                "url": "http://www.test.com",
//                        "title": "标题"
//            }
//            },
//            {
//                "type": 2,
//                    "name": "测试app",
//                    "miniprogram": {
//                "appid": "wx8bd80126147dFAKE",
//                        "pagepath": "/index",
//                        "title": "my miniprogram"
//            }
//            }
//		]
//        }
//        }

        if (StringUtils.isEmpty(responseStr)) {
            return null;
        }

        JSONObject jsonObject = JSON.parseObject(responseStr);
        if (jsonObject == null) {
            return null;
        }

        ExternalUserInfoDTO userInfo = new ExternalUserInfoDTO();

        // 设置基础返回信息
        userInfo.setErrcode(jsonObject.getString("errcode"));
        userInfo.setErrmsg(jsonObject.getString("errmsg"));

        // 如果调用失败，直接返回
        if (!userInfo.isSuccess()) {
            return userInfo;
        }

        // 设置基本用户信息
        userInfo.setUserid(jsonObject.getString("userid"));
        userInfo.setName(jsonObject.getString("name"));
        userInfo.setPosition(jsonObject.getString("position"));
        userInfo.setMobile(jsonObject.getString("mobile"));
        userInfo.setGender(jsonObject.getString("gender"));
        userInfo.setEmail(jsonObject.getString("email"));
        userInfo.setBizMail(jsonObject.getString("biz_mail"));
        userInfo.setAvatar(jsonObject.getString("avatar"));
        userInfo.setThumbAvatar(jsonObject.getString("thumb_avatar"));
        userInfo.setTelephone(jsonObject.getString("telephone"));
        userInfo.setAlias(jsonObject.getString("alias"));
        userInfo.setAddress(jsonObject.getString("address"));
        userInfo.setOpenUserid(jsonObject.getString("open_userid"));
        userInfo.setQrCode(jsonObject.getString("qr_code"));
        userInfo.setExternalPosition(jsonObject.getString("external_position"));

        // 设置数值类型字段
        if (jsonObject.get("main_department") != null) {
            userInfo.setMainDepartment(jsonObject.getInteger("main_department"));
        }
        if (jsonObject.get("status") != null) {
            userInfo.setStatus(jsonObject.getInteger("status"));
        }

        // 设置数组类型字段
        userInfo.setDepartment(parseIntegerList(jsonObject, "department"));
        userInfo.setOrder(parseIntegerList(jsonObject, "order"));
        userInfo.setIsLeaderInDept(parseIntegerList(jsonObject, "is_leader_in_dept"));
        userInfo.setDirectLeader(parseStringList(jsonObject, "direct_leader"));

        // 设置扩展属性
        setExtAttr(userInfo, jsonObject);

        // 设置对外属性
        setExternalProfile(userInfo, jsonObject);

        return userInfo;
    }



    /**
     * @description: 设置扩展属性
     * @param userInfo 用户信息对象
     * @param jsonObject JSON对象
     * <AUTHOR>
     * @date 2025/7/18
     * @since JDK 1.8
     */
    private void setExtAttr(ExternalUserInfoDTO userInfo, JSONObject jsonObject) {
        JSONObject extAttrJson = jsonObject.getJSONObject("extattr");
        if (extAttrJson == null) {
            return;
        }

        ExternalUserInfoDTO.ExtAttr extAttr = new ExternalUserInfoDTO.ExtAttr();
        JSONArray attrsArray = extAttrJson.getJSONArray("attrs");

        if (attrsArray != null && !attrsArray.isEmpty()) {
            List<ExternalUserInfoDTO.Attr> attrs = new ArrayList<>();

            for (int i = 0; i < attrsArray.size(); i++) {
                JSONObject attrJson = attrsArray.getJSONObject(i);
                if (attrJson != null) {
                    ExternalUserInfoDTO.Attr attr = parseAttr(attrJson);
                    if (attr != null) {
                        attrs.add(attr);
                    }
                }
            }

            extAttr.setAttrs(attrs);
        }

        userInfo.setExtattr(extAttr);
    }

    /**
     * @description: 解析单个属性
     * @param attrJson 属性JSON对象
     * @return ExternalUserInfoDTO.Attr 属性对象
     * <AUTHOR>
     * @date 2025/7/18
     * @since JDK 1.8
     */
    private ExternalUserInfoDTO.Attr parseAttr(JSONObject attrJson) {
        ExternalUserInfoDTO.Attr attr = new ExternalUserInfoDTO.Attr();

        attr.setType(attrJson.getInteger("type"));
        attr.setName(attrJson.getString("name"));

        // 根据类型设置不同的属性值
        Integer type = attr.getType();
        if (type != null) {
            switch (type) {
                case 0: // 文本类型
                    JSONObject textJson = attrJson.getJSONObject("text");
                    if (textJson != null) {
                        ExternalUserInfoDTO.TextAttr textAttr = new ExternalUserInfoDTO.TextAttr();
                        textAttr.setValue(textJson.getString("value"));
                        attr.setText(textAttr);
                    }
                    break;
                case 1: // 网页类型
                    JSONObject webJson = attrJson.getJSONObject("web");
                    if (webJson != null) {
                        ExternalUserInfoDTO.WebAttr webAttr = new ExternalUserInfoDTO.WebAttr();
                        webAttr.setUrl(webJson.getString("url"));
                        webAttr.setTitle(webJson.getString("title"));
                        attr.setWeb(webAttr);
                    }
                    break;
                case 2: // 小程序类型
                    JSONObject miniProgramJson = attrJson.getJSONObject("miniprogram");
                    if (miniProgramJson != null) {
                        ExternalUserInfoDTO.MiniProgramAttr miniProgramAttr = new ExternalUserInfoDTO.MiniProgramAttr();
                        miniProgramAttr.setAppid(miniProgramJson.getString("appid"));
                        miniProgramAttr.setPagepath(miniProgramJson.getString("pagepath"));
                        miniProgramAttr.setTitle(miniProgramJson.getString("title"));
                        attr.setMiniprogram(miniProgramAttr);
                    }
                    break;
                default:
                    log.warn("未知的属性类型: {}", type);
                    break;
            }
        }

        return attr;
    }

    /**
     * @description: 设置对外属性
     * @param userInfo 用户信息对象
     * @param jsonObject JSON对象
     * <AUTHOR>
     * @date 2025/7/18
     * @since JDK 1.8
     */
    private void setExternalProfile(ExternalUserInfoDTO userInfo, JSONObject jsonObject) {
        JSONObject externalProfileJson = jsonObject.getJSONObject("external_profile");
        if (externalProfileJson == null) {
            return;
        }

        ExternalUserInfoDTO.ExternalProfile externalProfile = new ExternalUserInfoDTO.ExternalProfile();

        // 设置企业简称
        externalProfile.setExternalCorpName(externalProfileJson.getString("external_corp_name"));

        // 设置视频号信息
        JSONObject wechatChannelsJson = externalProfileJson.getJSONObject("wechat_channels");
        if (wechatChannelsJson != null) {
            ExternalUserInfoDTO.WechatChannels wechatChannels = new ExternalUserInfoDTO.WechatChannels();
            wechatChannels.setNickname(wechatChannelsJson.getString("nickname"));
            wechatChannels.setStatus(wechatChannelsJson.getInteger("status"));
            externalProfile.setWechatChannels(wechatChannels);
        }

        // 设置对外属性列表
        JSONArray externalAttrArray = externalProfileJson.getJSONArray("external_attr");
        if (externalAttrArray != null && !externalAttrArray.isEmpty()) {
            List<ExternalUserInfoDTO.Attr> externalAttrs = new ArrayList<>();

            for (int i = 0; i < externalAttrArray.size(); i++) {
                JSONObject attrJson = externalAttrArray.getJSONObject(i);
                if (attrJson != null) {
                    ExternalUserInfoDTO.Attr attr = parseAttr(attrJson);
                    if (attr != null) {
                        externalAttrs.add(attr);
                    }
                }
            }

            externalProfile.setExternalAttr(externalAttrs);
        }

        userInfo.setExternalProfile(externalProfile);
    }

   /**
    * @description:(获取部门成员列表详情)
    * @param queryUserVO  获取部门成员列表详情查询对象
    * @return java.util.List<com.howbuy.crm.wechat.service.outerservice.wechatapi.domain.ExternalUserInfoDTO>
    * @author: haoran.zhang
    * @date: 2025/7/18 15:46
    * @since JDK 1.8
    */
    public List<ExternalUserInfoDTO> queryUserListByCompanyNo(WechatQueryUserVO queryUserVO ){
        String companyNoEnum = queryUserVO.getCompanyNo();
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("department_id", queryUserVO.getDepartmentId());
        paramMap.put("fetch_child", queryUserVO.getFetchChild());
        String responseStr = requestWithString(GET_USER_DETAIL_LIST, companyNoEnum, paramMap, Constants.METHOD_GET);

        ReturnMessageDto<String> validateDto = isSuccess(responseStr);
        if(!validateDto.isSuccess()){
            return Lists.newArrayList();
        }

        return transferExternalUserList(responseStr);
    }

    /**
     * @description: 将企业微信API返回的用户列表JSON字符串转换为ExternalUserInfoDTO列表
     * @param responseStr 企业微信API返回的JSON字符串
     * @return List<ExternalUserInfoDTO> 企业微信成员信息DTO列表
     * <AUTHOR>
     * @date 2025/7/18
     * @since JDK 1.8
     */
    private List<ExternalUserInfoDTO> transferExternalUserList(String responseStr) {
        if (StringUtils.isEmpty(responseStr)) {
            return Lists.newArrayList();
        }

        JSONObject jsonObject = JSON.parseObject(responseStr);
        if (jsonObject == null) {
            return Lists.newArrayList();
        }

        // 检查返回状态
        String errcode = jsonObject.getString("errcode");
        if (!"0".equals(errcode)) {
            log.warn("获取用户列表失败, errcode: {}, errmsg: {}", errcode, jsonObject.getString("errmsg"));
            return Lists.newArrayList();
        }

        JSONArray userListArray = jsonObject.getJSONArray("userlist");
        if (userListArray == null || userListArray.isEmpty()) {
            return Lists.newArrayList();
        }

        List<ExternalUserInfoDTO> userList = Lists.newArrayList();
        for (int i = 0; i < userListArray.size(); i++) {
            JSONObject userJson = userListArray.getJSONObject(i);
            if (userJson != null) {
                // 为每个用户创建一个完整的响应JSON，包含成功状态
                JSONObject completeUserJson = new JSONObject();
                completeUserJson.put("errcode", "0");
                completeUserJson.put("errmsg", "ok");
                completeUserJson.putAll(userJson);

                ExternalUserInfoDTO userInfo = transferExternalUser(completeUserJson.toJSONString());
                if (userInfo != null) {
                    userList.add(userInfo);
                }
            }
        }

        return userList;
    }
}
