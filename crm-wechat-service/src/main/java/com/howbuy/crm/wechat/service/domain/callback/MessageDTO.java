package com.howbuy.crm.wechat.service.domain.callback;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @classname: MessageDTO
 * @author: yu.zhang
 * @description: 企业微信回复文本消息
 * @creatdate: 2021-02-08 15:56
 * @since: JDK1.8
 */
@Getter
@Setter
@ToString
public class MessageDTO implements Serializable {
    /**
     *成员UserID
     */
    private String ToUserName;
    /**
     *企业微信
     */
    private String FromUserName;
    /**
     *时间戳
     */
    private Long CreateTime;
    /**
     * 消息类型：文本
     */
    private String MsgType;
    /**
     *消息内容-成功
     */
    private String Content;
}
