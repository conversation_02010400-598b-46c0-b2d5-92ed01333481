/**
 * Copyright (c) 2024, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.domain.wechatmember;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 企业微信成员信息DTO
 * <AUTHOR>
 * @date 2024/6/20 14:04
 * @since JDK 1.8
 */
@Setter
@Getter
public class WeChatMemberInfoDTO {

    /**
     * 成员UserID
     */
    private String userId;

    /**
     * 成员名称
     */
    private String name;

    /**
     * 成员所属部门id列表，仅返回该应用有查看权限的部门id
     */
    private List<Integer> department;

    /**
     * 部门内的排序值，默认为0。数量必须和department一致，数值越大排序越前面。值范围是[0, 2^32)
     */
    private List<Integer> order;

    /**
     * 职位
     */
    private String position;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 性别
     */
    private String gender;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 企业邮箱
     */
    private String bizMail;

    /**
     * 表示在所在的部门内是否为部门负责人
     */
    private String isLeaderInDept;

    /**
     * 直属上级UserID
     */
    private String directLeader;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 头像缩略图
     */
    private String thumbAvatar;

    /**
     * 座机
     */
    private String telephone;

    /**
     * 别名
     */
    private String alias;

    /**
     * 地址
     */
    private String address;

    /**
     * 全局唯一
     */
    private String openUserId;

    /**
     * 主部门，仅当应用对主部门有查看权限时返回
     */
    private String mainDepartment;

    /**
     * 扩展属性
     */
    private String extattr;

    /**
     * 激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业
     */
    private String status;

    /**
     * 员工个人二维码，扫描可添加为外部联系人
     */
    private String qrCode;

    /**
     *对外职务
     */
    private String externalPosition;

    /**
     *对外属性
     */
    private String externalProfile;
}
