/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.domain.message.vo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/10/30 19:24
 * @since JDK 1.8
 */
public class QueryMessageSendStatusVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     *
     */
    private List<MessageSendStatusVO> resultList = new ArrayList<>();

    public List<MessageSendStatusVO> getResultList() {
        return resultList;
    }

    public void setResultList(List<MessageSendStatusVO> resultList) {
        this.resultList = resultList;
    }

    public static class MessageSendStatusVO {

        /**
         * 唯一标识
         */
        private String uniqueId;

        /**
         * 发送状态
         */
        private String sendStatus;

        /**
         * 备注
         */
        private String memo;


        public String getUniqueId() {
            return uniqueId;
        }

        public void setUniqueId(String uniqueId) {
            this.uniqueId = uniqueId;
        }

        public String getSendStatus() {
            return sendStatus;
        }

        public void setSendStatus(String sendStatus) {
            this.sendStatus = sendStatus;
        }

        public String getMemo() {
            return memo;
        }

        public void setMemo(String memo) {
            this.memo = memo;
        }
    }


}