package com.howbuy.crm.wechat.service.service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.howbuy.crm.wechat.service.commom.aes.WXBizMsgCrypt;
import com.howbuy.crm.wechat.service.commom.constant.WxTempConstant;
import com.howbuy.crm.wechat.service.commom.utils.DateUtils;
import com.howbuy.crm.wechat.service.commom.utils.MessageUtil;
import com.howbuy.crm.wechat.service.domain.callback.*;
import com.howbuy.crm.wechat.service.service.config.BaseConfigServce;
import com.howbuy.crm.wechat.service.service.contact.ChangeContactEventService;
import com.howbuy.crm.wechat.service.service.externalcontact.ChangeExternalContactEventService;
import com.howbuy.crm.wechat.service.service.group.ChangeChatEventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @classname: WechatThirdPartyCallBackService
 * @author: hongdong.xie
 * @description: 企业微信第三方应用回调服务
 * @date: 2025-08-19 16:57:27
 * @since: JDK1.8
 */
@Slf4j
@Service
public class WechatThirdPartyCallBackService {

    @Autowired
    private ChangeContactEventService changeContactEventService;
    @Autowired
    private ChangeChatEventService changeChatEventService;
    @Autowired
    private ChangeExternalContactEventService changeExternalContactEventService;

    @Autowired
    private BaseConfigServce baseConfigServce;

    /**
     * @description:根据解密后的第三方应用消息进行落表分发
     * @param wechatCallbackSignature
     * @param companyNo
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2025-08-19 16:57:27
     * @since JDK 1.8
     */
    public String getEncryptRespMessage(WechatCallbackSignatureDTO wechatCallbackSignature, String companyNo) {
        String respMessage = null;
        try {
            log.info("第三方应用getEncryptRespMessage开始xmlMsg:{}", wechatCallbackSignature);
            //2.解析xml字符串
            Map<String, String> requestMap = MessageUtil.parseXml(wechatCallbackSignature.getResult());
            //日志打印解析串
            log.info("第三方应用解析结果:{}", JSON.toJSONString(requestMap));
            //3.获取请求参数
            //3.1 企业微信
            String fromUserName = requestMap.get("FromUserName");
            //3.2 成员UserID
            String toUserName = requestMap.get("ToUserName");
            //3.3 消息类型与事件
            String msgType = requestMap.get("MsgType");
            //业务触发
            Map<String, Object> finalRequestMap = new HashMap<>();
            requestMap.forEach((k,v)->{
                finalRequestMap.put(k,v);
            });
            this.getRespContentByMsgType(finalRequestMap, companyNo);
            //4.组装 回复文本消息
            MessageDTO textMessage = new MessageDTO();
            textMessage.setToUserName(fromUserName);
            textMessage.setFromUserName(toUserName);
            textMessage.setCreateTime(System.currentTimeMillis());
            textMessage.setMsgType(WxTempConstant.TEMP_TEXT_TEXT);
            //4.1.获取回复消息的内容 ：消息的分类处理
            textMessage.setContent("第三方应用成功");

            //5.获取xml字符串： 将（被动回复消息型的）文本消息对象 转成  xml字符串
            respMessage = MessageUtil.textMessageToXml(textMessage);

            //6.加密
            WXBizMsgCrypt wXBizMsgCrypt = baseConfigServce.buildWXBizMsgCrypt(companyNo);
            respMessage = wXBizMsgCrypt.EncryptMsg(respMessage, wechatCallbackSignature.getTimestamp(), msgType);

        } catch (Exception e) {
            e.printStackTrace();
            log.error("第三方应用getEncryptRespMessage Exception:" + e.getMessage(), e);
        }

        return respMessage;
    }

    /**
     * 第三方应用处理消息：根据消息类型获取回复内容
     * getRespContentByMsgType
     * @param requestMap
     * @param companyNo 企微-企业主体
     * @return void
     * @Author: hongdong.xie on 2025-08-19 16:57:27
     */
    public void getRespContentByMsgType(Map<String, Object> requestMap, String companyNo) {

        String msgType = (String) requestMap.get("MsgType");
        //事件推送 第三方应用对应的回调event
        if (msgType.equals(WxTempConstant.REQ_MESSAGE_TYPE_EVENT)) {
            this.processThirdPartyEvent(requestMap, companyNo);
        }
    }

    /**
     * @description:根据第三方应用消息类型判断是微信添加成员回调还是通讯录回调
     * @param requestMap
     * @param companyNo
     * @return void
     * @author: hongdong.xie
     * @date: 2025-08-19 16:57:27
     * @since JDK 1.8
     */
    public void processThirdPartyEvent(Map<String, Object> requestMap, String companyNo) {
        String corpId = baseConfigServce.getCorpId(companyNo);
        log.info("第三方应用WechatThirdPartyCallBackService|processThirdPartyEvent|requestMap:{},corpId:{}", JSON.toJSONString(requestMap), corpId);
        //获取类型
        String eventType = (String) requestMap.get("Event");
        ObjectMapper mapper = new ObjectMapper();
        //忽略驼峰大小写问题
        mapper.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
        //遇到未知属性，直接抛弃
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 获取创建时间
        Long createTime = getCrteateTime(requestMap);
        
        // 第三方应用同样支持外部联系人、通讯录、群聊事件处理
        if (Objects.equals(eventType, WxTempConstant.EVENT_TYPE_EXTERNAL)) {
            ExternalContactDTO externalContactDTO = mapper.convertValue(requestMap, ExternalContactDTO.class);
            externalContactDTO.setCorpId(corpId);
            externalContactDTO.setCreateTime(createTime);
            log.info("第三方应用处理外部联系人事件:{}", JSON.toJSONString(externalContactDTO));
            changeExternalContactEventService.processExternalChangeType(externalContactDTO);
        } else if (Objects.equals(eventType, WxTempConstant.EVENT_TYPE_CONTACT)) {
            ContactDTO contactDTO = mapper.convertValue(requestMap, ContactDTO.class);
            contactDTO.setCorpId(corpId);
            contactDTO.setCreateTime(createTime.toString());
            log.info("第三方应用处理通讯录事件:{}", JSON.toJSONString(contactDTO));
            changeContactEventService.processContactsChangeType(contactDTO);
        }else if (Objects.equals(eventType, WxTempConstant.EVENT_TYPE_EXTERNAL_CHAT)) {
            ChatEventDTO chatEventDTO = mapper.convertValue(requestMap, ChatEventDTO.class);
            chatEventDTO.setCorpId(corpId);
            chatEventDTO.setCreateTime(createTime.toString());
            log.info("第三方应用处理群聊事件:{}", JSON.toJSONString(chatEventDTO));
            changeChatEventService.processChatChangeType(chatEventDTO);
        } else {
            log.warn("第三方应用未知事件类型:{}, requestMap:{}", eventType, JSON.toJSONString(requestMap));
        }
    }

    /**
     * @description: 获取创建时间
     * @param requestMap
     * @return java.lang.Long
     * @author: hongdong.xie
     * @date: 2025-08-19 16:57:27
     * @since JDK 1.8
     */
    private static Long getCrteateTime(Map<String, Object> requestMap) {

        Object createTimeObj = requestMap.get("CreateTime") != null ? requestMap.get("CreateTime") : requestMap.get("createTime");

        // 如果为空取当前时间
        if (createTimeObj == null) {
            return System.currentTimeMillis();
        }
        Long createTime = Long.parseLong(createTimeObj.toString());

        // 外部给的部分 * 了1000，所以此处要判断下是否乘过了
        int year = Integer.parseInt(DateUtils.dateFormatToString(new Date(createTime),DateUtils.YYYYMMDD));
        // 如果格式化的日期小于2020年，说明是乘过1000的
        if (year < 20100101) {
            createTime = createTime * 1000;
        }

        return createTime;
    }
}