/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.facade;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.group.GetGroupInfoByUserIdRequest;
import com.howbuy.crm.wechat.client.domain.request.group.GetGroupInfoByChatIdRequest;
import com.howbuy.crm.wechat.client.domain.request.group.GetGroupInfoByExternalUserIdRequest;
import com.howbuy.crm.wechat.client.domain.response.group.GroupChatInfoVO;
import com.howbuy.crm.wechat.client.facade.group.WechatGroupFacade;
import com.howbuy.crm.wechat.service.service.group.WechatGroupBusinessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 微信群管理接口实现
 * @date 2025-08-19 19:05:03
 * @since JDK 1.8
 */
@DubboService
@Slf4j
public class WechatGroupFacadeImpl implements WechatGroupFacade {

    @Resource
    private WechatGroupBusinessService wechatGroupBusinessService;

    @Override
    public Response<List<GroupChatInfoVO>> getGroupInfoByUserId(GetGroupInfoByUserIdRequest request) {
        return Response.ok(wechatGroupBusinessService.getGroupInfoByUserId(request));
    }

    @Override
    public Response<GroupChatInfoVO> getGroupInfoByChatId(GetGroupInfoByChatIdRequest request) {
        return Response.ok(wechatGroupBusinessService.getGroupInfoByChatId(request));
    }

    @Override
    public Response<List<GroupChatInfoVO>> getGroupInfoByExternalUserId(GetGroupInfoByExternalUserIdRequest request) {
        return Response.ok(wechatGroupBusinessService.getGroupInfoByExternalUserId(request));
    }
}