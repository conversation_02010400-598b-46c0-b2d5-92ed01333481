/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.config;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.howbuy.crm.wechat.dao.po.CmWechatApplicationPO;
import com.howbuy.crm.wechat.dao.po.CmWechatCompanyPO;
import com.howbuy.crm.wechat.service.cacheservice.AbstractCacheService;
import com.howbuy.crm.wechat.service.commom.utils.ApplicationUtilityDTO;
import com.howbuy.crm.wechat.service.commom.utils.CorpUtilityDTO;
import com.howbuy.crm.wechat.service.repository.CmWechatCorpConfigRepository;
import crm.howbuy.base.enums.YesOrNoEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @description: (企微基础配置  缓存 service)
 * <AUTHOR>
 * @date 2025/7/24 10:01
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CacheBaseConfigServce  extends AbstractCacheService implements InitializingBean {

    @Autowired
    private CmWechatCorpConfigRepository corpConfigRepository;

    @Value("${spring.profiles.active}")
    private String envCode;



    /**
     * 企微 企业 基础配置缓存 key
     */
    private static final String WECHAT_CORP_PARTITION ="WECHAT_CORP_CONFIG|";

    /**
     * 企微 企业 基础配置缓存 key
     */
    private static final String WECHAT_APPLICATION_PARTITION ="WECHAT_APPLICATION_CONFIG|";

    @Override
    public void afterPropertiesSet() throws Exception {
       log.info("envCode:{} , 重新加载企微基础配置缓存！", envCode);
       reloadCorpConfig();
       reloadAppConfig();
    }

    /**
     * 重新加载企微 企业 缓存
     */
    public void reloadCorpConfig() {
        log.info("envCode:{} , 重新加载[企业]配置缓存！", envCode);
        List<CmWechatCompanyPO> corpConfigList = corpConfigRepository.selectCorpConfigList(envCode);
        corpConfigList.forEach(this::syncCorpToCache);
    }

   /**
     * 根据companyNo 刷新企微企业缓存
     * @param companyNo
     */
    public void reloadCorpConfigByConpanyNo(String companyNo){
        log.info("envCode:{} ,companyNo:{} 重新加载[企业]配置缓存！", envCode,companyNo);
        CmWechatCompanyPO companyPO = corpConfigRepository.getCorpInfoByCompanyNo(envCode,companyNo);
        if(companyPO == null){
            log.info("企微企业companyNo:{} 配置不存在！", companyNo);
            return;
        }
        syncCorpToCache(companyPO);
    }

    /**
     * 同步企微企业 数据库配置 --> 缓存
     * @param companyPO
     */
    private void syncCorpToCache(CmWechatCompanyPO companyPO){
        String companyNo = companyPO.getCompanyNo();
        if(isValid(companyPO)){
            CorpUtilityDTO corpUtilityDTO = buidlCorpUtilityDTO(companyPO);
            Long result = CACHE_SERVICE.putObjToMap(getCorpCacheName(),companyNo,corpUtilityDTO);
            log.info("企微企业companyNo:{} 配置:{}，缓存同步刷新！ 缓存刷新成功结果：{}", companyNo, JSON.toJSONString(corpUtilityDTO),result);
        }else{
            Long result = CACHE_SERVICE.removeFromMap(getCorpCacheName(),companyNo);
            log.info("企微企业companyNo:{} 配置已失效:{}，企业缓存同步删除！刷新结果：{}", companyNo, JSON.toJSONString(companyPO),result);
        }
    }

    /**
     * 获取企微企业缓存名称
     * @return
     */
    private String getCorpCacheName(){
        return String.join("",WECHAT_CORP_PARTITION, envCode);
    }

    /**
     * 获取企微企业-应用 缓存名称
     * @return
     */
    private String getApplicationCacheName(){
        return String.join("",WECHAT_APPLICATION_PARTITION, envCode);
    }




    /**
     * 构建企微企业配置信息
     * @param companyPO
     * @return
     */
    private CorpUtilityDTO buidlCorpUtilityDTO(CmWechatCompanyPO companyPO){
        String companyNo = companyPO.getCompanyNo();
        CorpUtilityDTO corpUtilityDTO = new CorpUtilityDTO();
        corpUtilityDTO.setCompanyNo(companyNo);
        corpUtilityDTO.setCompanyDesc(companyPO.getCompanyDesc());
        corpUtilityDTO.setCorpId(companyPO.getCorpId());
        corpUtilityDTO.setCorpType(companyPO.getCorpType());
        corpUtilityDTO.setToken(companyPO.getToken());
        corpUtilityDTO.setEncodingAesKey(companyPO.getEncodingAesKey());
        corpUtilityDTO.setCustomerSecret(companyPO.getCustomerSecret());
        corpUtilityDTO.setEnvCode(companyPO.getEnvCode());
        return corpUtilityDTO;
    }



    /**
     * 根据companyNo 查询有效的公司配置信息
     * @param companyNo  企微-企业主体
     * @return
     */
    public CorpUtilityDTO getCorpConfig(String companyNo) {
        return (CorpUtilityDTO)CACHE_SERVICE.getObjFromMap(getCorpCacheName(), companyNo);
    }

    /**
     * 获取所有公司配置信息
     * @return
     */
    public Map<String,CorpUtilityDTO> getCorpConfigMap() {
        Map<String, CorpUtilityDTO> returnMap= Maps.newHashMap();
        Map<String, Object> cacheMap=CACHE_SERVICE.getFromObjMap(getCorpCacheName());
        cacheMap.forEach((companyNo,value)->{
            returnMap.put(companyNo,(CorpUtilityDTO)value);
        });
        return returnMap;
    }




    //---------------------------------------------------------------------------------------------------------------------
    /**
     * 重新加载企微 应用 缓存
     */
    public void reloadAppConfig() {
        log.info("envCode:{} 重新加载[企业-应用]配置缓存！", envCode);
        List<CmWechatApplicationPO> appConfigList = corpConfigRepository.selectApplicationConfigList(envCode);
        putConfigListToCache(appConfigList);
    }

    /**
     * 根据companyNo 刷新企微企业应用 缓存
     * @param companyNo
     */
    public void reloadAppConfigByCompanyNo(String companyNo) {
        log.info("envCode:{} ,companyNo:{} 重新加载[企业-应用]配置缓存！", envCode,companyNo);
        List<CmWechatApplicationPO> appConfigList = corpConfigRepository.selectApplicationConfigList(envCode);
        appConfigList.removeIf(applicationInfo -> !applicationInfo.getCompanyNo().equals(companyNo));
        putConfigListToCache(appConfigList);
    }




    /**
     * 判断企微企业信息是否有效
     * @param corpInfo
     * @return
     */
    private  boolean isValid(CmWechatCompanyPO corpInfo){
        return  corpInfo!=null &&   YesOrNoEnum.YES.getCode().equals(corpInfo.getRecStat());
    }


    /**
     * 批量添加企微企业应用配置信息 --> cache中
     * @param appConfigList
     */
    private void putConfigListToCache(List<CmWechatApplicationPO> appConfigList){
        for (CmWechatApplicationPO applicationInfo : appConfigList) {
            syncAppToCache(applicationInfo);
        }
    }

     private  void syncAppToCache(CmWechatApplicationPO applicationInfo) {
         String applicationCode = applicationInfo.getApplicationCode();
         //归属 公司
         String companyNo = applicationInfo.getCompanyNo();
         CmWechatCompanyPO corpInfo = corpConfigRepository.getCorpInfoByCompanyNo(envCode,companyNo);
         if (corpInfo == null) {
             log.error("企微应用：{} 对应companyNo:{},配置不存在！", applicationCode, companyNo);
             return;
         }
         String  cacheName = getApplicationCacheName();
         if (!isValid(corpInfo)){
             Long result=CACHE_SERVICE.removeFromMap(cacheName, applicationCode);
             log.info("企微应用：{} 对应companyNo:{} 企业配置已失效:{}，应用缓存同步删除！刷新结果：{}",
                     applicationCode,companyNo, JSON.toJSONString(corpInfo),result);
             return;
         }
         if (!YesOrNoEnum.YES.getCode().equals(applicationInfo.getRecStat())){
             Long result=CACHE_SERVICE.removeFromMap(cacheName, applicationCode);
             log.info("企微应用：{} 应用配置已失效:{}，应用缓存同步删除！刷新结果：{}",
                     applicationCode, JSON.toJSONString(applicationInfo),result);
             return;
         }

         ApplicationUtilityDTO utilityDTO = new ApplicationUtilityDTO();
         //应用信息
         utilityDTO.setApplicationCode(applicationCode);
         utilityDTO.setApplicationType(applicationInfo.getApplicationType());
         utilityDTO.setAgentId(applicationInfo.getAgentId());
         utilityDTO.setAccessSecret(applicationInfo.getAccessSecret());
//            [企业微信- 其他应用：Eg: 商路通、CRM、机构服务通知]缓存后缀名称 ：
//     *       applicationCode_envCode [NOTICE: 实际最好使用 agentId . 以解决：同一应用在两套环境，实际缓存一样]
         utilityDTO.setCacheSuffixName(String.join("_",applicationCode,applicationInfo.getEnvCode()));
         utilityDTO.setEnvCode(applicationInfo.getEnvCode());

         //补充该公司信息
         utilityDTO.setCompanyNo(companyNo);
         utilityDTO.setCorpId(corpInfo.getCorpId());
         utilityDTO.setToken(corpInfo.getToken());
         utilityDTO.setEncodingAesKey(corpInfo.getEncodingAesKey());

         CACHE_SERVICE.putObjToMap(cacheName, applicationCode, utilityDTO);
         log.info("企微应用：{}，所属企业companyNo:{} 配置:{}，缓存刷新成功！", applicationCode, companyNo, JSON.toJSONString(utilityDTO));
    }

    /**
     * 根据 applicationCode 查询有效的公司配置信息
     * @param applicationCode  企微-应用主体
     * @return
     */
    public ApplicationUtilityDTO getApplicationConfig(String applicationCode) {
        return (ApplicationUtilityDTO)CACHE_SERVICE.getObjFromMap(getApplicationCacheName(), applicationCode);
    }

    /**
     * 获取所有公司应用配置信息
     * @return
     */
    public Map<String,ApplicationUtilityDTO> getApplicationConfigMap() {
        Map<String, ApplicationUtilityDTO> returnMap= Maps.newHashMap();
        Map<String, Object> cacheMap=CACHE_SERVICE.getFromObjMap(getApplicationCacheName());
        cacheMap.forEach((applicationCode,utilityDTO)->{
            returnMap.put(applicationCode,(ApplicationUtilityDTO)utilityDTO);
        });
        return returnMap;
    }


}