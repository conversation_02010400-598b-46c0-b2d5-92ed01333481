/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.message.build;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.wechat.client.enums.AcceptMessageTypeEnum;
import com.howbuy.crm.wechat.client.enums.MessageSendChannelEnum;
import com.howbuy.crm.wechat.client.enums.MessageSendStatusEnum;
import com.howbuy.crm.wechat.dao.po.message.MessageAcceptInfoPO;
import com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO;
import com.howbuy.crm.wechat.service.domain.message.vo.EmailMessageVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 邮箱
 * @date 2023/11/29 1:58 PM
 * @since JDK 1.8
 */
@Service
@Slf4j
public class EmailBuilderService extends AbstractMessageBuildService {


    @Override
    public List<AcceptMessageTypeEnum> getMsgTypeList() {
        return Lists.newArrayList(AcceptMessageTypeEnum.EMAIL);
    }

    @Override
    public List<MessageSendInfoPO> buildSpecificMessage(MessageAcceptInfoPO acceptInfo) {
        List<MessageSendInfoPO> resList = new ArrayList<>();
        MessageSendInfoPO messageSendInfoPO = new MessageSendInfoPO();
        messageSendInfoPO.setAcceptId(acceptInfo.getId());
        messageSendInfoPO.setTempleteId(acceptInfo.getTemplateId());

        // 处理MessageAcceptInfoPO中messageParams、templateParams的逻辑：将templateParams作为messageParams的一个key-value存储起来
        String messageParams = acceptInfo.getMessageParams();
        EmailMessageVo emailMessageVo = JSON.parseObject(messageParams, EmailMessageVo.class);
        emailMessageVo.setTemplateParams(acceptInfo.getTemplateParams());
        messageSendInfoPO.setTempleteParams(JSON.toJSONString(emailMessageVo));

        messageSendInfoPO.setSendChannel(MessageSendChannelEnum.EMAIL.getCode());
        messageSendInfoPO.setSendDt(DateUtil.formatNowDate(DateUtil.SHORT_DATE_PATTERN));
        // 发送状态为0-未推送，消息发送次数设置为默认值0
        messageSendInfoPO.setSendStatus(MessageSendStatusEnum.UNPUSH.getCode());
        messageSendInfoPO.setSendTimes(0);
        messageSendInfoPO.setCreateTime(new Date());

        resList.add(messageSendInfoPO);
        return resList;
    }
}