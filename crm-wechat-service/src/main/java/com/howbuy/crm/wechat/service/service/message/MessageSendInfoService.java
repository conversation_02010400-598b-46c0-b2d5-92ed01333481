package com.howbuy.crm.wechat.service.service.message;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.howbuy.crm.wechat.client.enums.MessageSendStatusEnum;
import com.howbuy.crm.wechat.dao.mapper.message.MessageSendInfoMapper;
import com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO;
import com.howbuy.crm.wechat.dao.vo.message.MessageSendInfoVO;
import com.howbuy.crm.wechat.service.domain.message.SendResultDTO;
import com.howbuy.crm.wechat.service.domain.message.UpdateMessageSendStatusDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/10/7 13:19
 * @since JDK 1.8
 */
@Service
public class MessageSendInfoService{

    @Resource
    private MessageSendInfoMapper messageSendInfoMapper;

    
    public int deleteByPrimaryKey(Long id) {
        return messageSendInfoMapper.deleteByPrimaryKey(id);
    }

    
    public int insert(MessageSendInfoPO record) {
        return messageSendInfoMapper.insert(record);
    }

    public int batchInsert(List<MessageSendInfoPO> recordList) {
        return messageSendInfoMapper.batchInsert(recordList);
    }

    
    public int insertSelective(MessageSendInfoPO record) {
        return messageSendInfoMapper.insertSelective(record);
    }

    
    public MessageSendInfoPO selectByPrimaryKey(Long id) {
        return messageSendInfoMapper.selectByPrimaryKey(id);
    }


    /**
     * @description: 根据接收信息id 查询发送信息
     * @param acceptId 接收信息id
     * @return com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO 发送信息
     * @author: jin.wang03
     * @date: 2024/3/22 14:41
     * @since JDK 1.8
     */
    public List<MessageSendInfoPO> selectByAcceptId(Long acceptId) {
        return messageSendInfoMapper.selectByAcceptId(acceptId);
    }

    
    public int updateByPrimaryKeySelective(MessageSendInfoPO record) {
        return messageSendInfoMapper.updateByPrimaryKeySelective(record);
    }

    
    public int updateByPrimaryKey(MessageSendInfoPO record) {
        return messageSendInfoMapper.updateByPrimaryKey(record);
    }


    /**
     * 分页查询 消息发送信息
     * @param acceptInfoVO
     * @return
     */
    public Page<MessageSendInfoPO> selectPageByVo(MessageSendInfoVO acceptInfoVO){
        PageHelper.startPage(acceptInfoVO.getPage(), acceptInfoVO.getRows());
        return messageSendInfoMapper.selectPageByVo(acceptInfoVO);
    }



    /**
     * 批量 更新  消息发送状态
     * @param sendResultDTO
     * @param id
     * @return
     */
    public int updateStatusAfterSend(SendResultDTO sendResultDTO, Long id) {
        Assert.notNull(sendResultDTO, "sendResultDTO is null");
        Assert.notNull(id, "idList is empty");

        // 如果发送消息具体的渠道，根据业务逻辑对sendStatus已经赋值了，则使用sendStatus  --- 2024年10月30号
        String sendStatus = sendResultDTO.getSendStatus();
        if (StringUtils.isBlank(sendStatus)) {
            // 兜底逻辑：根据[调用接口是否成功]来赋值
            // ！！注意: 这个兜底逻辑有漏洞，外部消息发送接口 调用成功，只意味着：受理成功，而不是真实已经发送成功
            MessageSendStatusEnum statusEnum = sendResultDTO.isSuccess()
                    ? MessageSendStatusEnum.PUSH_SUCCESS
                    : MessageSendStatusEnum.PUSH_FAIL;
            sendStatus = statusEnum.getCode();
        }

        return messageSendInfoMapper.updateSendStatus(id,
                sendStatus,
                sendResultDTO.getCode(),
                sendResultDTO.getMsg(),
                sendResultDTO.getMessageUUID());
    }


    /**
     * @description: 更新 【发送中】消息的发送状态
     * @param sendResultDTO
     * @return int
     * @author: jin.wang03
     * @date: 2024/10/30 11:24
     * @since JDK 1.8
     */
    public int updateStatusForPushing(UpdateMessageSendStatusDTO sendResultDTO, Long id) {
        Assert.notNull(sendResultDTO, "sendResultDTO is null");

        return messageSendInfoMapper.updateStatusForPushing(id,
                sendResultDTO.getSendStatus(),
                sendResultDTO.getResponseCode(),
                sendResultDTO.getResponseMsg());
    }


    /**
     * @description: 根据查询条件 分页查询消息发送信息
     * @param queryVo
     * @return com.github.pagehelper.Page<com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO>
     * @author: jin.wang03
     * @date: 2025/3/26 16:06
     * @since JDK 1.8
     */
    public Page<MessageSendInfoPO> selectPageByVoAndSixHours(MessageSendInfoVO queryVo) {
        PageHelper.startPage(queryVo.getPage(), queryVo.getRows());
        return messageSendInfoMapper.selectPageByVoAndSixHours(queryVo);
    }

}
