package com.howbuy.crm.wechat.service.service.message.build;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.wechat.client.enums.AcceptMessageTypeEnum;
import com.howbuy.crm.wechat.client.enums.MessageSendChannelEnum;
import com.howbuy.crm.wechat.client.enums.MessageSendStatusEnum;
import com.howbuy.crm.wechat.dao.po.message.MessageAcceptInfoPO;
import com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.commom.constant.DfileConstants;
import com.howbuy.crm.wechat.service.commom.enums.CpfxEnum;
import com.howbuy.crm.wechat.service.commom.enums.CurrencyEnum;
import com.howbuy.crm.wechat.service.commom.enums.HbProductLineEnum;
import com.howbuy.crm.wechat.service.commom.utils.FileUtil;
import com.howbuy.crm.wechat.service.domain.message.MarketReportMessageParamDTO;
import com.howbuy.crm.wechat.service.domain.message.vo.BotMessageVo;
import com.howbuy.crm.wechat.service.domain.message.vo.TextBotMessageVo;
import com.howbuy.dfile.HFileService;
import crm.howbuy.base.constants.StaticVar;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.Date;
import java.util.List;

/**
 * @description: (营销喜报  发送消息 处理 )
 * <AUTHOR>
 * @date 2023/10/7 9:59
 * @since JDK 1.8
 */
@Service
@Slf4j
public class MarketReportingBuildService extends AbstractMessageBuildService implements MessageBuildService {

    /**
     * 营销喜报 模板图片 名称
     */
    private static final String BACKGROUND_IMAGE_NAME ="market_report.png";

    /**
     * IC营销喜报 模板图片 名称
     */
    private static final String IC_BACKGROUND_IMAGE_NAME ="ic_market_report.png";

    /**
     * HBC营销喜报 模板图片 名称
     */
    private static final String HBC_BACKGROUND_IMAGE_NAME ="hbc_market_report.png";


    /**
     * 非证券类私募海报 模版图片 名称 （复用了 HBC营销喜报的模板图片）
     */
    private static final String NON_SECURITIES_BACKGROUND_IMAGE_NAME ="hbc_market_report.png";


    /**
     * 成功配置-中文文案
     */
    private static final String SUCCESSFULLY_CONFIGURED = "成功配置";




    /**
     * 生成图片后缀
     */
    private static final String SUFFIX_NAME=".png";

    /**
     * 字体名称
     */
    private static final String FONT_NAME="Microsoft YaHei";

    /**
     * 营销喜报-发送 消息的  webHookUrlKey-机器人配置
     */
    @Value("${marketReport.HookUrlKey}")
    private String marketReportHookUrlKey;


    /**
     * IC-营销喜报-发送 消息的  webHookUrlKey-机器人配置
     */
    @Value("${marketReport.ic.HookUrlKey}")
    private String icMarketReportHookUrlKey;

    /**
     * HBC-营销喜报-发送 消息的  webHookUrlKey-机器人配置
     */
    @Value("${marketReport.hbc.HookUrlKey}")
    private String hbcMarketReportHookUrlKey;

    /**
     * 财富管理中心
     */
    private static final String IC = "1";

    /**
     * 高端业务中心
     */
    private static final String HBC = "10";

    /**
     * 产品类型-FOF
     */
    private static final String FOF_CODE = "501";

    /**
     * 神秘大咖
     */
    private static final String MYSTERY_MEN = "神秘大咖";

    /**
     * FOF产品
     */
    private static final String FOF_NAME = "FOF产品";

    /**
     * 【非证券类产品】且分销渠道 = 海外 时，海报展示的产品名称
     */
    private static final String HW_STR = "HW";

    /**
     * 【非证券类产品】且分销渠道 = 好臻 时，海报展示的产品名称
     */
    private static final String GQ_STR = "GQ";



    public  List<AcceptMessageTypeEnum> getMsgTypeList(){
        return Lists.newArrayList(AcceptMessageTypeEnum.MARKETING_REPORT);
    }




    /**
     * @description: 话术处理
     * @param paramBean 消息参数
     * @return java.lang.String 话术
     * @author: jin.wang03
     * @date: 2024/3/20 17:40
     * @since JDK 1.8
     */
    private String  getTextContent(MarketReportMessageParamDTO paramBean) {
        String consName = paramBean.getConsName();
        String prodName = paramBean.getProdName();


        // 非证券类私募文案：
        // 恭喜{$投顾姓名拼音大写字母缩写+域账号数字}！
        // 域账号含有数字标识的投顾，示例：恭喜LS01！
        // 在途投顾，示例： 恭喜神秘大咖！
        if (!CpfxEnum.HB.getCode().equals(paramBean.getCpfx())) {
            consName = paramBean.getConsNamePinyinInitial();
            //
            if (StaticVar.YES.equals(paramBean.getIsOnTheWay())) {
                consName = MYSTERY_MEN;
            }
            return String.join("",
                    "/:rose",
                    "恭喜",
                    consName,
                    "！"
            );
        }

        // 总体文案：[玫瑰][玫瑰]热烈祝贺${CRM区域}${CRM部门}${CRM所属投顾姓名}，今日成功配置${CRM产品名称} ${CRM打款确认金额}${单位}${币种}

        // {$好买产品线}=PE\VC\海外 投顾名字打星号，{$好买产品线}=除了(PE、VC、海外)之外的取值 显示投顾全名
        if (HbProductLineEnum.PE_VC.getCode().equals(paramBean.getHbType())
                || HbProductLineEnum.OVERSEAS.getCode().equals(paramBean.getHbType())) {
            consName = hideName(consName);
        }
        //规则3：在途投顾：热烈祝贺 XX区域XX分部神秘大咖，今日成功配置【产品名称】XX万
        if (StaticVar.YES.equals(paramBean.getIsOnTheWay())) {
            consName = MYSTERY_MEN;
        }

        // {$好买产品线}=除了(PE、VC、海外)之外的取值 且 {$产品类型(对内)}=FOF，产品名称前加"FOF产品"
        if (!HbProductLineEnum.PE_VC.getCode().equals(paramBean.getHbType())
                && !HbProductLineEnum.OVERSEAS.getCode().equals(paramBean.getHbType())
                && FOF_CODE.equals(paramBean.getStrategyClassify())) {
            prodName = FOF_NAME + prodName;
        }

        // XXX万/亿 （如果是外币，需展示 外币单位）（如果数额>=9位数，显示XX亿）
        String currencyDescription = CurrencyEnum.getDescription(paramBean.getCurrency());
        String currencyName = CurrencyEnum.RMB.getCode().equals(paramBean.getCurrency()) ? "" : currencyDescription;
        // ic群需要产品名称后面加空格
        prodName = IC.equals(paramBean.getCenterOrgCode()) ? prodName + " " :  prodName;
        // ic群不展示${CRM部门}
        String orgName = IC.equals(paramBean.getCenterOrgCode()) ? "" : paramBean.getOrgName();

        return String.join("",
                "/:rose /:rose",
                "热烈祝贺",
                StringUtil.null2String(paramBean.getDistrictName()),
                orgName,
                consName,
                "，今日成功配置",
                prodName,
                paramBean.getDisPlayAmtValue(),
                paramBean.getDisplayUnitValue(),
                currencyName,
                "[Party][Party]"
        );
    }

    /**
     * @description: 投顾姓名隐藏加*
     * 统一按倒数 第二个字进行脱敏
     *  2个字人名 *B
     *  3个字人名 A*C
     *  4个字人名 AB*D
     * @param consName
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2024/3/20 16:09
     * @since JDK 1.8
     */
    private static String hideName(String consName) {
        if (StringUtils.isNotBlank(consName)) {
            int length = consName.length();
            if (length == 0 || length == 1) {
                consName = "*";
            } else {
                consName = consName.charAt(0) + "*" + consName.substring(2);
            }

        }
        return consName;
    }


    /**
     * @description: (子类覆写：不同的消息类型，构建不同的待发送消息)
     *
     * 证券类私募vs非证券类私募,定义:
     *      取【产品预约管理-产品分销】
     *      证券类私募：产品分销=好买
     *      非证券类私募：产品分销=好臻 OR 海外
     * @return
     * @return: java.util.List<com.howbuy.crm.wechat.client.enums.AcceptMessageTypeEnum>
     * @since JDK 1.8
     */
    @Override
    public List<MessageSendInfoPO> buildSpecificMessage(MessageAcceptInfoPO acceptInfo){
        List<MessageSendInfoPO> buildList=Lists.newArrayList();
        //根据接收消息参数内容，生成图片
        MarketReportMessageParamDTO paramBean = transferToParamBean(acceptInfo.getMessageParams(), MarketReportMessageParamDTO.class);
        log.info("buildSpecificMessage解析得到的DTO为：{}", JSON.toJSONString(paramBean));
        // IC团队 和 HBC团队各有一个企微群，所以 机器人配置url不同
        String hookUrlKey = IC.equals(paramBean.getCenterOrgCode()) ? icMarketReportHookUrlKey :  hbcMarketReportHookUrlKey;
        //[玫瑰][玫瑰]热烈祝贺${CRM区域}${CRM所属投顾姓名}，今日成功配置${CRM产品名称} ${CRM打款确认金额}万${CRM打款币种}
        String textContent = getTextContent(paramBean);

        MessageSendInfoPO imgSendInfo = geImgSendInfoPO(acceptInfo, paramBean, hookUrlKey, textContent);
        if (imgSendInfo != null) {
            buildList.add(imgSendInfo);
        }

        //生成话术 信息
        MessageSendInfoPO textSendInfo=initSendInfoPO(acceptInfo.getId());
        BotMessageVo  textMessageVo=getTextMessageVo(hookUrlKey, textContent);
        textSendInfo.setTempleteParams(JSON.toJSONString(textMessageVo));
        buildList.add(textSendInfo);
        return buildList;
    }

    /**
     * @description: 构建图片发送消息
     * @param acceptInfo
     * @param paramBean
     * @param hookUrlKey
     * @param textContent
     * @return com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO
     * @author: jin.wang03
     * @date: 2024/3/20 19:54
     * @since JDK 1.8
     */
    private MessageSendInfoPO geImgSendInfoPO(MessageAcceptInfoPO acceptInfo, MarketReportMessageParamDTO paramBean, String hookUrlKey, String textContent) {
        MessageSendInfoPO imgSendInfo = null;
        InputStream stream = null;
        try {
            //文件夹名称：按日期存放
            String relativePath = File.separator + DateUtil.formatNowDate(DateUtil.SHORT_DATE_PATTERN);
            //文件名称：唯一标识_时间戳
            String fileName = String.join("_", acceptInfo.getUniqueId(), DateUtil.date2String(new Date(), DateUtil.StR_PATTERN_HHMMSS))
                    + SUFFIX_NAME;

            // 生成图片
            stream = getImgStream(paramBean);
            if (stream != null) {
                HFileService instance = HFileService.getInstance();
                instance.write(DfileConstants.MARKET_REPORT_CONFIG, relativePath, fileName, FileUtil.inputStreamToByteArray(stream));
            }

            //发送相关 属性 。存储在 templeteParams 字段中
            BotMessageVo messageVo = getMessageVo(hookUrlKey, relativePath, fileName, textContent);

            imgSendInfo = initSendInfoPO(acceptInfo.getId());
            imgSendInfo.setTempleteParams(JSON.toJSONString(messageVo));

        }catch (Exception e){
            log.error("构建失败！",e);
            if(stream!=null){
                try {
                    stream.close();
                }catch (Exception ex){
                    log.error("关闭流失败！",ex);
                }
            }
        }
        return imgSendInfo;
    }

    /**
     * 构建纯文本 机器人发送 参数
     * @param textContent
     * @return
     */
    private BotMessageVo getTextMessageVo(String hookUrlKey, String textContent){
        BotMessageVo  textMessageVo=new BotMessageVo();
        //1:文本, 4:图片 , 5:图文
        textMessageVo.setMessageType(1);
        //机器人配置的key
        textMessageVo.setWebHookUrlKey(hookUrlKey);
        TextBotMessageVo textVo=new TextBotMessageVo();
        textVo.setContent(textContent);
        textMessageVo.setTextBotMessage(textVo);
        return textMessageVo;
    }

    /**
     * 初始化 机器人发送 build消息 信息
     * @param acceptId
     * @return
     */
    private MessageSendInfoPO initSendInfoPO(Long acceptId){
        MessageSendInfoPO botSendInfo=new MessageSendInfoPO();
        botSendInfo.setAcceptId(acceptId);
        botSendInfo.setSendDt(DateUtil.formatNowDate(DateUtil.SHORT_DATE_PATTERN));
        //发送状态为0-未推送，消息发送次数设置为默认值0
        botSendInfo.setSendStatus(MessageSendStatusEnum.UNPUSH.getCode());
        botSendInfo.setSendTimes(0);
        botSendInfo.setCreateTime(new Date());
        botSendInfo.setSendChannel(MessageSendChannelEnum.BOT_MESSAGE.getCode());
        botSendInfo.setTempleteId(null);
        return  botSendInfo;
    }


    public BotMessageVo getMessageVo(String hookUrlKey, String relativePath, String fileName, String textContent) {
        BotMessageVo messageVo = new BotMessageVo();
        //1:文本, 4:图片 , 5:图文
        messageVo.setMessageType(4);
        //机器人配置的key
        messageVo.setWebHookUrlKey(hookUrlKey);
        // 文件
        messageVo.setRelativePath(relativePath);
        messageVo.setFileName(fileName);

        TextBotMessageVo textVo = new TextBotMessageVo();
        textVo.setContent(textContent);
        messageVo.setTextBotMessage(textVo);
        return messageVo;
    }

    /**
     * 生成图片
     * @param paramBean
     */
    private InputStream getImgStream(MarketReportMessageParamDTO paramBean) {
        if (!CpfxEnum.HB.getCode().equals(paramBean.getCpfx())) {
            // 非证券类私募 海报
            return getNonSecuritiesInputStream(paramBean);
        }
        // 区域、投顾描述
        String districtDesc = getDistrictDesc(paramBean);
        //产品名称，Eg: 华宝信托-宝幡稳健回报十八号-第一期
        String prodName = paramBean.getProdName();
        //金额(万)，Eg: 1000
        String amtValue = paramBean.getDisPlayAmtValue();
        String unitValue = paramBean.getDisplayUnitValue();

        InputStream inputStream = null;
        try {
            // IC 和 HBC的海报模版不同，需要区分
            String imgName = IC.equals(paramBean.getCenterOrgCode()) ? IC_BACKGROUND_IMAGE_NAME : HBC_BACKGROUND_IMAGE_NAME;
            //File imageFile = FileUtil.getFile(String.join(File.separator,getTemplateImgPath(), imgName));
            HFileService instance = HFileService.getInstance();
            //这里的后两个参数应该和放入文件的时候一样
            byte[] fileData = instance.read2Bytes(DfileConstants.MARKET_REPORT_TEMPLATE_CONFIG, "", imgName);
            log.info("getImgStream:fileData:{}", fileData.length);
            if (fileData.length==0) {
                throw new BusinessException(Constants.SYSTEM_ERROR, "模板图片不存在");
            }
            // 使用ByteArrayInputStream包装byte数组
            ByteArrayInputStream ins = new ByteArrayInputStream(fileData);
            BufferedImage originalImage = ImageIO.read(ins);
            if (IC.equals(paramBean.getCenterOrgCode())) {
                icGraphics(originalImage, districtDesc, prodName, amtValue, unitValue);
            } else {
                hbcGraphics(originalImage, districtDesc, prodName, amtValue, unitValue);
            }

            // Save the modified image with added districtDesc

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(originalImage, "png", outputStream);
            outputStream.flush();

            //转换为 InputStream
            inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        } catch (Exception e) {
            log.error("生成图片失败", e);
        } finally {
             //释放资源 TODO:

        }
        return inputStream;
    }

    /**
     * @description:(请在此添加描述)
     *
     * {$投顾姓名拼音大写字母缩写+域账号数字}；域账号含有数字标识的投顾，示例 LS01
     *  在途投顾，海报仍显示神秘大咖
     *  产品名称 改成 产品代码
     *  金额 不显示 外币单位，统一不显示单位（人民币\美元\港币 均不显示）
     *  喜报文案示例：热烈祝贺 ZS01 成功配置 H04243 100万
     *              热烈祝贺 神秘大咖 成功配置 JE0027  200万
     * @param paramBean
     * @return java.io.InputStream
     * @author: jin.wang03
     * @date: 2024/10/10 17:12
     * @since JDK 1.8
     */
    private static InputStream getNonSecuritiesInputStream(MarketReportMessageParamDTO paramBean) {
        String consName = paramBean.getConsNamePinyinInitial();
        if (StaticVar.YES.equals(paramBean.getIsOnTheWay())) {
            consName = MYSTERY_MEN;
        }
        // 产品代码
        String prodCode;
        if (CpfxEnum.HW.getCode().equals(paramBean.getCpfx())) {
            prodCode = HW_STR;
        } else {
            prodCode = GQ_STR;
        }

        //金额(万)，Eg: 1000
        String amtValue = paramBean.getDisPlayAmtValue();
        String unitValue = paramBean.getDisplayUnitValue();

        InputStream inputStream = null;
        try {
            HFileService instance = HFileService.getInstance();
            //这里的后两个参数应该和放入文件的时候一样
            byte[] fileData = instance.read2Bytes(DfileConstants.MARKET_REPORT_TEMPLATE_CONFIG, "", NON_SECURITIES_BACKGROUND_IMAGE_NAME);
            log.info("getImgStream:fileData:{}", fileData.length);
            if (fileData.length == 0) {
                throw new BusinessException(Constants.SYSTEM_ERROR, "模板图片不存在");
            }
            // 使用ByteArrayInputStream包装byte数组
            ByteArrayInputStream ins = new ByteArrayInputStream(fileData);
            BufferedImage originalImage = ImageIO.read(ins);

            nonSecuritiesGraphics(originalImage, consName, prodCode, amtValue, unitValue);

            // Save the modified image with added districtDesc

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(originalImage, "png", outputStream);
            outputStream.flush();

            //转换为 InputStream
            inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        } catch (Exception e) {
            log.error("生成图片失败", e);
        }
        return inputStream;
    }

    /**
     * @description: 区域、投顾描述
     * @param paramBean
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2024/4/2 14:01
     * @since JDK 1.8
     */
    private static String getDistrictDesc(MarketReportMessageParamDTO paramBean) {
        String districtDesc;

        //区域名称，Eg: 成都区
        String districtName = paramBean.getDistrictName();
        //团队|部门名称，Eg: 第三团队
        String orgName = paramBean.getOrgName();
        // 所属投顾姓名
        String consName = paramBean.getConsName();

        if (IC.equals(paramBean.getCenterOrgCode())) {
            // 所属投顾姓名 打码
            consName = hideName(consName);
            // 在途投顾
            if (StaticVar.YES.equals(paramBean.getIsOnTheWay())) {
                consName = MYSTERY_MEN;
            }

            // IC群 喜报规则：取值区域+所属投顾姓名 打码，分公司不取值
            // ！！！这里需要注意：当区域层级为3时，举个例子： 好买-财富中心-北京区域   当区域层级大于3时，举个例子： 好买-财富中心-北京区域-北京三区
            // 当层级大于3，区域所在的字段为districtName。当区域层级为3时，区域所在的字段为orgName（即它自己的名称就是区域的名称）
            if (paramBean.getOrgLevel() > 3) {
                districtDesc = String.join(" ", districtName, consName, SUCCESSFULLY_CONFIGURED);
            } else {
                districtDesc = String.join(" ", orgName, consName, SUCCESSFULLY_CONFIGURED);
            }
        } else {
            // HBC群 喜报规则
            // {$好买产品线}=PE、VC、海外时，所属投顾姓名 打码
            if (HbProductLineEnum.PE_VC.getCode().equals(paramBean.getHbType())
                    || HbProductLineEnum.OVERSEAS.getCode().equals(paramBean.getHbType())) {
                consName = hideName(paramBean.getConsName());
            }
            // 在途投顾
            if (StaticVar.YES.equals(paramBean.getIsOnTheWay())) {
                consName = MYSTERY_MEN;
            }

            // HBC群 喜报规则：区域 + 分部 + 所属投顾姓名，如果没有 分部， 区域+投顾名全名
            if (paramBean.getOrgLevel() > 3) {
                districtDesc = String.join(" ", districtName, orgName, consName, SUCCESSFULLY_CONFIGURED);
            } else {
                districtDesc = String.join(" ", orgName, consName, SUCCESSFULLY_CONFIGURED);
            }

        }

        return districtDesc;
    }


    public static void main(String[] args) {
        String consName = "张磊";
        consName = consName.substring(0, 1) +  "*" + consName.substring(2);
        System.out.println(consName);
        InputStream inputStream = null;
        FileOutputStream fos = null;
        String districtDesc = "上海一区 *磊 成功配置";
       // String prodName = "海通资管5号";
       String prodName = "海通资管沪盈5号";
        String amtValue = "120";
        String unitValue = "万";

        try {

            File imageFile = FileUtil.getFile("E:\\temp\\non.png");
            BufferedImage originalImage = ImageIO.read(imageFile);

            // IC、HBC本地测试生成图片
            //icGraphics(originalImage, districtDesc, prodName, amtValue, unitValue);
           // hbcGraphics(originalImage, districtDesc, prodName, amtValue, unitValue);
            nonSecuritiesGraphics(originalImage, "LJ01", "P1102", amtValue, unitValue);


            // Save the modified image with added districtDesc

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(originalImage, "png", outputStream);
            outputStream.flush();

            //转换为 InputStream
            inputStream= new ByteArrayInputStream(outputStream.toByteArray());

            ImageIO.write(originalImage, "png", new File("E:\\temp\\IC21.png"));

        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    /**
     * @description: 非证券类私募海报
     * @param originalImage
     * @param consName
     * @param prodCode
     * @param amtValue
     * @param unitValue
     * @return void
     * @author: jin.wang03
     * @date: 2024/10/10 17:54
     * @since JDK 1.8
     */
    private static void nonSecuritiesGraphics(BufferedImage originalImage, String consName, String prodCode,
                                              String amtValue, String unitValue) {

        int imageWidth = originalImage.getWidth();
        int imageHeight = originalImage.getHeight();
        // toCreate a graphics object  draw on the image
        Graphics2D graphics = originalImage.createGraphics();

        // 团队区域
        // Set font and color for the consName
        Font font = new Font(FONT_NAME, Font.PLAIN, 140);
        Color textColor = new Color(253, 230, 207);
        graphics.setFont(font);
        graphics.setColor(textColor);
        // Get FontMetrics to calculate consName dimensions
        FontMetrics fontMetrics = graphics.getFontMetrics();
        int textWidth = fontMetrics.stringWidth(consName);
        int textHeight = fontMetrics.getHeight();
        // Calculate the position to place the consName (you can adjust these values)
        int x = (imageWidth - textWidth) / 2;
        int y = (imageHeight - textHeight) / 2 + fontMetrics.getAscent() + 700;

        // Draw the consName on the image
        graphics.drawString(consName, x, y);

        // 成功配置
        // Draw the consName on the image
        // Get FontMetrics to calculate consName dimensions
        FontMetrics fontMetrics1 = graphics.getFontMetrics();
        int textWidth1 = fontMetrics1.stringWidth(SUCCESSFULLY_CONFIGURED);
        int textHeight1 = fontMetrics1.getHeight();
        // Calculate the position to place the consName (you can adjust these values)
        int x1 = (imageWidth - textWidth1) / 2;
        int y1 = (imageHeight - textHeight1) / 2 + fontMetrics.getAscent() + 900;
        graphics.drawString(SUCCESSFULLY_CONFIGURED, x1, y1);

        // 产品名称区域
        // 设置标签的背景色
        Color startBackgroundColor = new Color(253, 195, 133);
        Color endBackgroundColor = new Color(246, 233, 219);
        Font font2 = new Font(FONT_NAME, Font.PLAIN, 130);
        Color textColor2 = new Color(38, 41, 56);
        graphics.setFont(font2);

        FontMetrics fontMetrics2 = graphics.getFontMetrics();
        int textWidth2 = fontMetrics2.stringWidth(prodCode);
        int textHeight2 = fontMetrics2.getHeight();
        int x2 = (imageWidth - textWidth2) / 2;
        int y2 = (imageHeight - textHeight2) / 2 + fontMetrics2.getAscent() + 1200;
//                graphics.setColor(startBackgroundColor);
//                graphics.setBackground(startBackgroundColor);
        int fx = x2 - 125;
        int fy = y2 - textHeight2 - 15;
        int fw = textWidth2 + 250;
        int fh = textHeight2 + 100;
        int fx2 = (int) (fx + (fw * 0.7));
        int fy2 = fy+ textHeight2 + 120;
        // 创建渐变背景
        GradientPaint gradient = new GradientPaint(fx, fy, startBackgroundColor, fx2, fy2, endBackgroundColor);
        // 绘制带渐变色背景的标签
        graphics.setPaint(gradient);
        // 绘制带底色的矩形
//                graphics.fillRect(x2 - 25, y2 - textHeight2 - 13, textWidth2 + 50, textHeight2 + 40);
        graphics.fillRoundRect(fx, fy, fw, fh,70,70);
        graphics.setColor(textColor2);
        graphics.drawString(prodCode, x2, y2);

        // 金额
        Font font3 = new Font(FONT_NAME, Font.PLAIN, 580);
        Color textColor3 = new Color(253, 230, 207);
        graphics.setFont(font3);
        graphics.setColor(textColor3);
        FontMetrics fontMetrics3 = graphics.getFontMetrics();
        int textWidth3 = fontMetrics3.stringWidth(amtValue);
        int textHeight3 = fontMetrics3.getHeight();

        Font font4 = new Font(FONT_NAME, Font.PLAIN, 300);
        graphics.setFont(font4);
        graphics.setColor(textColor3);
        FontMetrics fontMetrics4 = graphics.getFontMetrics();
        int textWidth4 = fontMetrics4.stringWidth(unitValue);
        int textHeight4 = fontMetrics4.getHeight();

        int textWidth0 = textWidth3 + textWidth4;

        int x3 = (imageWidth - textWidth0) / 2;
        int y3 = (imageHeight - textHeight3) / 2 + fontMetrics3.getAscent() + 1660;
        int x4 = x3 + textWidth3;
        int y4 = y3 - 10;

//                int y4 = (imageHeight - textHeight4) / 2 + fontMetrics4.getAscent() + 370;

        graphics.setFont(font3);
        graphics.setColor(textColor3);
        graphics.drawString(amtValue, x3, y3);
        graphics.setFont(font4);
        graphics.drawString(unitValue, x4, y4);

        // Dispose of the graphics object
        graphics.dispose();
    }


    private static void icGraphics(BufferedImage originalImage, String districtDesc, String prodName, String amtValue, String unitValue) {
        int imageWidth = originalImage.getWidth();
        int imageHeight = originalImage.getHeight();
        // Create a graphics object to draw on the image
        Graphics2D graphics = originalImage.createGraphics();

        // 团队区域
        // Set font and color for the districtDesc
        Font font = new Font(FONT_NAME, Font.PLAIN, 140);
        Color textColor = new Color(253, 230, 207);
        graphics.setFont(font);
        graphics.setColor(textColor);
        // Get FontMetrics to calculate districtDesc dimensions
        FontMetrics fontMetrics = graphics.getFontMetrics();
        int textWidth = fontMetrics.stringWidth(districtDesc);
        int textHeight = fontMetrics.getHeight();
        // Calculate the position to place the districtDesc (you can adjust these values)
        int x = (imageWidth - textWidth) / 2;
        int y = (imageHeight - textHeight) / 2 + fontMetrics.getAscent() - 10;

        // Draw the districtDesc on the image
        graphics.drawString(districtDesc, x, y);

        // 产品名称区域
        // 设置标签的背景色
        Color startBackgroundColor = new Color(253, 195, 133);
        Color endBackgroundColor = new Color(246, 233, 219);
        Font font2 = new Font(FONT_NAME, Font.PLAIN, 130);
        Color textColor2 = new Color(38, 41, 56);
        graphics.setFont(font2);

        FontMetrics fontMetrics2 = graphics.getFontMetrics();
        int textWidth2 = fontMetrics2.stringWidth(prodName);
        int textHeight2 = fontMetrics2.getHeight();
        int x2 = (imageWidth - textWidth2) / 2;
        int y2 = (imageHeight - textHeight2) / 2 + fontMetrics2.getAscent() + 345;
//                graphics.setColor(startBackgroundColor);
//                graphics.setBackground(startBackgroundColor);
        int fx = x2 - 125;
        int fy = y2 - textHeight2 - 15;
        int fw = textWidth2 + 250;
        int fh = textHeight2 + 100;
        int fx2 = (int) (fx + (fw * 0.7));
        int fy2 = fy+ textHeight2 + 120;
        // 创建渐变背景
        GradientPaint gradient = new GradientPaint(fx, fy, startBackgroundColor, fx2, fy2, endBackgroundColor);
        // 绘制带渐变色背景的标签
        graphics.setPaint(gradient);
        // 绘制带底色的矩形
//                graphics.fillRect(x2 - 25, y2 - textHeight2 - 13, textWidth2 + 50, textHeight2 + 40);
        graphics.fillRoundRect(fx, fy, fw, fh,70,70);
        graphics.setColor(textColor2);
        graphics.drawString(prodName, x2, y2);

        // 金额
        Font font3 = new Font(FONT_NAME, Font.PLAIN, 580);
        Color textColor3 = new Color(253, 230, 207);
        graphics.setFont(font3);
        graphics.setColor(textColor3);
        FontMetrics fontMetrics3 = graphics.getFontMetrics();
        int textWidth3 = fontMetrics3.stringWidth(amtValue);
        int textHeight3 = fontMetrics3.getHeight();

        Font font4 = new Font(FONT_NAME, Font.PLAIN, 300);
        graphics.setFont(font4);
        graphics.setColor(textColor3);
        FontMetrics fontMetrics4 = graphics.getFontMetrics();
        int textWidth4 = fontMetrics4.stringWidth(unitValue);
        int textHeight4 = fontMetrics4.getHeight();

        int textWidth0 = textWidth3 + textWidth4;

        int x3 = (imageWidth - textWidth0) / 2;
        int y3 = (imageHeight - textHeight3) / 2 + fontMetrics3.getAscent() + 860;
        int x4 = x3 + textWidth3;
        int y4 = y3 - 10;

//                int y4 = (imageHeight - textHeight4) / 2 + fontMetrics4.getAscent() + 370;

        graphics.setFont(font3);
        graphics.setColor(textColor3);
        graphics.drawString(amtValue, x3, y3);
        graphics.setFont(font4);
        graphics.drawString(unitValue, x4, y4);

        // Dispose of the graphics object
        graphics.dispose();
    }

    private static void hbcGraphics(BufferedImage originalImage, String districtDesc, String prodName, String amtValue, String unitValue) {
        int imageWidth = originalImage.getWidth();
        int imageHeight = originalImage.getHeight();
        // Create a graphics object to draw on the image
        Graphics2D graphics = originalImage.createGraphics();

        // 团队区域
        // Set font and color for the districtDesc
        Font font = new Font(FONT_NAME, Font.PLAIN, 140);
        Color textColor = new Color(253, 230, 207);
        graphics.setFont(font);
        graphics.setColor(textColor);
        // Get FontMetrics to calculate districtDesc dimensions
        FontMetrics fontMetrics = graphics.getFontMetrics();
        int textWidth = fontMetrics.stringWidth(districtDesc);
        int textHeight = fontMetrics.getHeight();
        // Calculate the position to place the districtDesc (you can adjust these values)
        int x = (imageWidth - textWidth) / 2;
        int y = (imageHeight - textHeight) / 2 + fontMetrics.getAscent() + 810;

        // Draw the districtDesc on the image
        graphics.drawString(districtDesc, x, y);

        // 产品名称区域
        // 设置标签的背景色
        Color startBackgroundColor = new Color(253, 195, 133);
        Color endBackgroundColor = new Color(246, 233, 219);
        Font font2 = new Font(FONT_NAME, Font.PLAIN, 130);
        Color textColor2 = new Color(38, 41, 56);
        graphics.setFont(font2);

        FontMetrics fontMetrics2 = graphics.getFontMetrics();
        int textWidth2 = fontMetrics2.stringWidth(prodName);
        int textHeight2 = fontMetrics2.getHeight();
        int x2 = (imageWidth - textWidth2) / 2;
        int y2 = (imageHeight - textHeight2) / 2 + fontMetrics2.getAscent() +1145;
//                graphics.setColor(startBackgroundColor);
//                graphics.setBackground(startBackgroundColor);
        int fx = x2 - 125;
        int fy = y2 - textHeight2 - 15;
        int fw = textWidth2 + 250;
        int fh = textHeight2 + 100;
        int fx2 = (int) (fx + (fw * 0.7));
        int fy2 = fy+ textHeight2 + 120;
        // 创建渐变背景
        GradientPaint gradient = new GradientPaint(fx, fy, startBackgroundColor, fx2, fy2, endBackgroundColor);
        // 绘制带渐变色背景的标签
        graphics.setPaint(gradient);
        // 绘制带底色的矩形
//                graphics.fillRect(x2 - 25, y2 - textHeight2 - 13, textWidth2 + 50, textHeight2 + 40);
        graphics.fillRoundRect(fx, fy, fw, fh,70,70);
        graphics.setColor(textColor2);
        graphics.drawString(prodName, x2, y2);

        // 金额
        Font font3 = new Font(FONT_NAME, Font.PLAIN, 580);
        Color textColor3 = new Color(253, 230, 207);
        graphics.setFont(font3);
        graphics.setColor(textColor3);
        FontMetrics fontMetrics3 = graphics.getFontMetrics();
        int textWidth3 = fontMetrics3.stringWidth(amtValue);
        int textHeight3 = fontMetrics3.getHeight();

        Font font4 = new Font(FONT_NAME, Font.PLAIN, 300);
        graphics.setFont(font4);
        graphics.setColor(textColor3);
        FontMetrics fontMetrics4 = graphics.getFontMetrics();
        int textWidth4 = fontMetrics4.stringWidth(unitValue);
        int textHeight4 = fontMetrics4.getHeight();

        int textWidth0 = textWidth3 + textWidth4;

        int x3 = (imageWidth - textWidth0) / 2;
        int y3 = (imageHeight - textHeight3) / 2 + fontMetrics3.getAscent() + 1660;
        int x4 = x3 + textWidth3;
        int y4 = y3 - 10;

//                int y4 = (imageHeight - textHeight4) / 2 + fontMetrics4.getAscent() + 370;

        graphics.setFont(font3);
        graphics.setColor(textColor3);
        graphics.drawString(amtValue, x3, y3);
        graphics.setFont(font4);
        graphics.drawString(unitValue, x4, y4);

        // Dispose of the graphics object
        graphics.dispose();
    }
}
