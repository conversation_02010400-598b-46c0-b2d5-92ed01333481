package com.howbuy.crm.wechat.service.batch;

import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.crm.wechat.dao.vo.PageVo;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.commom.utils.ExecutorsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletionService;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorCompletionService;
import java.util.concurrent.ExecutorService;


@Service
public class BatchOperateExecutor<Vo extends PageVo, Po> {


    private static final Logger logger = LoggerFactory.getLogger(BatchOperateExecutor.class);

    public int businessExecute(List<Po> persistList, BatchPersistService<Po> callbackService) {
        return batchExecute(Constants.BATCH_COUNT, persistList, callbackService);
    }

    public int batchExecute(int perSize, List<Po> persistList, BatchPersistService<Po> callbackService) {
        int affectCount = 0;
        if (CollectionUtils.isEmpty(persistList)) {
            return affectCount;
        }
        // 分页执行
        List<List<Po>> lists = Lists.partition(persistList, perSize);
        return lists.stream().mapToInt(callbackService::doBusiness).sum();
    }

    public int queryBeforeExecute(Vo queryVo, QueryPageService<Vo, Po> callbackService) {
        queryVo.setPage(1);
        queryVo.setRows(Constants.MAX_QUERY_DB_COUNT); // 每次查询条数，默认从第一页开始查
        long t = System.currentTimeMillis();
        Page<Po> pageList = callbackService.queryPage(queryVo);
        String serviceName = callbackService.getClass().getSimpleName();
        if (CollectionUtils.isEmpty(pageList)) {
            return 0;
        }
        int pages = pageList.getPages();// 总页数
        logger.info("{}查询数据总页数：{}，总条数：{}", serviceName, pages, pageList.getTotal());
        int totalCount = businessExecute(pageList, callbackService);
        for (int pageNo = 2; pageNo <= pages; pageNo++) {
            logger.info("{}查询数据总页数：{}，当前处理页数：{}", serviceName, pages, pageNo);
            queryVo.setPage(pageNo);
            pageList = callbackService.queryPage(queryVo);
            if (CollectionUtils.isEmpty(pageList)) {
                break;
            }
            totalCount += businessExecute(pageList, callbackService);
        }
        logger.info("{}执行任务完成，耗时：{}", serviceName, System.currentTimeMillis() - t);
        return totalCount;
    }

    public int poolExecute(final Vo queryVo, final QueryPageService<Vo, Po> callbackService) {
        queryVo.setPage(1);
        queryVo.setRows(Constants.MAX_QUERY_DB_COUNT); // 每次查询条数，默认从第一页开始查
        long t = System.currentTimeMillis();
        Page<Po> pageList = callbackService.queryPage(queryVo);
        if (CollectionUtils.isEmpty(pageList)) {
            return 0;
        }
        String serviceName = callbackService.getClass().getSimpleName();
        int pages = pageList.getPages();// 总页数
        logger.info("{}查询数据总页数：{}，总条数：{}", serviceName, pages, pageList.getTotal());
        int totalCount = businessExecute(pageList, callbackService);
        if (pages > 1) {
            int remainingCnt = pages - 1;
            //计算 poolSize
            int poolSize = Math.min(remainingCnt, Constants.THREAD_MAX_POOL_SIZE);
            ExecutorService threadPoolExecutor = ExecutorsUtil.fixedPool(poolSize, serviceName);
            CompletionService<Integer> completionService = new ExecutorCompletionService<>(threadPoolExecutor);
            try {
                for (int pageNo = 2; pageNo <= pages; pageNo++) {
                    queryVo.setPage(pageNo);
                    Page<Po> singlePageList = callbackService.queryPage(queryVo);
                    if (CollectionUtils.isEmpty(singlePageList)) {
                        break;
                    }
                    final CopyOnWriteArrayList<Po> copyOnWriteList = Lists.newCopyOnWriteArrayList(singlePageList);
                    completionService.submit(() -> businessExecute(copyOnWriteList, callbackService));
                }
                for (int i = 0; i < remainingCnt; i++) {
                    totalCount += completionService.take().get();
                }
            } catch (Exception e) {
                logger.error("执行任务失败：", e);
                throw new BusinessException(Constants.SYSTEM_ERROR,"线程池执行任务失败：", e);
            } finally {
                threadPoolExecutor.shutdown();
            }
        }
        logger.info("{}执行任务完成，耗时：{}", serviceName, System.currentTimeMillis() - t);
        return totalCount;
    }

    public void poolExecute(final Vo queryVo, final QueryPageService<Vo, Po> callbackService, int poolSize) {
        queryVo.setPage(1);
        queryVo.setRows(Constants.MAX_QUERY_DB_COUNT); // 每次查询条数，默认从第一页开始查
        Page<Po> pageList = callbackService.queryPage(queryVo);
        if (CollectionUtils.isEmpty(pageList)) {
            return;
        }
        String serviceName = callbackService.getClass().getSimpleName();
        int pages = pageList.getPages();
        int pageNo = 1;// 总页数
        long totalCount = pageList.getTotal();
        long t1 = System.currentTimeMillis();
        logger.info("{}查询数据总页数：{}，总条数：{}", serviceName, pages, totalCount);
        poolSize = Math.min(Math.min(pages, poolSize), Constants.THREAD_MAX_POOL_SIZE);
        ExecutorService threadPoolExecutor = ExecutorsUtil.fixedPool(poolSize, serviceName);
        CompletionService<Integer> completionService = new ExecutorCompletionService<>(threadPoolExecutor);
        try {
            while (pageNo <= pages) {
                CopyOnWriteArrayList<Po> copyOnWriteList = Lists.newCopyOnWriteArrayList(pageList);
                completionService.submit(() -> businessExecute(copyOnWriteList, callbackService));
                queryVo.setPage(++pageNo);
                pageList = callbackService.queryPage(queryVo);
                if (CollectionUtils.isEmpty(pageList)) {
                    break;
                }
            }
            int total = 0;
            for (int i = 0; i < pages; i++) {
                total += completionService.take().get();
            }
            logger.info("{}执行任务完成，总条数：{}，耗时：{}", serviceName, total, (System.currentTimeMillis() - t1));
        } catch (Exception e) {
            logger.error("执行任务失败：", e);
            throw new BusinessException(Constants.SYSTEM_ERROR,"线程池执行任务失败：", e);
        } finally {
            threadPoolExecutor.shutdown();
        }
    }

    public List<Po> pageQuery(Vo queryVo, PageQueryService<Vo, Po> pageQueryService) {
        queryVo.setPage(1);
        queryVo.setRows(Constants.MAX_QUERY_DB_COUNT); // 每次查询条数，默认从第一页开始查

        Page<Po> pageList = pageQueryService.pageQuery(queryVo);
        int pages = pageList.getPages();// 总页数
        logger.info("{}分页查询总页数：{}，总条数：{}", pageQueryService.getClass().getSimpleName(), pages, pageList.getTotal());
        if (!CollectionUtils.isEmpty(pageList)) {
            List<Po> allPageList = Lists.newArrayList(pageList);

            for (int pageNo = 2; pageNo <= pages; pageNo++) {
                queryVo.setPage(pageNo);
                pageList = pageQueryService.pageQuery(queryVo);
                if (CollectionUtils.isEmpty(pageList)) {
                    break;
                }
                allPageList.addAll(pageList);
            }
            return allPageList;
        }
        return Collections.emptyList();
    }
}
