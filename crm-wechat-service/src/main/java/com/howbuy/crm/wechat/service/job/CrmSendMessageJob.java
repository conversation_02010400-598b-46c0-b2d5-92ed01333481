/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.job;

import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.howbuy.crm.wechat.client.enums.MessageSendStatusEnum;
import com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO;
import com.howbuy.crm.wechat.dao.vo.message.MessageSendInfoVO;
import com.howbuy.crm.wechat.service.batch.NewBatchOperateExecutor;
import com.howbuy.crm.wechat.service.batch.QueryPageService;
import com.howbuy.crm.wechat.service.service.message.MessageSendInfoService;
import com.howbuy.crm.wechat.service.service.message.send.MessageSendFactory;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: (发送消息  发送消息  消息接收处理器)
 * <AUTHOR>
 * @date 2023/10/7 9:59
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CrmSendMessageJob extends AbstractBatchMessageJob {

    /**
     * crm发送消息 接收消息队列
     */
    @Value("${crm_send_message_channel}")
    private String crmSendMessageChannel;

    /**
     * 发送最大次数约束
     */
    private static final Integer SEND_TIMES = 3;

    @Resource
    private NewBatchOperateExecutor newBatchOperateExecutor;

    @Autowired
    private MessageSendInfoService messageSendInfoService;

    @Autowired
    private MessageSendFactory messageSendfactory;

    public CrmSendMessageJob() {
    }

    @Override
    protected String getQuartMessageChannel() {
        return crmSendMessageChannel;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
//        查询消息表（message_accept_info）中状态为0-未处理的记录list
        MessageSendInfoVO sendInfoVO=new MessageSendInfoVO();
        // 未推送的 推送失败的
        sendInfoVO.setSendStatusList(Lists.newArrayList(MessageSendStatusEnum.UNPUSH.getCode(),MessageSendStatusEnum.PUSH_FAIL.getCode()));
        //发送次数小于3次
        sendInfoVO.setSendTimes(SEND_TIMES);
        newBatchOperateExecutor.poolExecute(sendInfoVO, new QueryPageService<MessageSendInfoVO, MessageSendInfoPO>() {
            @Override
            public int doBusiness(List<MessageSendInfoPO> dataList) {
//                messageBuildfactory.buidMessageList(dataList);
                //遍历list，根据调用接口 -->  发送消息
                dataList.forEach(messageInfo -> {
                    //构建消息发送表中的消息
                    messageSendfactory.sendMessage(messageInfo);
                });
                return dataList.size();
            }

            @Override
            public Page<MessageSendInfoPO> queryPage(MessageSendInfoVO queryVo) {
                return messageSendInfoService.selectPageByVo(queryVo);
            }
        });

    }

}