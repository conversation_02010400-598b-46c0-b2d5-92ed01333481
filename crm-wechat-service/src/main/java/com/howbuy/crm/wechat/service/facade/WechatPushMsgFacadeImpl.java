package com.howbuy.crm.wechat.service.facade;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.wechatpushmsg.SendGroupMessageRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatpushmsg.WechatPushMsgVO;
import com.howbuy.crm.wechat.client.facade.wechatpushmsg.WechatPushMsgFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import com.howbuy.crm.wechat.service.business.CompanySendBusiness;

/**
 * <AUTHOR>
 * @description: 微信推送消息服务实现
 * @date 2024/9/6
 * @since JDK 1.8
 */
@DubboService
@Slf4j
public class WechatPushMsgFacadeImpl implements WechatPushMsgFacade {

    @Autowired
    private CompanySendBusiness companySendBusiness;

    @Override
    public Response<WechatPushMsgVO> sendGroupMessage(SendGroupMessageRequest request) {
        WechatPushMsgVO wechatPushMsgVO = new WechatPushMsgVO();
        String taskId = companySendBusiness.sendGroupMessage(request.getMessageType(), request.getTitle(), request.getWechatConsCode(),
                request.getUrl(), request.getCustNos(), request.getMessageContent(), request.getAsseetId());
        wechatPushMsgVO.setTaskId(taskId);
        return Response.ok(wechatPushMsgVO);
    }
} 