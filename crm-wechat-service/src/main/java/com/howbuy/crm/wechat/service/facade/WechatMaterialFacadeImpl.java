/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.facade;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.wechatmaterial.WechatUploadMediaRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatmaterial.WechatUploadMediaVO;
import com.howbuy.crm.wechat.client.facade.wechatmaterial.WechatMaterialFacade;
import com.howbuy.crm.wechat.service.service.wechatmaterial.WechatMaterialService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 微信素材管理接口实现
 * @date 2024/9/19 10:02
 * @since JDK 1.8
 */
@DubboService
public class WechatMaterialFacadeImpl implements WechatMaterialFacade {

    @Resource
    private WechatMaterialService wechatMaterialService;

    @Override
    public Response<WechatUploadMediaVO> uploadMediaFile(WechatUploadMediaRequest request) {
        return wechatMaterialService.uploadMediaFile(request);
    }
}
