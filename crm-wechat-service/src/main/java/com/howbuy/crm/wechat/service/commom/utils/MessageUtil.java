package com.howbuy.crm.wechat.service.commom.utils;


import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.crm.wechat.service.domain.callback.ChatEventDTO;
import com.howbuy.crm.wechat.service.domain.callback.MessageDTO;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.core.util.QuickWriter;
import com.thoughtworks.xstream.io.HierarchicalStreamWriter;
import com.thoughtworks.xstream.io.xml.PrettyPrintWriter;
import com.thoughtworks.xstream.io.xml.XppDriver;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.io.Writer;
import java.util.*;

/**
 * @classname: MessageUtil
 * @author: yu.zhang
 * @description: 消息工具类
 * @creatdate: 2021-02-08 16:22
 * @since: JDK1.8
 */
public class MessageUtil {

    /**
     * 解析微信发来的请求（XML）,获取请求参数
     * parseXml
     * @param request
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @Author: yu.zhang on 2021/2/10 14:33
     */
    public static Map<String, String> parseXml(HttpServletRequest request) throws Exception {
        // 将解析结果存储在HashMap中
        Map<String, String> map = Maps.newHashMap();

        // 从request中取得输入流
        InputStream inputStream = request.getInputStream();
        // 读取输入流
        SAXReader reader = new SAXReader();
        Document document = reader.read(inputStream);
        // 得到xml根元素
        Element root = document.getRootElement();
        // 得到根元素的所有子节点
        List<Element> elementList = root.elements();

        // 遍历所有子节点
        for (Element e : elementList) {
            map.put(e.getName(), e.getText());
        }
        // 释放资源
        inputStream.close();

        return map;
    }

    /**
     * 解析微信发来的请求（xmlStr）,获取请求参数
     * parseXml
     * @param xmlStr
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @Author: yu.zhang on 2021/2/10 14:33
     */
    public static Map<String, String> parseXml(String xmlStr) throws Exception {
        // 将解析结果存储在HashMap中
        Map<String, String> map = Maps.newHashMap();

        //1.将字符串转为Document
        Document document = DocumentHelper.parseText(xmlStr);

        //2.获取根元素的所有子节点
        // 得到xml根元素
        Element root = document.getRootElement();
        // 得到根元素的所有子节点
        List<Element> elementList = root.elements();

        //3.遍历所有子节点
        for (Element e : elementList) {
            map.put(e.getName(), e.getText());
        }

        return map;
    }
    /**
     * @description:(解析微信发来的请求（xmlStr）,获取请求参数_群事件有list  单独取出来)
     * @param xmlStr
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * @author: shuai.zhang
     * @date: 2024/3/20 13:47
     * @since JDK 1.8
     */
    public static Map<String, Object> parseXmlForChatEvent(String xmlStr) throws Exception {
        // 将解析结果存储在HashMap中
        Map<String, Object> map = new HashMap<String, Object>();

        //1.将字符串转为Document
        Document document = DocumentHelper.parseText(xmlStr);

        //2.获取根元素的所有子节点
        // 得到xml根元素
        Element root = document.getRootElement();
        // 得到根元素的所有子节点
        List<Element> elementList = root.elements();

        //3.遍历所有子节点
        for (Element e : elementList){
            if(Objects.equals(e.getName(),"MemChangeList")){
                ArrayList<String> objects = Lists.newArrayList();
                Iterator iterator = e.elementIterator();
                while(iterator.hasNext()) {
                    Element element=(Element) iterator.next();
                    String item = element.getText();
                    objects.add(item);
                }
                map.put(e.getName(), objects);
            }else{
                map.put(e.getName(), e.getText());
            }
        }
        return map;
    }

//    public static void main(String[] args) throws Exception {
//        String a ="<xml><ToUserName><![CDATA[wx261bb2bb281b1cce]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>1710865937</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[change_external_chat]]></Event><ChatId><![CDATA[wrl2gbEQAAfX8kufAsa3FaBJ3AKC84XQ]]></ChatId><ChangeType><![CDATA[update]]></ChangeType><UpdateDetail><![CDATA[del_member]]></UpdateDetail><QuitScene>0</QuitScene><MemChangeCnt>1</MemChangeCnt><MemChangeList><Item><![CDATA[wml2gbEQAArEBEu37N_-96fUFTfuCMMQ]]></Item></MemChangeList><LastMemVer><![CDATA[014a817b78894d2b35637e184113c615]]></LastMemVer><CurMemVer><![CDATA[65876d2da2e0a7dcb00fc4ed1eb6c9e3]]></CurMemVer></xml>";
//        Map<String, Object> stringStringMap = parseXmlForChatEvent(a);
//        System.out.println(JSON.toJSONString(stringStringMap));
//        ObjectMapper mapper = new ObjectMapper();
//        //忽略驼峰大小写问题
//        mapper.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
//        //遇到未知属性，直接抛弃
//        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//        ChatEventDTO chatEventDTO = mapper.convertValue(stringStringMap, ChatEventDTO.class);
//        System.out.println(JSON.toJSONString(chatEventDTO));
//    }
    /**
     * 文本消息对象转换成xml
     * textMessageToXml
     * @param textMessage
     * @return java.lang.String
     * @Author: yu.zhang on 2021/2/10 14:32
     */
    public static String textMessageToXml(MessageDTO textMessage) {
        xstream.alias("xml", textMessage.getClass());
        return xstream.toXML(textMessage);
    }

    /**
     * 扩展xstream，使其支持CDATA块  
     * @param null
     * @return null
     * @Author: yu.zhang on 2021/2/10 14:32
     */
    private static XStream xstream = new XStream(new XppDriver() {
        @Override
        public HierarchicalStreamWriter createWriter(Writer out) {
            return new PrettyPrintWriter(out) {
                // 对所有xml节点的转换都增加CDATA标记
                boolean cdata = true;

                @Override
                @SuppressWarnings("unchecked")
                public void startNode(String name, Class clazz) {
                    super.startNode(name, clazz);
                }

                @Override
                protected void writeText(QuickWriter writer, String text) {
                    if (cdata) {
                        writer.write("<![CDATA[");
                        writer.write(text);
                        writer.write("]]>");
                    } else {
                        writer.write(text);
                    }
                }
            };
        }
    });
}