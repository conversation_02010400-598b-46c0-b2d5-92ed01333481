package com.howbuy.crm.wechat.service.repository;

import com.howbuy.crm.wechat.dao.mapper.CmWechatRefreshConscustMapper;
import com.howbuy.crm.wechat.dao.po.CmWechatRefreshConscustPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @author: hongdong.xie
 * @date: 2025-02-12
 * @description: 投顾客户微信刷新状态表Repository
 */
@Repository
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class CmWechatRefreshConscustRepository {

    @Autowired
    private CmWechatRefreshConscustMapper cmWechatRefreshConscustMapper;

    /**
     * 更新刷新状态表记录
     * @param po 待更新的记录
     * @return 更新结果
     */
    public int updateByPrimaryKeySelective(CmWechatRefreshConscustPO po) {
        return cmWechatRefreshConscustMapper.updateByPrimaryKeySelective(po);
    }

    /**
     * 获取当天需要处理的记录
     * @param companyNo  企微-企业主体
     * @param nowDate 处理时间YYYYMMDD
     * @param dealStatus 处理状态:1-已处理 0-未处理 2-处理完废弃
     * @param dealType 处理类型: 1-投顾客户列表刷新 2-客户姓名备注刷新
     * @return 记录列表
     */
    public List<CmWechatRefreshConscustPO> getRefreshTask(String companyNo,
                                                          String nowDate,
                                                          String dealStatus,
                                                          String dealType) {
        return cmWechatRefreshConscustMapper.getRefreshTask(companyNo,
                nowDate,
                dealStatus,
                dealType);
    }

    /**
     * 批量插入当天需要处理的记录
     * @param companyNo  企微-企业主体
     * @param nowDate 处理时间YYYYMMDD
     * @param dealType 处理类型: 1-投顾客户列表刷新 2-客户姓名备注刷新
     * @param refreshConscodeList 需要刷新的投顾编号列表
     * @return 插入记录数
     */
    public int insertRefreshTask(String companyNo,
                                 String nowDate,
                                 String dealType,
                                 List<String> refreshConscodeList) {
        return cmWechatRefreshConscustMapper
                .insertRefreshTask(companyNo,
                        nowDate,
                        dealType,
                        refreshConscodeList);
    }

    /**
     * 更新处理任务状态，从1-已处理  更新为：2-处理完废弃
     * @param companyNo  企微-企业主体
     * @param nowDate 处理时间YYYYMMDD
     * @param dealType  处理类型: 1-投顾客户列表刷新 2-客户姓名备注刷新
     * @return 更新记录数
     */
    public int discardRefreshTask(String companyNo,
                                  String nowDate,
                                  String dealType) {
        return cmWechatRefreshConscustMapper
                .discardRefreshTask(companyNo,nowDate,dealType);
    }
} 