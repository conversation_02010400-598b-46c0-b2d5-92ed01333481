package com.howbuy.crm.wechat.service.service.message.build;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.howbuy.crm.wechat.client.enums.AcceptMessageStatusEnum;
import com.howbuy.crm.wechat.dao.po.message.MessageAcceptInfoPO;
import com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO;
import com.howbuy.crm.wechat.service.service.message.MessageAcceptInfoService;
import com.howbuy.crm.wechat.service.service.message.MessageSendInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/10/7 9:59
 * @since JDK 1.8
 */
@Service
@Slf4j
public abstract class AbstractMessageBuildService implements MessageBuildService {



    @Autowired
    private MessageAcceptInfoService messageAcceptInfoService;
    @Autowired
    private MessageSendInfoService messageSendInfoService;


    /**
     * @description: (子类覆写：不同的消息类型，构建不同的待发送消息)
     * @return
     * @return: java.util.List<com.howbuy.crm.wechat.client.enums.AcceptMessageTypeEnum>
     * @since JDK 1.8
     */
    public abstract List<MessageSendInfoPO> buildSpecificMessage(MessageAcceptInfoPO acceptInfo);


    /**
     * @description:构建待发送消息
     * @param acceptInfo 消息接收信息
     * @return void
     * @throws
     * @since JDK 1.8
     */
    @Override
    public void buildMessage(MessageAcceptInfoPO acceptInfo){
        //组装消息
        List<MessageSendInfoPO> sendInfoList=buildSpecificMessage(acceptInfo);
        if(CollectionUtils.isNotEmpty(sendInfoList)){
            //插入待发送消息表
            messageSendInfoService.batchInsert(sendInfoList);
            //更新消息接收表中的状态为1-已处理
            messageAcceptInfoService.batchUpdateStatus(AcceptMessageStatusEnum.PROCESSED, Lists.newArrayList(acceptInfo.getId()));
        }else {
            log.error("消息构建失败,消息ID:{}。",acceptInfo.getId());
        }

    }

    /**
     * @description:构建待发送消息-批量
     * @param acceptInfoList 消息接收信息列表
     * @return void
     * @throws
     * @since JDK 1.8
     */
    @Override
    public void buildMessageList(List<MessageAcceptInfoPO> acceptInfoList){
        List<MessageSendInfoPO> sendInfoList=Lists.newArrayList();
        acceptInfoList.forEach(acceptInfo->{
            //遍历 组装消息
            try {
                List<MessageSendInfoPO> buidlList=buildSpecificMessage(acceptInfo);
                if(CollectionUtils.isNotEmpty(buidlList)){
                    sendInfoList.addAll(buidlList);
                }else {
                    log.error("消息构建失败,消息ID:{}。",acceptInfo.getId());
                }
            }catch (Exception e){
                log.error("消息构建失败。",e);
            }

        } );
        if(CollectionUtils.isNotEmpty(sendInfoList)){
            //插入待发送消息表
            messageSendInfoService.batchInsert(sendInfoList);
            //批量更新消息接收表中的状态为1-已处理
            messageAcceptInfoService.batchUpdateStatus(AcceptMessageStatusEnum.PROCESSED,
                    sendInfoList.stream().map(MessageSendInfoPO::getAcceptId).distinct().collect(Collectors.toList()));
        }
    }


    /**
     * 工具类 从消息接收表中获取参数
     * @param attribute
     * @param acceptInfo
     * @return
     */
    public String getParamAttribute(String attribute,MessageAcceptInfoPO acceptInfo){
        if(acceptInfo==null||acceptInfo.getMessageParams()==null){
            return null;
        }
        String jsonString = acceptInfo.getMessageParams();
        JSONObject jsonObject = JSON.parseObject(jsonString);
        return jsonObject.getString(attribute);
    }

    /**
     * 工具类  转换为 参数对象
     * @param messageParams
     * @param clazz
     * @return
     */
    public <T>  T transferToParamBean(String messageParams,Class<T> clazz){
        return JSON.parseObject(messageParams,clazz);
    }


}
