/**
 * Copyright (c) 2025, <PERSON>gHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.domain.externaluser;

import com.howbuy.crm.wechat.service.outerservice.wechatapi.domain.ExternalWeChatBaseDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @description: (外部部门DTO类)
 * <AUTHOR>
 * @date 2025/7/18 14:46
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ExternalDeptDTOExternal extends ExternalWeChatBaseDTO {

    /**
     * id	创建的部门id
     */
    private Integer id;

    /**
     *     name	部门名称，代开发自建应用需要管理员授权才返回；
     *     此字段从2019年12月30日起，对新创建第三方应用不再返回，
     *     2020年6月30日起，对所有历史第三方应用不再返回name，返回的name字段使用id代替，
     *     后续第三方仅通讯录应用可获取，未返回名称的情况需要通过通讯录展示组件来展示部门名称
     */
    private String name;

    /**
        name_en	英文名称，此字段从2019年12月30日起，对新创建第三方应用不再返回，2020年6月30日起，对所有历史第三方应用不再返回该字段
     */
    private String nameEn;

    /**
        department_leader	部门负责人的UserID；第三方仅通讯录应用可获取
     */
    private List<String> departmentLeader;
     /**
        parentid	父部门id。根部门为1
      */
     private Integer parentId;
     /**
        order	在父部门中的次序值。order值大的排序靠前。值范围是[0, 2^32)
      */
     private Long order;
}