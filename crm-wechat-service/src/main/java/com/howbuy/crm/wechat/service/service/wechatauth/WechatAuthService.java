/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.wechatauth;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.wechatauth.GetUserIdRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatauth.GetUserIdVO;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.commom.utils.ApplicationUtilityDTO;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WeChatCommonOuterService;
import com.howbuy.crm.wechat.service.service.config.BaseConfigServce;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 微信身份认证服务
 * @date 2024/9/19 10:12
 * @since JDK 1.8
 */
@Slf4j
@Service
public class WechatAuthService {

    @Resource
    private WeChatCommonOuterService weChatCommonOuterService;

    @Autowired
    private BaseConfigServce baseConfigServce;

    /**
     * @param request
     * @return com.howbuy.crm.wechat.client.base.Response<com.howbuy.crm.wechat.client.domain.response.wechatauth.GetUserIdVO>
     * @description:获取访问用户身份-userId
     * <AUTHOR>
     * @date 2024/9/19 10:30
     * @since JDK 1.8
     */
    public Response<GetUserIdVO> getUserId(GetUserIdRequest request) {
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("code", request.getCode());
        String applicationCode = request.getWechatAppEnumKey();
        ApplicationUtilityDTO utilityDTO= baseConfigServce.getApplicationUtilityDTO(applicationCode);

        Assert.notNull(utilityDTO, "企微应用秘钥枚举转换为空");



        String resultJson = weChatCommonOuterService.requestWechatWithApplication(Constants.WECHAT_USER_ID_PATH,
                utilityDTO, paramsMap, Constants.METHOD_GET);
        String userId = null;
        // 结果非空
        if (!StringUtils.isEmpty(resultJson)) {
            JSONObject mapData = JSON.parseObject(resultJson);
            //结果判断
            String errCode = mapData.getString(Constants.WECHAT_ERR_CODE_KEY);
            String errMsg = mapData.getString(Constants.WECHAT_ERR_MSG_KEY);
            if (!Constants.SUCCESS_CODE.equals(errCode)) {
                log.info("getUserId >>> 获取访问用户身份错误，企微应用:{},errCode:{},errMsg:{}", request.getWechatAppEnumKey(), errCode, errMsg);
                return new Response<>(ResponseCodeEnum.UNKNOWN_ERROR.getCode(), errMsg, null);
            }
            userId = mapData.getString("userid");
        }

        Assert.hasText(userId, "获取访问用户身份-userid错误");

        GetUserIdVO getUserIdVO = new GetUserIdVO();
        getUserIdVO.setUserId(userId);
        return Response.ok(getUserIdVO);
    }

}
