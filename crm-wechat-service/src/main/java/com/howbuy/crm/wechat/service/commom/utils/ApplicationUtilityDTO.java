/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.utils;

import java.io.Serializable;

/**
 * @description: (企业微信-企业应用-组件DTO)
 * <AUTHOR>
 * @date 2025/7/11 13:44
 * @since JDK 1.8
 */
public class ApplicationUtilityDTO  extends  CorpUtilityDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 枚举的code
     */
    private String applicationCode;


    /**
     * 企业应用类型 ： customer-客户联系
     */
    private String applicationType;


    /**
     * 企业应用id
     * 应用的唯一标识，用于接口区分不同应用，或者回调通知中标识通知来源。
     */
    private String agentId;


    /**
     * 获取应用接口访问凭证
     * 用于获取access_token，相当于登录密码，开发者应保管好该字段值，避免在公开场合暴露（比如在网页参数）
     */
    private String accessSecret;

    /**
     * 缓存TOKEN后缀名称
     * [企业微信-内建应用-客户联系]构建的缓存后缀名称 ：
     *       companyNo_customer_envCode
     * [企业微信- 其他应用：Eg: 商路通、CRM、机构服务通知]缓存后缀名称 ：
     *       applicationCode_envCode [NOTICE: 实际最好使用 agentId . 以解决：同一应用在两套环境，实际缓存一样]
     */
    private String cacheSuffixName;


    public ApplicationUtilityDTO() {
    }


    public ApplicationUtilityDTO(String agentId, String accessSecret) {
        this.agentId = agentId;
        this.accessSecret = accessSecret;
    }

    public String getApplicationCode() {
        return applicationCode;
    }


    public void setApplicationCode(String applicationCode) {
        this.applicationCode = applicationCode;
    }

    public String getApplicationType() {
        return applicationType;
    }

    public void setApplicationType(String applicationType) {
        this.applicationType = applicationType;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getAccessSecret() {
        return accessSecret;
    }

    public void setAccessSecret(String accessSecret) {
        this.accessSecret = accessSecret;
    }

    public String getCacheSuffixName() {
        return cacheSuffixName;
    }

    public void setCacheSuffixName(String cacheSuffixName) {
        this.cacheSuffixName = cacheSuffixName;
    }
}