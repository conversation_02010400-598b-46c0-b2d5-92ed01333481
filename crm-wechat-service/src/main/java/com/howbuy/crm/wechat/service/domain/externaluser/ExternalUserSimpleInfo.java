package com.howbuy.crm.wechat.service.domain.externaluser;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @classname: ExternalUserInfoDTO
 * @author: yu.zhang
 * @description:  外部客户详情- 简单信息[不包括 profile 和 relation信息 ]
 * @creatdate: 2021-02-08 16:48
 * @since: JDK1.8
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ExternalUserSimpleInfo implements Serializable {

    /**
     * 外部联系人的userid
     */
    private String externalUserId;

    /**
     * 外部联系人的名称
     */
    private String externalUserName;

    /**
     * 外部联系人的职位，
     * 如果外部企业或用户选择隐藏职位，则不返回，
     * 仅当联系人类型是企业微信用户时有此字段
     */
    private String externalUserPosition;

    /**
     * 外部联系人头像，
     * 代开发自建应用需要管理员授权才可以获取，第三方不可获取
     */
    private String externalUserAvatar;

    /**
     * 外部联系人所在企业的简称，
     * 仅当联系人类型是企业微信用户时有此字段
     */
    private String externalUserCorpName;

    /**
     * 外部联系人所在企业的主体名称，
     * 仅当联系人类型是企业微信用户时有此字段
     */
    private String externalUserCorpFullName;

    /**
     * 外部联系人的类型，
     * 1-表示该外部联系人是微信用户，
     * 2-表示该外部联系人是企业微信用户
     */
    private String externalUserType;

    /**
     * 外部联系人性别
     * 0-未知
     * 1-男性
     * 2-女性
     */
    private String externalUserGender;

    /**
     * 外部联系人[在微信开放平台]的唯一身份标识（微信unionid）
     * 通过此字段企业可将外部联系人与公众号/小程序用户关联起来。
     * 仅当联系人类型是微信用户，且企业或第三方服务商绑定了微信开发者ID有此字段
     * see https://work.weixin.qq.com/api/doc/90000/90135/92114#%E5%A6%82%E4%BD%95%E7%BB%91%E5%AE%9A%E5%BE%AE%E4%BF%A1%E5%BC%80%E5%8F%91%E8%80%85ID
     */
    private String unionid;

    /**
     * 外部联系人头像，代开发自建应用需要管理员授权才可以获取，第三方不可获取，上游企业不可获取下游企业客户该字段
     */
    private String wechatAvatar;

}
