/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.job;

import com.howbuy.crm.wechat.service.service.WechatFullDeptDataScheduleService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: 同步企业微信群企业员工job类
 * @date 2023/11/6 10:52
 * @since JDK 1.8
 */
@Slf4j
@Component
public class SyncChatEmpJob extends AbstractBatchMessageJob {

    /**
     * 调度消息队列，nacos中需要配置sync.SYNC_CHAT_EMP_JOB对应的队列名称
     */
    @Value("${sync_chat_emp_channel}")
    private String queue;

    @Autowired
    private WechatFullDeptDataScheduleService wechatFullDeptDataScheduleService;

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("SyncChatEmpJob process start");
        try {
            wechatFullDeptDataScheduleService.execute();
        } catch (Exception e) {
            log.error("error in SyncChatEmpJob", e);
        }
        log.info("SyncChatEmpJob process end");
    }

    @Override
    protected String getQuartMessageChannel() {
        // 返回调度配置的队列名
        return queue;
    }

}