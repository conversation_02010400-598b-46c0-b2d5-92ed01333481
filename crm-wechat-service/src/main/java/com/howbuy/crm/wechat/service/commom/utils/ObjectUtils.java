package com.howbuy.crm.wechat.service.commom.utils;

import java.util.Date;

/**
 * @description: 工具类
 * @author: yu.zhang
 * @date: 2023/6/19 13:01 
 * @since JDK 1.8
 * @version: 1.0
 */
public class ObjectUtils {

    public static String trimNullString(Object object){
        return object==null?null:object.toString();
    }

    public Integer  trimIntegerString(Object object){
        return object==null?null:Integer.valueOf(object.toString());
    }


    public Date trimNullDate(Object object){
        return object==null?null: new Date((Integer)object);
    }
    //微信获取的时间需要*1000
    public static Date trimNullDateMulK(Object object){
        return object==null?null: new Date(Long.parseLong(object.toString())* 1000);
    }
}
