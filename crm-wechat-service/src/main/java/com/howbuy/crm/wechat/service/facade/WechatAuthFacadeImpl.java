/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.facade;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.wechatauth.GetUserIdRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatauth.GetUserIdVO;
import com.howbuy.crm.wechat.client.facade.wechatauth.WechatAuthFacade;
import com.howbuy.crm.wechat.service.service.wechatauth.WechatAuthService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 微信身份认证接口实现
 * @date 2024/9/19 10:03
 * @since JDK 1.8
 */
@DubboService
public class WechatAuthFacadeImpl implements WechatAuthFacade {

    @Resource
    private WechatAuthService wechatAuthService;

    @Override
    public Response<GetUserIdVO> getUserId(GetUserIdRequest request) {
        return wechatAuthService.getUserId(request);
    }
}
