package com.howbuy.crm.wechat.service.repository;

import com.howbuy.crm.wechat.dao.mapper.CmWechatVoiceRemindMapper;
import com.howbuy.crm.wechat.dao.po.CmWechatVoiceRemindPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @description: 睿狐拨号后落表数据，暂无后续业务要求，只是落表
 * @author: yu.zhang
 * @date: 2023/6/26 14:57 
 * @since JDK 1.8
 * @version: 1.0
 */
@Repository
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class CmWechatVoiceRemindRepository {

    @Autowired
    private CmWechatVoiceRemindMapper cmWechatVoiceRemindMapper;

    @Autowired
    private CommonRepository commonRepository;


    /**
     * @description:睿狐拨号后落表数据
     * @param wechatVoiceRemind
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/26 15:01
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void insertWechatExternal(CmWechatVoiceRemindPO wechatVoiceRemind) {
        wechatVoiceRemind.setId(commonRepository.getWechatIdBySeq());
        wechatVoiceRemind.setCreateTime(new Date());
        cmWechatVoiceRemindMapper.insertSelective(wechatVoiceRemind);
    }
}
