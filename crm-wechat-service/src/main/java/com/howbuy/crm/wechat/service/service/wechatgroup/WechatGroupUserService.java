/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.wechatgroup;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.base.model.BaseConstantEnum;
import com.howbuy.crm.wechat.client.enums.JoinGroupStatusEnum;
import com.howbuy.crm.wechat.client.producer.vo.*;
import com.howbuy.crm.wechat.dao.bo.UserGroupInfoBO;
import com.howbuy.crm.wechat.dao.bo.WechatGroupBO;
import com.howbuy.crm.wechat.dao.mapper.CmWechatGroupUserMapper;
import com.howbuy.crm.wechat.dao.mapper.customize.CustomizeCmWechatCustInfoMapper;
import com.howbuy.crm.wechat.dao.po.CmWechatGroupPO;
import com.howbuy.crm.wechat.service.repository.CmWechatCustInfoRepository;
import com.howbuy.crm.wechat.dao.po.CmWechatGroupUserNewPO;
import com.howbuy.crm.wechat.service.commom.enums.ChatFlagEnum;
import com.howbuy.crm.wechat.service.repository.CmWechatGroupRepository;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.enums.YesOrNoEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/5/27 3:18 PM
 * @since JDK 1.8
 */
@Service
@Slf4j
public class WechatGroupUserService {
    @Autowired
    private CustomizeCmWechatCustInfoMapper customizeCmWechatCustInfoMapper;
    @Autowired
    private CmWechatCustInfoRepository cmWechatCustInfoRepository;
    @Autowired
    private CmWechatGroupUserMapper cmWechatGroupUserMapper;
    @Autowired
    private CmWechatGroupRepository cmWechatGroupRepository;

    /**
     * @description
     * @param hbOneNo
     * @param wechatNickName
     * @param deptIdList
     * @param pageNo
     * @param pageSize
     * @return
     * <AUTHOR>
     * @date 2024/5/27 3:35 PM
     * @since JDK 1.8
     */
    public UserGroupVO queryUserGroupList(String companyNo,
                                          String hbOneNo,
                                          String wechatNickName,
                                          List<Integer> deptIdList,
                                          Integer pageNo,
                                          Integer pageSize) {
        Assert.notNull(companyNo, "企微-企业主体不能为空！");

        if (pageNo == null) {
            pageNo = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }



        long start2 = System.currentTimeMillis();
        //分页查询externaluserid
        PageInfo<String> externalUserIdsPageInfo = PageHelper.startPage(pageNo, pageSize, true)
                .doSelectPageInfo(() ->
                        customizeCmWechatCustInfoMapper.selectExternalUserIds(companyNo,
                                hbOneNo,
                                wechatNickName,
                                deptIdList)
                );

        log.info("selectExternalUserIds:{}ms", System.currentTimeMillis() - start2);
        long totalCount = externalUserIdsPageInfo.getTotal();

        // 查询客户所在群相关信息
        long start3 = System.currentTimeMillis();

        List<UserGroupInfoBO> wxGroupUserInfoList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(externalUserIdsPageInfo.getList())){
            wxGroupUserInfoList =  cmWechatCustInfoRepository.selectUserGroupList(companyNo,externalUserIdsPageInfo.getList());
        }
        log.info("selectUserGroupList:{}ms", System.currentTimeMillis() - start3);

        UserGroupVO userGroupVO = new UserGroupVO();

        userGroupVO.setTotalNum((int) totalCount);
        userGroupVO.setTotalPage(new BigDecimal(totalCount).divide(new BigDecimal(pageSize), RoundingMode.UP).intValue());
        List<UserGroupVO.UserGroupDetailVO> userList = new ArrayList<>();

        // 判断wxGroupUserInfoList，如果不为空就遍历wxGroupUserInfoList，按照externalUserId进行分组统计到UserGroupDetailVO中
        if (CollectionUtils.isNotEmpty(wxGroupUserInfoList)) {
            Map<String, UserGroupVO.UserGroupDetailVO> map = Maps.newLinkedHashMap();
            wxGroupUserInfoList.forEach(e -> {
                UserGroupVO.UserGroupDetailVO vo = map.get(e.getExternalUserId());
                if (Objects.isNull(vo)) {
                    vo = new UserGroupVO.UserGroupDetailVO();
                    vo.setHbOneNo(e.getHboneNo());
                    vo.setWechatNickName(e.getNickName());
                    map.put(e.getExternalUserId(), vo);
                }
                // 添加助手时间,如果addTime不为空，就将addTime转成字符串
                if (Objects.nonNull(e.getAddTime())) {
                    vo.setAddAssistantTime(DateUtil.date2String(e.getAddTime(), DateUtil.STR_PATTERN));
                }
                // 如果 userChatFlag 为 0，表示客户还在群中，否则表示客户已退群
                if (Objects.equals(e.getUserChatFlag(), YesOrNoEnum.NO.getCode())) {
                    vo.getGroupIdList().add(e.getChatId());
                } else {
                    vo.getHisGroupIdList().add(e.getChatId());
                }
            });

            // 去重
            map.values().forEach(e -> {
                e.setGroupIdList(removeDuplicate(e.getGroupIdList()));
                e.setHisGroupIdList(removeDuplicate(e.getHisGroupIdList()));
            });
            // 将map转成list
            userList.addAll(map.values());
        }
        userGroupVO.setUserGroupDetailVOList(userList);
        return userGroupVO;
    }

    /**
     * @description 去重
     * @param groupIdList
     * @return
     * <AUTHOR>
     * @date 2024/5/27 3:29 PM
     * @since JDK 1.8
     */
    private List<String> removeDuplicate(List<String> groupIdList) {
//        return new ArrayList<>(new HashSet<>(groupIdList));  创建对象浪费空间
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            return groupIdList.stream().distinct().collect(Collectors.toList());
        }
        return groupIdList;
    }


    /**
     * @description 根据一账通号查询客户当前所在群信息
     * @param companyNoEnum  企微-企业主体
     * @param hboneNo	一账通
     * @return
     * <AUTHOR>
     * @date 2024/5/27 4:34 PM
     * @since JDK 1.8
     */
    public GroupIdVO queryNormalByHboneNo(String companyNo,
                                          String hboneNo) {

        // 根据一账通号获取客户企业微信ID
        String externalUserId = customizeCmWechatCustInfoMapper.getExternalUserIDByHboneNo(companyNo,hboneNo);

        if(StringUtils.isEmpty(externalUserId)){
            return new GroupIdVO();
        }

        List<CmWechatGroupUserNewPO> wxGroupUserInfoList = cmWechatGroupUserMapper.selectNormalByUserId(companyNo,externalUserId);
        GroupIdVO groupIdVO = new GroupIdVO();
        if (CollectionUtils.isNotEmpty(wxGroupUserInfoList)) {
            List<GroupIdVO.GroupIdDetailVO> groupIdDetailVOList = new ArrayList<>();
            groupIdVO.setGroupIdList(groupIdDetailVOList);
            for (CmWechatGroupUserNewPO cmWechatGroupUserPO : wxGroupUserInfoList) {
                GroupIdVO.GroupIdDetailVO groupIdDetailVO = new GroupIdVO.GroupIdDetailVO();
                groupIdDetailVO.setGroupId(cmWechatGroupUserPO.getChatid());
                groupIdDetailVO.setJoinGroupTime(DateUtil.date2String(cmWechatGroupUserPO.getJoinTime(), DateUtil.STR_PATTERN));
                groupIdDetailVOList.add(groupIdDetailVO);
            }
        }

        return groupIdVO;
    }

    /**
     * @description 查询客户是否入群
     * @param companyNoEnum  企微-企业主体
     * @param hboneNo 一账通号
     * @param chatId 群ID
     * @return
     * <AUTHOR>
     * @date 2024/5/28 10:56 AM
     * @since JDK 1.8
     */
    public JoinGroupResultVO queryJoinGroup(String companyNo,
                                            String hboneNo,
                                            String chatId) {

        // 根据一账通号获取客户企业微信ID
        String externalUserId = customizeCmWechatCustInfoMapper.getExternalUserIDByHboneNo(companyNo,hboneNo);

        JoinGroupResultVO joinGroupResultVO = new JoinGroupResultVO();
        if (StringUtils.isEmpty(externalUserId)) {
            joinGroupResultVO.setJoinGroupStatus(JoinGroupStatusEnum.UN_JOIN.getKey());
            return joinGroupResultVO;
        }

        // 根据群ID和客户ID查询是否入群
        CmWechatGroupUserNewPO po = cmWechatGroupUserMapper.selectByChatIdAndUserId(chatId, externalUserId);
        if (Objects.isNull(po)) {
            joinGroupResultVO.setJoinGroupStatus(JoinGroupStatusEnum.UN_JOIN.getKey());
            return joinGroupResultVO;
        }

        // 判断客户是否还在群
        if (Objects.equals(po.getUserchatflag(), StaticVar.CODE_SUCCESS)) {
            joinGroupResultVO.setJoinGroupStatus(JoinGroupStatusEnum.ALREADY_JOIN.getKey());
            joinGroupResultVO.setJoinGroupTime(DateUtil.date2String(po.getJoinTime(), DateUtil.STR_PATTERN));
            return joinGroupResultVO;
        }

        // 客户已退群
        joinGroupResultVO.setJoinGroupStatus(JoinGroupStatusEnum.LEAVE_JOIN.getKey());
        joinGroupResultVO.setJoinGroupTime(DateUtil.date2String(po.getJoinTime(), DateUtil.STR_PATTERN));
        String existGroupTime = getExitGroupTimeString(po,companyNo);
        if (StringUtils.isNotEmpty(existGroupTime)) {
            joinGroupResultVO.setExistGroupTime(existGroupTime);
        }
        return joinGroupResultVO;
    }

    /**
     * @param chatId
     * @return
     * @description 查询群状态
     * <AUTHOR>
     * @date 2024/5/28 11:07 AM
     * @since JDK 1.8
     */
    public GroupBreakStatusVO queryGroupStatus(String chatId) {
        if (StringUtils.isEmpty(chatId)) {
            return null;
        }
        WechatGroupBO groupInfo = cmWechatGroupUserMapper.selectGroupByChatId(chatId);
        if (groupInfo == null) {
            return null;
        }

        GroupBreakStatusVO vo = new GroupBreakStatusVO();
        vo.setGroupBreakStatus(groupInfo.getChatFlag());
        return vo;
    }

    /**
     * @description 根据一帐通查询用户和群信息
     * @param companyNoEnum  企微-企业主体
     * @param hbOneNo
     * @return
     * <AUTHOR>
     * @date 2024/6/4 2:04 PM
     * @since JDK 1.8
     */
    public List<UserGroupInfoVO> queryUserGroupInfoByHbOneNo(String companyNo,
                                                             String hbOneNo) {

        List<UserGroupInfoVO> infoList = new ArrayList<>();
        // 根据一账通号获取客户企业微信ID
        String externalUserId = customizeCmWechatCustInfoMapper.getExternalUserIDByHboneNo(companyNo,hbOneNo);

        if (StringUtils.isEmpty(externalUserId)) {
            throw new BusinessException(BaseConstantEnum.DATA_NOT_FUND.getCode(), "根据一帐通未查询到客户信息");
        }
        List<CmWechatGroupUserNewPO> wxGroupUserInfoList = cmWechatGroupUserMapper.selectInfoByUserId(companyNo,externalUserId);
        for (CmWechatGroupUserNewPO cmWechatGroupUserPO : wxGroupUserInfoList) {
            UserGroupInfoVO groupIdDetailVO = new UserGroupInfoVO();
            groupIdDetailVO.setGroupId(cmWechatGroupUserPO.getChatid());
            if (cmWechatGroupUserPO.getJoinTime() != null) {
                groupIdDetailVO.setJoinGroupTime(DateUtil.date2String(cmWechatGroupUserPO.getJoinTime(), DateUtil.STR_PATTERN));
            }
            if (cmWechatGroupUserPO.getLeaveTime() != null) {
                groupIdDetailVO.setExitGroupTime(DateUtil.date2String(cmWechatGroupUserPO.getLeaveTime(), DateUtil.STR_PATTERN));
            } else if (!StaticVar.CODE_SUCCESS.equals(cmWechatGroupUserPO.getUserchatflag())) {
                // 如果客户已退群但退群时间为空，检查群状态是否为解散（chat_flag=1）
                String exitGroupTime = getExitGroupTimeString(cmWechatGroupUserPO,companyNo);
                if (StringUtils.isNotEmpty(exitGroupTime)) {
                    // 群已解散，使用群的更新时间作为退群时间
                    groupIdDetailVO.setExitGroupTime(exitGroupTime);
                }
            }
            groupIdDetailVO.setStatus(StaticVar.CODE_SUCCESS.equals(cmWechatGroupUserPO.getUserchatflag()) ?
                    JoinGroupStatusEnum.ALREADY_JOIN.getKey() : JoinGroupStatusEnum.LEAVE_JOIN.getKey());
            infoList.add(groupIdDetailVO);
        }
        return infoList;
    }

    /**
     * @param userGroupPo 用户群关系PO
     * @param companyNo  企微-企业主体
     * @return java.lang.String 退群时间字符串，或null
     * @description 获取退群时间。如果用户记录中有退群时间，直接使用。否则，如果群已解散，使用群的更新时间作为退群时间。
     * <AUTHOR>
     * @date 2025-06-18 14:46:20
     * @since JDK 1.8
     */
    private String getExitGroupTimeString(CmWechatGroupUserNewPO userGroupPo,String companyNo) {
        if (userGroupPo.getLeaveTime() != null) {
            return DateUtil.date2String(userGroupPo.getLeaveTime(), DateUtil.STR_PATTERN);
        }

        CmWechatGroupPO groupInfo = cmWechatGroupRepository.getByChatId(userGroupPo.getChatid(),companyNo);
        if (groupInfo != null && ChatFlagEnum.isDeleted(groupInfo.getChatFlag()) && groupInfo.getUpdateTime() != null) {
            return DateUtil.date2String(groupInfo.getUpdateTime(), DateUtil.STR_PATTERN);
        }

        return null;
    }
}