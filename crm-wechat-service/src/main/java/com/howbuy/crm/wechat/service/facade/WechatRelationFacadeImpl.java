package com.howbuy.crm.wechat.service.facade;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.wechatrelation.QueryWechatRelationBatchRequest;
import com.howbuy.crm.wechat.client.domain.request.wechatrelation.QueryWechatRelationRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatrelation.QueryWechatRelationResponse;
import com.howbuy.crm.wechat.client.domain.response.wechatrelation.QueryWechatRelationBatchResponse;
import com.howbuy.crm.wechat.client.facade.wechatrelation.WechatRelationFacade;
import com.howbuy.crm.wechat.service.service.custrelation.WechatCustRelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @description: 企业微信关系查询接口实现类
 * <AUTHOR>
 * @date 2024/5/23 11:12
 * @since JDK 1.8
 */
@DubboService
@Slf4j
public class WechatRelationFacadeImpl implements WechatRelationFacade {

    @Autowired
    private WechatCustRelationService wechatCustRelationService;

    @Override
    public Response<QueryWechatRelationResponse> queryWechatRelation(QueryWechatRelationRequest request) {
        return wechatCustRelationService.queryWechatRelation(request);
    }

    @Override
    public Response<QueryWechatRelationBatchResponse> queryWechatRelationBatch(QueryWechatRelationBatchRequest request) {
        return wechatCustRelationService.queryWechatRelationBatch(request);
    }
} 