/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.exposeimpl;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.producer.wechatusertag.WechatUserTagService;
import com.howbuy.crm.wechat.client.producer.wechatusertag.request.AddUserTagRequest;
import com.howbuy.crm.wechat.client.producer.wechatusertag.request.DeleteUserTagRequest;
import com.howbuy.crm.wechat.service.service.wechatusertag.UserTagService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @description: 企业微信标签接口实现类
 * <AUTHOR>
 * @date 2024/6/12 19:26
 * @since JDK 1.8
 */
@DubboService
@Slf4j
public class WechatUserTagServiceImpl implements WechatUserTagService {

    @Autowired
    private UserTagService userTagService;

    @Override
    public Response<String> add(AddUserTagRequest request) {
        return userTagService.add(request);
    }

    @Override
    public Response<String> delete(DeleteUserTagRequest request) {
        return userTagService.delete(request);
    }
}