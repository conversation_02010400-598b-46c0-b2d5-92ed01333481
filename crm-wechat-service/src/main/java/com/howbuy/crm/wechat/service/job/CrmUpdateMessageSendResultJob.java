/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.job;

import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.howbuy.crm.wechat.client.enums.MessageSendStatusEnum;
import com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO;
import com.howbuy.crm.wechat.dao.vo.message.MessageSendInfoVO;
import com.howbuy.crm.wechat.service.batch.NewBatchOperateExecutor;
import com.howbuy.crm.wechat.service.batch.QueryPageService;
import com.howbuy.crm.wechat.service.service.message.MessageSendInfoService;
import com.howbuy.crm.wechat.service.service.message.result.MessageResultFactory;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (更新消息发送结果)
 * @date 2024/10/7 9:59
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CrmUpdateMessageSendResultJob extends AbstractBatchMessageJob {

    /**
     * 更新消息发送结果 接收消息队列
     */
    @Value("${crm_update_message_result_channel}")
    private String crmUpdateMessageResultChannel;

    @Resource
    private NewBatchOperateExecutor newBatchOperateExecutor;

    @Autowired
    private MessageSendInfoService messageSendInfoService;


    @Autowired
    private MessageResultFactory messageResultfactory;

    public CrmUpdateMessageSendResultJob() {
    }

    @Override
    protected String getQuartMessageChannel() {
        return crmUpdateMessageResultChannel;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        // 查询消息表（message_send_info）
        MessageSendInfoVO sendInfoVO = new MessageSendInfoVO();
        // 推送中的
        sendInfoVO.setSendStatusList(Lists.newArrayList(MessageSendStatusEnum.PUSHING.getCode()));

        newBatchOperateExecutor.poolExecute(sendInfoVO, new QueryPageService<MessageSendInfoVO, MessageSendInfoPO>() {
            @Override
            public int doBusiness(List<MessageSendInfoPO> dataList) {
                for (MessageSendInfoPO sendPo : dataList) {
                    // 更新消息发送结果
                    messageResultfactory.update(sendPo);
                }
                return dataList.size();
            }

            @Override
            public Page<MessageSendInfoPO> queryPage(MessageSendInfoVO queryVo) {
                return messageSendInfoService.selectPageByVoAndSixHours(queryVo);
            }
        });

    }

}