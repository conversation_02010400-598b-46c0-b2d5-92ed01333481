/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.domain.message.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (群机器发送消息-参数)
 * <AUTHOR>
 * @date 2023/10/12 10:23
 * @since JDK 1.8
 */
@Data
public class BotMessageVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * webhookurl 拼接的key值, 只需要value值，不需要key=
     */
    private String webHookUrlKey;

   /**
    * 消息类型
    */
     private Integer messageType;

    /**
    * 文本类型内容,msgType = 1 时必填
    */
    private TextBotMessageVo textBotMessage;

    /**
     * 图片文件字节流,msgType = 4 时必填
     */
//    private byte[] fileData;

    /**
     * 图片文件路径,msgType = 4 时必填
     * 发送时， 读取filePath路径下的文件，转换成字节流
      */
    private String filePath;
    /**
     * 图文消息内容,msgType = 5 时必填
     */
    private NewsBotMessageVo newsBotMessage;

    /**
     * 相对路径
     */
    private String relativePath;

    /**
     * 文件名
     */
    private String fileName;


}