/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.outerservice.hkacccenter;

import com.howbuy.common.exception.BusinessException;
import com.howbuy.crm.account.client.enums.ExceptionCodeEnum;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.hkacconline.facade.query.queryhkcusthbonerel.QueryHkCustHboneRelFacade;
import com.howbuy.hkacconline.facade.query.queryhkcusthbonerel.QueryHkCustHboneRelRequest;
import com.howbuy.hkacconline.facade.query.queryhkcusthbonerel.QueryHkCustHboneRelResponse;
import com.howbuy.hkacconline.facade.query.queryhkouterbind.QueryHkOuterAcctLoginBindFacade;
import com.howbuy.hkacconline.facade.query.queryhkouterbind.QueryHkOuterAcctLoginBindRequest;
import com.howbuy.hkacconline.facade.query.queryhkouterbind.QueryHkOuterAcctLoginBindResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/5/22 08:58
 * @since JDK 1.8
 */
@Slf4j
@Service
public class QueryHkWechatAcctBindOuterService {

    @DubboReference(registry = "hk-acc-online-service", check = false)
    private QueryHkOuterAcctLoginBindFacade queryHkOuterAcctLoginBindFacade;
    @DubboReference(registry = "hk-acc-online-service", check = false)
    private QueryHkCustHboneRelFacade queryHkCustHboneRelFacade;


    /**
     * @param unionid
     * @param type
     * @return java.lang.String
     * @description:(基于绑定的uninoID 查询香港客户号)
     * @author: xufanchao
     * @date: 2025/5/22 09:30
     * @since JDK 1.8
     */
    public String getAcctLoginHboneNoByUnionid(String unionid, String type) {
        QueryHkOuterAcctLoginBindRequest req = new QueryHkOuterAcctLoginBindRequest();
        req.setOuterAcct(unionid);
        req.setOuterSysType(type);
        QueryHkOuterAcctLoginBindResponse response = queryHkOuterAcctLoginBindFacade.execute(req);
        if (response.isSuccess()) {
            return response.getHkCustNo();
        } else {
            throw new BusinessException(Constants.SYSTEM_ERROR, response.getDescription());
        }
    }


    /**
     * 根据香港客户号查询一账通号
     *
     * @param hkCustNo 香港客户号
     * @return 一账通号
     */
    public String queryHboneNoByHkCustNo(String hkCustNo) {
        log.info("queryHboneNoByHkCustNo-根据香港客户号查询一账通号,hkCustNo:{}", hkCustNo);
        // 1.参数校验
        if (StringUtils.isBlank(hkCustNo)) {
            throw new com.howbuy.crm.wechat.service.commom.exception.BusinessException(ExceptionCodeEnum.PARAMS_ERROR.getCode(), "香港客户号不能为空");
        }
        // 2.查询一账通号
        QueryHkCustHboneRelRequest queryHkCustHboneRelRequest = new QueryHkCustHboneRelRequest();
        queryHkCustHboneRelRequest.setHkCustNo(hkCustNo);
        QueryHkCustHboneRelResponse response = queryHkCustHboneRelFacade.execute(queryHkCustHboneRelRequest);
        if (!response.isSuccess()) {
            throw new BusinessException(Constants.SYSTEM_ERROR, response.getDescription());
        }
        return response.getHboneNo();
    }
}