/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.outerservice.wechatapi.vo;

import lombok.Data;

/**
 * @description: (获取部门成员详情)
 * <AUTHOR>
 * @date 2025/7/21 16:39
 * @since JDK 1.8
 */
@Data
public class WechatQueryUserVO {


    /**
     *DEPARTMENT_ID = departmentId
     */
    private String departmentId;

    /**
     * FETCH_CHILD = 1：递归调用
     */
    private String fetchChild;


    /**
     * COMPANY_NO = companyNo-企业编码
     */
    private String companyNo;

}