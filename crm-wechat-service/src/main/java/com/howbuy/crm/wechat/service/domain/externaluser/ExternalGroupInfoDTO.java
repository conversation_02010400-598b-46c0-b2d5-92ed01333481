/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.domain.externaluser;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @description: 外部群信息
 * <AUTHOR>
 * @date 2023/10/31 11:23
 * @since JDK 1.8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExternalGroupInfoDTO {

    /**
     * 客户群ID
     */
    private String chatId;

    /**
     * 客户群跟进状态。
     * 0 - 跟进人正常
     * 1 - 跟进人离职
     * 2 - 离职继承中
     * 3 - 离职继承完成
     */
    private String status;

    /**
     * 群名
     */
    private String name;

    /**
     * 群主ID
     */
    private String owner;

    /**
     * 群的创建时间
     */
    private Date createTime;

    /**
     * 群公告
     */
    private String notice;

    /**
     * 群成员列表
     */
    private List<ExternalGroupChatUserDTO> memberList;

    /**
     * 群管理员列表
     */
    private List<ExternalGroupChatUserDTO> adminList;

    /**
     * 返回码
     */
    private String errCode;
    /**
     * 返回信息
     */
    private String errMsg;
}