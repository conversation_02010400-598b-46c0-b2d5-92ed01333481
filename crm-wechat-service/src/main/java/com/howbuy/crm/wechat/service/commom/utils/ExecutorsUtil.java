package com.howbuy.crm.wechat.service.commom.utils;

import com.howbuy.common.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.core.task.TaskDecorator;

import java.util.concurrent.*;

public abstract class ExecutorsUtil {

    private static final Logger logger = LoggerFactory.getLogger(ExecutorsUtil.class);

    public static ExecutorService fixedPool(int threadNumber, String threadPoolNamePrefix) {
        return fixedPool(threadNumber, threadNumber, threadPoolNamePrefix);
    }

    /**
     * 创建线程池
     *
     * @param corePoolSize         核心线程数
     * @param maxPoolSize          最大线程数
     * @param threadPoolNamePrefix 线程池名称前缀
     * @param keepAliveTime        存活时间(秒)
     * @param workQueue            队列类型
     */
    public static ExecutorService newPool(int corePoolSize, int maxPoolSize, String threadPoolNamePrefix, long keepAliveTime,
                                          BlockingQueue<Runnable> workQueue) {
        return new ThreadPoolExecutor(corePoolSize, maxPoolSize, keepAliveTime, TimeUnit.SECONDS,
                workQueue, threadFactory(threadPoolNamePrefix)) {

            @Override
            public void execute(Runnable command) {
                ApplicationContext context
                        = new AnnotationConfigApplicationContext(
                        TaskDecorator.class);

                // Getting the bean
                TaskDecorator decorator = context.getBean("uuidTaskDecorator",
                        TaskDecorator.class);

//                TaskDecorator decorator=ApplicationContext.getBean("uuidTaskDecorator", TaskDecorator.class);
                // 为线程池中的线程添加MDC信息
                super.execute(decorator.decorate(command));
            }
        };
    }


    /**
     * 创建固定线程数的线程池
     *
     * @param corePoolSize 核心线程数
     * @param maxPoolSize  最大线程数
     */
    public static ExecutorService fixedPool(int corePoolSize, int maxPoolSize, String threadPoolNamePrefix) {
        return new ThreadPoolExecutor(corePoolSize, maxPoolSize, 0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(), threadFactory(threadPoolNamePrefix)) {
            @Override
            public void execute(Runnable command) {
                ApplicationContext context
                        = new AnnotationConfigApplicationContext(
                        TaskDecorator.class);
                // Getting the bean
                TaskDecorator decorator = context.getBean("uuidTaskDecorator",
                        TaskDecorator.class);

                super.execute(decorator.decorate(command));
            }
        };
    }

    private static ThreadFactory threadFactory(final String threadPoolNamePrefix) {
        final ThreadFactory defaultFactory = Executors.defaultThreadFactory();
        return r -> {
            Thread thread = defaultFactory.newThread(r);
            thread.setName(String.format(StringUtil.isEmpty(threadPoolNamePrefix) ? "当前线程-%s" :
                    threadPoolNamePrefix + "-%s", thread.getName()));
            logger.info("创建新线程:{},线程ID:{}", thread.getName(), thread.getId());
            return thread;
        };
    }

}
