/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.domain.message.vo;

import com.howbuy.cc.message.send.company.bot.model.Article;
import com.howbuy.cc.message.send.company.bot.model.NewsBotMessage;
import com.howbuy.cc.message.send.company.bot.model.TextBotMessage;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (群机器发送消息-参数)
 * <AUTHOR>
 * @date 2023/10/12 10:23
 * @since JDK 1.8
 */
@Data
public class NewsBotMessageVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 图文消息，一个图文消息支持1到8条图文
     */
    private List<ArticleMessageVo> articles;


}