package com.howbuy.crm.wechat.service.outerservice.acccenter;

import com.howbuy.acccenter.facade.query.querywechatbindinfo.QueryWechatAcctBindFacade;
import com.howbuy.acccenter.facade.query.querywechatbindinfo.QueryWechatAcctBindRequest;
import com.howbuy.acccenter.facade.query.querywechatbindinfo.QueryWechatAcctBindResponse;
import com.howbuy.acccenter.facade.query.querywechatbindinfo.WechatAcctBindInfo;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.commom.enums.BaseReturnCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: 客户手机微信关注好买关系接口
 * @author: yu.zhang
 * @date: 2023/6/19 15:08 
 * @since JDK 1.8
 * @version: 1.0
 */
@Slf4j
@Service
public class QueryWechatAcctBindService {

//    @DubboReference(registry = Constants.ZK_ACC_CENTER, check = false)
    @Autowired
    private QueryWechatAcctBindFacade queryWechatAcctBindFacade;

    /**
     * @description:客户通过微信添加的企业公众号关注
     * @param unionid
     * @return java.lang.String
     * @author: yu.zhang
     * @date: 2023/6/19 15:10
     * @since JDK 1.8
     */
    public String getAcctHboneNoByUnionid(String unionid){

        String hboneno = Strings.EMPTY;

        QueryWechatAcctBindRequest wechatreq = new QueryWechatAcctBindRequest();
        wechatreq.setUnionId(unionid);
        QueryWechatAcctBindResponse wechatrep = queryWechatAcctBindFacade.execute(wechatreq);
        if(wechatrep != null && BaseReturnCodeEnum.ACCSUCCESS.getCode().equals(wechatrep.getReturnCode())){
            for(WechatAcctBindInfo info:wechatrep.getWechatList()){
                if(StringUtils.isNotBlank(info.getHboneNo())){
                    hboneno = info.getHboneNo();
                    break;
                }
            }
        }
        return hboneno;
    }
}
