/**
 * Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 企业应用类型 ： customer-客户联系  notice-企业内部业务消息通知
 * @date 2025年8月14日 17:11:50
 */
public enum WechatApplicationTypeEnum {


    /**
     * 企业微信-内建应用-客户联系
     */
    CUSTOMER("customer", "内建应用-客户联系" ),

    /**
     * 企业微信-自建应用-通知应用
     * [Eg :
     * 好买财富，使用自建应用[CRM]业务通知
     * 好买基金，暂无应用， 等待配置
     * ]
     */
    NOTICE("notice", "自建应用-业务通知应用")
;

    /**
     * 编码
     */
    private String code;


    /**
     * 描述
     */
    private String description;


    WechatApplicationTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }
    public String getDescription() {
        return description;
    }



    /**
     * 根据编码获取枚举
     * @param code
     * @return
     */
    public static WechatApplicationTypeEnum getEnum(String code) {
        for (WechatApplicationTypeEnum value : WechatApplicationTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }




}
