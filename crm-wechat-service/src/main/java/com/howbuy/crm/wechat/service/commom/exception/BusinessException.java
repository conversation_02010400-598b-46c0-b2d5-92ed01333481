/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.exception;

import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/3/15 13:07
 * @since JDK 1.8
 */
@Setter
@Getter
public class BusinessException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private String exceptionCode;

    private String exceptionDesc;

    public BusinessException() {
        super();
    }

    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(String exceptionCode, String exceptionDesc) {
        super(exceptionDesc);
        this.exceptionCode = exceptionCode;
        this.exceptionDesc = exceptionDesc;
    }

    public BusinessException(ResponseCodeEnum exceptionCode) {
        super(exceptionCode.getDescription());
        this.exceptionCode = exceptionCode.getCode();
        this.exceptionDesc = exceptionCode.getDescription();
    }


}