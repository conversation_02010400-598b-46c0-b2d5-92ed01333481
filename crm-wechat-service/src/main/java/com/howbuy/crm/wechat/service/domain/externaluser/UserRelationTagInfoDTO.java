package com.howbuy.crm.wechat.service.domain.externaluser;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @classname: ExternalUserInfoDTO
 * @author: yu.zhang
 * @description: 外部客户 vs 企业微信成员  绑定关系中  企业微信成员 对外部客户  打上的 标签列表
 *  NOTICE: 标签的分组名称（标签功能需要企业微信升级到2.7.5及以上版本）
 * @creatdate: 2021-02-08 16:48
 * @since: JDK1.8
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class UserRelationTagInfoDTO implements Serializable {

    /**
     *企业成员-->外部联系人 标注的标签中
     *
     */
    private String groupName;

    /**
     *企业成员-->外部联系人 标注的标签中
     * 标签名称
     */
    private String tagName;

    /**
     *企业成员-->外部联系人 标注的标签中
     * 标签类型, 1-企业设置，2-用户自定义，3-规则组标签（仅系统应用返回）
     */
    private String type;

    /**
     *企业成员-->外部联系人 标注的标签中
     * 企业标签的id，用户自定义类型标签（type=2）不返回
     */
    private String tagId;
}
