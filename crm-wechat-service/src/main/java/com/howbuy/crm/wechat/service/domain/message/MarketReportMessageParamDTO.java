/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.domain.message;

import com.howbuy.common.utils.StringUtil;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (营销喜报  接受消息 中 参数属性对象)
 * <AUTHOR>
 * @date 2023/10/9 15:26
 * @since JDK 1.8
 */
@Data
public class MarketReportMessageParamDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 金额单位-万
     */
    private static final String UNIT_VALUE_W ="万";
    /**
     * 金额单位-亿
     */
    private static final String UNIT_VALUE_Y ="亿";


    /**
     * 金额单位-元
     */
    private static final String UNIT_VALUE ="元";

    /**
     * 万 单位比较值
     */
    private static final BigDecimal TEN_THOUSAND_COMPARE_VALUE =new BigDecimal(10000);

    /**
     * 亿 单位比较值
     */
    private static final BigDecimal HUNDRED_MILLION_COMPARE_VALUE = new BigDecimal(100000000);

    /**
     * 部门-组织架构code
     */
    private String orgCode;

    /**
     * 部门-组织架构 名称  Eg: 第三团队
     */
    private String orgName;


    /**
     * 区域 -组织架构code
     */
    private String districtCode;

    /**
     * 区域 -组织架构 名称 Eg: 成都区
     */
    private String districtName;

    /**
     * 中心 -组织架构code
     */
    private String centerOrgCode;

    /**
     * 中心 -组织架构 名称
     */
    private String centerOrgName;

    /**
     * 投顾名称 Eg: 张三
     */
    private String consName;
    /**
     * 产品名称 Eg: 华宝信托-宝幡稳健回报十八号-第一期
     */
    private String prodName;

    /**
     * 金额 Eg: 1000
     */
    private String realPayAmt;

    /**
     * 金额-人民币  Eg: 1000
     */
    private String  realPayAmtRmb;

    /**
     * 货币类型
     */
    private String currency;

    /**
     * 好买产品线( 0:现金管理工具;1:债券型;2:类固定收益;3:对冲;4:股票型;5:PE、VC;6:房地产基金;7:其他;8:海外)
     */
    private String hbType;

    /**
     * 策略分类101	中国股票 102	香港股票 103	美国股票 104	其他 201	中国债券 202	海外债券 203	非标产品 204	中国货币市场产品 205	其他 301	多策略 302	多空仓型 303	管理期货 304	宏观策略 305	市场中性 306	套利型 307	其他 401	中国股权 402	其他 501	FOF 502	结构化产品 503	其他 601	其他
     */
    private String strategyClassify;

    /**
     * 是否在途 0-否 1-是
     */
    private String isOnTheWay;

    /**
     * 当前部门的层级
     */
    private Integer orgLevel;

    /**
     * 产品代码
     */
    private String prodCode;

    /**
     * 产品分销
     */
    private String cpfx;

    /**
     * 拼音大写首字母
     */
    private String consNamePinyinInitial;


    /**
     * 金额是否超过 1w
     * @param amtValue
     * @return
     */
    private  static boolean isGtw(BigDecimal amtValue){
        return amtValue.compareTo(TEN_THOUSAND_COMPARE_VALUE)>0;
    }

    /**
     * @description: (金额是否超过 1亿)
     * @param amtValue 金额
     * @return boolean 是否超过
     * @author: jin.wang03
     * @date: 2024/3/20 17:53
     * @since JDK 1.8
     */
    private static boolean isGty(BigDecimal amtValue) {
        return amtValue.compareTo(HUNDRED_MILLION_COMPARE_VALUE) > 0;
    }

    /**
     * @description: 获取展示金额
     *
     * @return java.lang.String 金额展示值
     * @author: jin.wang03
     * @date: 2024/3/20 17:55
     * @since JDK 1.8
     */
    public String getDisPlayAmtValue() {
        if (StringUtil.isEmpty(getRealPayAmt())) {
            return "";
        }
        BigDecimal amtValue = new BigDecimal(getRealPayAmt());
        // 如果超过1亿，则换算成亿。 如果超过1w，则换算成万
        return isGty(amtValue) ?
                String.valueOf(amtValue.divide(HUNDRED_MILLION_COMPARE_VALUE, BigDecimal.ROUND_DOWN).intValue()) :
                isGtw(amtValue) ?
                        String.valueOf(amtValue.divide(TEN_THOUSAND_COMPARE_VALUE, BigDecimal.ROUND_DOWN).intValue()) :
                        String.valueOf(amtValue.intValue());
    }

    /**
     * 获取展示单位
     * @return
     */
    public String getDisplayUnitValue() {
        if (StringUtil.isEmpty(getRealPayAmtRmb())) {
            return "";
        }
        BigDecimal amtValue = new BigDecimal(getRealPayAmtRmb());
        return isGty(amtValue) ? UNIT_VALUE_Y : isGtw(amtValue) ? UNIT_VALUE_W : UNIT_VALUE;
    }

}