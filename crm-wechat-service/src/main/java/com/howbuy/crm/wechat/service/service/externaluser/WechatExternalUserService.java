/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.externaluser;

import com.howbuy.crm.wechat.client.domain.request.externaluser.GetExternalUserRequest;
import com.howbuy.crm.wechat.client.domain.response.externaluser.GetExternalUserVO;
import com.howbuy.crm.wechat.service.commom.utils.CompanyNoUtils;
import com.howbuy.crm.wechat.service.domain.externaluser.ExternalUserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import com.howbuy.crm.wechat.client.domain.response.externaluser.ExternalUserProfileInfoVO;
import com.howbuy.crm.wechat.client.domain.response.externaluser.ExternalUserRelationInfoVO;
import com.howbuy.crm.wechat.service.domain.externaluser.ExternalUserProfileInfoDTO;
import com.howbuy.crm.wechat.service.domain.externaluser.ExternalUserRelationInfoDTO;

import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 企微外部联系人业务服务
 * @date 2025-08-19 19:27:20
 * @since JDK 1.8
 */
@Service
@Slf4j
public class WechatExternalUserService {

    @Resource
    private com.howbuy.crm.wechat.service.service.WechatExternalUserService originalWechatExternalUserService;

    /**
     * @param request 请求参数
     * @return 返回结果
     * @description: 获取企微外部联系人详情信息
     * <AUTHOR>
     * @date 2025-08-19 19:27:20
     * @since JDK 1.8
     */
    public GetExternalUserVO getExternalUser(GetExternalUserRequest request) {
        String validCompanyNo = CompanyNoUtils.getOrDefault(request.getCompanyNo());
        ExternalUserInfoDTO externalUserInfoDTO = originalWechatExternalUserService.getExternalUser(request.getExternalUserId(), validCompanyNo);
        
        GetExternalUserVO vo = new GetExternalUserVO();
        
        // 手动复制属性
        vo.setHboneNo(externalUserInfoDTO.getHboneNo());
        vo.setHkHboneNo(externalUserInfoDTO.getHkHboneNo());
        
        // 转换ExternalUserProfileInfoDTO列表为ExternalUserProfileInfoVO列表
        if (externalUserInfoDTO.getExternalUserProfileList() != null) {
            List<ExternalUserProfileInfoVO> profileVOList = new ArrayList<>();
            for (ExternalUserProfileInfoDTO dto : externalUserInfoDTO.getExternalUserProfileList()) {
                ExternalUserProfileInfoVO profileVO = new ExternalUserProfileInfoVO();
                profileVO.setType(dto.getType());
                profileVO.setName(dto.getName());
                profileVO.setProfileMap(dto.getProfileMap());
                profileVOList.add(profileVO);
            }
            vo.setExternalUserProfileList(profileVOList);
        }
        
        // 转换ExternalUserRelationInfoDTO列表为ExternalUserRelationInfoVO列表
        if (externalUserInfoDTO.getFollowUserList() != null) {
            List<ExternalUserRelationInfoVO> relationVOList = new ArrayList<>();
            for (ExternalUserRelationInfoDTO dto : externalUserInfoDTO.getFollowUserList()) {
                ExternalUserRelationInfoVO relationVO = new ExternalUserRelationInfoVO();
                relationVO.setUserid(dto.getUserid());
                relationVO.setExternalUserId(dto.getExternalUserId());
                relationVO.setRemark(dto.getRemark());
                relationVO.setDescription(dto.getDescription());
                relationVO.setCreatetime(dto.getCreatetime());
                relationVO.setRemarkCorpName(dto.getRemarkCorpName());
                relationVOList.add(relationVO);
            }
            vo.setFollowUserList(relationVOList);
        }
        
        return vo;
    }
}