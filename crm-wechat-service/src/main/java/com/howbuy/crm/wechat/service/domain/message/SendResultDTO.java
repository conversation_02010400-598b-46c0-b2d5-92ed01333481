/**
 * Copyright (c) 2023, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.domain.message;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (消息发送 结果 )
 * <AUTHOR>
 * @date 2023/10/12 16:10
 * @since JDK 1.8
 */
@Data
public class SendResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

//    code	String     状态码 0-成功
//
//    msg	String     描述
//
//    messageUUID	String     唯一标识

    /**
     * 状态码   接口第三方 返回状态码
     */
    private String code;

    /**
     * 描述 接口第三方 返回描述
     */
    private String msg;

    /**
     * 唯一标识 接口第三方 返回描述
     */
    private String messageUUID;

    /**
     * 是否成功 true-成功 false-失败
     */
    private boolean success =false;


    /**
     * 发送状态， 1-未推送；2-推送中；3-推送成功；4-推送失败；5-重新推送
     */
    private String sendStatus;



}