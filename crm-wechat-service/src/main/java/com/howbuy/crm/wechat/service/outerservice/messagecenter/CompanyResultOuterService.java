/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.outerservice.messagecenter;

import com.alibaba.fastjson.JSON;
import com.howbuy.cc.message.request.QuerySystemSendEmailStatusRequest;
import com.howbuy.cc.message.response.QuerySystemSendEmailStatusResponse;
import com.howbuy.crm.wechat.client.enums.CcMessageTaskStatusEnum;
import com.howbuy.crm.wechat.client.enums.MessageSendStatusEnum;
import com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO;
import com.howbuy.crm.wechat.service.business.CompanyResultBusiness;
import com.howbuy.crm.wechat.service.domain.message.UpdateMessageSendStatusDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description: (企业微信发送消息)
 * @date 2023/10/10 14:26
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CompanyResultOuterService {

    @Autowired
    private CompanyResultBusiness companyResultBusiness;

    /**
     * @param sendInfo
     * @return com.howbuy.crm.wechat.service.domain.message.UpdateMessageSendStatusDTO
     * @description:(请在此添加描述)
     * @author: jin.wang03
     * @date: 2024/10/30 11:26
     * @since JDK 1.8
     */
    public UpdateMessageSendStatusDTO getEmailResult(MessageSendInfoPO sendInfo) {
        UpdateMessageSendStatusDTO result = new UpdateMessageSendStatusDTO();
        result.setSendStatus(MessageSendStatusEnum.PUSH_FAIL.getCode());

        QuerySystemSendEmailStatusRequest statusRequest = new QuerySystemSendEmailStatusRequest();
        statusRequest.setTraceId(sendInfo.getMessageUUID());
        QuerySystemSendEmailStatusResponse statusResponse = companyResultBusiness.querySendEmailStatus(statusRequest);
        log.info("查询邮件的发送状态,request:{}, 结果:{}", JSON.toJSONString(statusRequest), JSON.toJSONString(statusResponse));
        if (statusResponse != null) {
            if (statusResponse.getCode() == 0 && Objects.nonNull(statusResponse.getTaskStatus())) {
                Integer taskStatus = statusResponse.getTaskStatus();
                result.setSendStatus(transferStatus(String.valueOf(taskStatus)));
            } else {
                result.setSendStatus(MessageSendStatusEnum.PUSH_FAIL.getCode());
            }
            result.setResponseCode(String.valueOf(statusResponse.getCode()));
            result.setResponseMsg(statusResponse.getMemo());
        }

        return result;
    }


    /**
     * @param taskStatus
     * @return java.lang.String
     * @description: 消息中心的发送状态 转换成 crm-wechat的发送状态
     * @author: jin.wang03
     * @date: 2024/10/30 18:57
     * @since JDK 1.8
     */
    public String transferStatus(String taskStatus) {
        if (CcMessageTaskStatusEnum.PUSHING.getCode().equals(taskStatus)) {
            return MessageSendStatusEnum.PUSHING.getCode();
        } else if (CcMessageTaskStatusEnum.PUSH_SUCCESS.getCode().equals(taskStatus)) {
            return MessageSendStatusEnum.PUSH_SUCCESS.getCode();
        } else if (CcMessageTaskStatusEnum.PUSH_FAIL.getCode().equals(taskStatus)) {
            return MessageSendStatusEnum.PUSH_FAIL.getCode();
        }
        return MessageSendStatusEnum.PUSH_FAIL.getCode();
    }


}