package com.howbuy.crm.wechat.service.outerservice.crm;

import com.howbuy.crm.nt.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.nt.conscust.request.QueryConscustListRequest;
import com.howbuy.crm.nt.conscust.response.QueryConscustListResponse;
import com.howbuy.crm.nt.conscust.service.QueryConscustListService;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: 查询CRM获取客户对应的客户信息
 * @author: yu.zhang
 * @date: 2023/6/19 15:38 
 * @since JDK 1.8
 * @version: 1.0
 */
@Slf4j
@Service
public class QueryConscustListOuterService {

//    @DubboReference(registry = Constants.ZK_CRM_NT, check = false)
    @Autowired
    private QueryConscustListService queryConscustListService;

    public ConscustInfoDomain queryConscustInfo(String hboneNo) {
        //通过一账通获取客户所属投顾信息
        QueryConscustListRequest consreq = new QueryConscustListRequest();
        consreq.setHboneno(hboneNo);
        QueryConscustListResponse consrep = queryConscustListService.queryConscustInfo(consreq);

        List<ConscustInfoDomain> conscustlist = consrep.getConscustlist();

        if (CollectionUtils.isNotEmpty(conscustlist)) {
            return conscustlist.get(0);
        }

        return null;
    }
}
