package com.howbuy.crm.wechat.service.commom.enums;

/**
 * 常用返回参数枚举
 *
 */
public enum BaseReturnCodeEnum {
	
	//执行成功
	SUCCESS("0000", "操作成功"),
	//执行成功
	ACCSUCCESS("0000000", "操作成功"),
	//参数错误
	PARAM_ERROR("0002", "参数错误"),
    
	UNKNOWN_ERROR("9999","系统错误，请联系好买");
	
	
	private String code;	//编码
	
	private String description;  //描述
	
	BaseReturnCodeEnum(String code, String description){
		this.code=code;
		this.description=description;
	}
	
	/**
	 * 通过code获得
	 * @param code	系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code){
		for(BaseReturnCodeEnum b: BaseReturnCodeEnum.values()){
			if(b.getCode().equals(code)){
				return b.description;
			}
		}
		return null;
	}
	
	/**
	 * 通过code直接返回 整个枚举类型
	 * @param code 系统返回参数编码
	 * @return BaseReturnCodeEnum
	 */
	public static BaseReturnCodeEnum getEnum(String code){
		if(code != null && !"".equals(code)){
			for(BaseReturnCodeEnum b: BaseReturnCodeEnum.values()){
				if(b.getCode().equals(code)){
					return b;
				}
			}
		}
		return null;
	}

	public String getCode() {
		return code;
	}


	public String getDescription() {
		return description;
	}

}
