/**
 * Copyright (c) 2023, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.domain.externaluser;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * @description: 外部群成员DTO类
 * <AUTHOR>
 * @date 2023/10/31 15:29
 * @since JDK 1.8
 */
@Data
@Builder
public class ExternalGroupChatUserDTO {

    /**
     * 群成员ID
     */
    private String userId;
    /**
     * 群成员类型
     * 1 - 企业成员
     * 2 - 外部联系人
     */
    private String type;
    /**
     * 外部联系人在微信开放平台的唯一身份标识（微信unionid）
     */
    private String unionId;
    /**
     * 入群时间
     */
    private Date joinTime;
    /**
     * 入群方式。
     * 1 - 由群成员邀请入群（直接邀请入群）
     * 2 - 由群成员邀请入群（通过邀请链接入群）
     * 3 - 通过扫描群二维码入群
     */
    private String joinScene;
    /**
     * 邀请者的userid
     */
    private String invitor;
    /**
     * 群成员在群里的昵称
     */
    private String groupNickname;
    /**
     * 在群里的昵称
     */
    private String name;
}