package com.howbuy.crm.wechat.service.config;

import com.howbuy.crm.wechat.service.commom.utils.UuidTaskDecorator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;
import org.springframework.stereotype.Component;

/**
 * @description: 企业微信nacos对应配置
 * NOTICE :  2025年7月11日 。该 config 不应该暴露给 业务代码 直接使用
 * @author: yu.zhang
 * @date: 2023/6/8 9:49
 * @version: 1.0
 * @since JDK 1.8
 */
@Slf4j
@Component
@Configuration
public class WechatConfig implements InitializingBean {

    @Override
    public void afterPropertiesSet() {
    }


    //    @Bean(name = "uuidTaskDecorator")
    @Bean(name = "uuidTaskDecorator")
    public TaskDecorator uuidTaskDecorator() {
        return new UuidTaskDecorator();
    }

}
