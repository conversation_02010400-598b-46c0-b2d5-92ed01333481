package com.howbuy.crm.wechat.service.commom.enums;

import java.util.stream.Stream;

/**
 * @description: 企业微信对应状态
 * @author: yu.zhang
 * @date: 2023/6/25 14:52 
 * @since JDK 1.8
 * @version: 1.0
 */
public enum WechatStatusEnum {

    ADD("1", "新增"),
    DELETE("2", "删除"),
    DELETE_BY_USER("3", "被客户删除"),
    DELETE_BY_TRANSFER("4", "被删除");

    private final String key;
    private final String desc;

    public static WechatStatusEnum getWechatStatusEnum(String key) {
        return Stream.of(values()).filter(tmp -> tmp.key.equals(key)).findFirst().orElse(null);
    }

    public static String getDesc(String code) {
        WechatStatusEnum radarEnum = getWechatStatusEnum(code);
        return radarEnum == null ? null : radarEnum.getDesc();
    }

    public String getKey() {
        return this.key;
    }

    public String getDesc() {
        return this.desc;
    }

    WechatStatusEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
