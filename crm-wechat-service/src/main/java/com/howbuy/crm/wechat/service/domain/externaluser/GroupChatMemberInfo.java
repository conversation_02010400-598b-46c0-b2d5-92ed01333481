package com.howbuy.crm.wechat.service.domain.externaluser;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @description: 客户群-群成员信息
 * @author: haoran.zhang
 * @create: 2021-08-31 13:56
 **/
@Data
@Accessors(chain = true)
@Builder
public class GroupChatMemberInfo {


    /**
     * 群成员id
     */
    private String userId;
    /**
     *  成员类型
     *  1 - 企业成员
     * 2 - 外部联系人
      */
    private String type;

    /**
     * 外部联系人在微信开放平台的唯一身份标识（微信unionid）
     */
    private String unionid;

    /**
     * 入群时间
      */
    private Date joinTime;

    /**
     * 入群方式。
     * 1 - 由群成员邀请入群（直接邀请入群）
     * 2 - 由群成员邀请入群（通过邀请链接入群）
     * 3 - 通过扫描群二维码入群
     */
    private String joinScene;

    /**
     * 邀请者。目前仅当是由本企业内部成员邀请入群时会返回该值
     * 邀请者的userid
     */
    private String invitorUserId;

    /**
     * 在群里的昵称
     */
    private String groupNickname;

    /**
     * 如果是微信用户，则返回其在微信中设置的名字
     *     如果是企业微信联系人，则返回其设置对外展示的别名或实名
     */
    private String name;

}
