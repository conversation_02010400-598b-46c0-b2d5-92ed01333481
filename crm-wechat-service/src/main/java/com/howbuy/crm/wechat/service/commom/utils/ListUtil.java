/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/10/26 18:02
 * @since JDK 1.8
 */
public class ListUtil {

    public static <T> List<List<T>> splitList(List<T> list, int limit) {
        List<List<T>> lists = new ArrayList<>();
        if (list.isEmpty()) {
            return lists;
        }

        int total = list.size();
        int remain = total % limit;    // 确认是否有余数
        int times = total / limit;        // 要分裂的次数
        int count = (remain == 0 ? times : (times + 1));    // 如果有余数，则多分裂一次
        for (int i = 0; i < count; i++) {
            List<T> appList = list.stream().skip(i * limit).limit(limit).collect(Collectors.toList());
            lists.add(appList);
        }

        return lists;
    }
}