package com.howbuy.crm.wechat.service.outerservice.acccenter;

import com.howbuy.acccenter.facade.query.sensitive.custinfo.QueryCustSensitiveInfoFacade;
import com.howbuy.acccenter.facade.query.sensitive.custinfo.QueryCustSensitiveInfoRequest;
import com.howbuy.acccenter.facade.query.sensitive.custinfo.QueryCustSensitiveInfoResponse;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: 查询账户中心获取敏感信息
 * @author: yu.zhang
 * @date: 2023/6/25 15:45 
 * @since JDK 1.8
 * @version: 1.0
 */
@Slf4j
@Service
public class QueryCustSensitiveInfoOuterService {

//    @DubboReference(registry = Constants.ZK_ACC_CENTER, check = false)
    @Autowired
    private QueryCustSensitiveInfoFacade queryCustSensitiveInfoFacade;

    /**
     * @description:根据一账通号判断是否实名
     * @param hboneNo
     * @return boolean
     * @author: yu.zhang
     * @date: 2023/6/25 15:46
     * @since JDK 1.8
     */
    public boolean getIfRealNameByHboneNo(String hboneNo){
        QueryCustSensitiveInfoRequest queryCustSensitiveInfoRequest = new QueryCustSensitiveInfoRequest();
        queryCustSensitiveInfoRequest.setHboneNo(hboneNo);
        QueryCustSensitiveInfoResponse queryCustSensitiveInfoResponse = queryCustSensitiveInfoFacade.execute(queryCustSensitiveInfoRequest);
        if (queryCustSensitiveInfoResponse != null && queryCustSensitiveInfoResponse.getCustSensitiveInfo() != null) {
            return StringUtils.isNotEmpty(queryCustSensitiveInfoResponse.getCustSensitiveInfo().getIdNo());
        }
        return false;
    }
}
