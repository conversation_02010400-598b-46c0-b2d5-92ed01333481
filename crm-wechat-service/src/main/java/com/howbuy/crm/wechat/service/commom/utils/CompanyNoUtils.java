/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.utils;

import com.howbuy.crm.wechat.service.commom.constant.Constants;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025-08-14
 */
public final class CompanyNoUtils {

    private CompanyNoUtils() {
    }

    /**
     * 如果传入的 companyNo 为空或空字符串，则返回默认的企业编号。
     *
     * @param companyNo 原始企业编号
     * @return 有效的企业编号
     */
    public static String getOrDefault(String companyNo) {
        if (StringUtils.isEmpty(companyNo)) {
            return Constants.DEFAULT_COMPANY_NO;
        }
        return companyNo;
    }
}
