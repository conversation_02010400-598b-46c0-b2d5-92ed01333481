/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.job;

import com.howbuy.crm.wechat.service.commom.utils.LoggerUtils;
import com.howbuy.crm.wechat.service.service.syncchatgroupuser.SyncChatGroupUserService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: 同步所有企微客户群的成员信息定时任务
 * <AUTHOR>
 * @date 2023/10/30 14:29
 * @since JDK 1.8
 */

@Slf4j
@Component
public class SyncChatGroupUserJob extends AbstractBatchMessageJob {
    /**
     * 调度消息队列，nacos中需要配置sync.SYNC_CHAT_GROUP_USER_JOB对应的队列名称
     */
    @Value("${sync_chat_cust_group_user_channel:TOPIC_SYNC_CHAT_CUST_GROUP_USER_JOB}")
    private String queue;

    @Autowired
    private SyncChatGroupUserService syncChatGroupUserService;

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("SyncChatGroupUserJob process start");
        LoggerUtils.setUUID();
        try {
            syncChatGroupUserService.syncChatGroupUserData(getContent(message));
        } catch (Exception e) {
            log.error("error in SyncChatGroupUserJob", e);
        } finally {
            LoggerUtils.clearConfig();
        }
        log.info("SyncChatGroupUserJob process end");
    }

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }
}