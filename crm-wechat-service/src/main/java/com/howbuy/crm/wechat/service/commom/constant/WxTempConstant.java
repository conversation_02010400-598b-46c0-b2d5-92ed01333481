package com.howbuy.crm.wechat.service.commom.constant;

import java.io.Serializable;

/**
 * @classname: WxTempConstant
 * @author: yu.zhang
 * @description: 企业微信模板
 * @creatdate: 2021-01-20 11:29
 * @since: JDK1.8
 */
public class WxTempConstant implements Serializable {

    /**
     * 事件类型：https://open.work.weixin.qq.com/api/doc/90000/90135/92130 中所有格式
     */
    public static final String REQ_MESSAGE_TYPE_EVENT = "event";
    /**
     * 事件类型：变更客户信息
     */
    public static final String EVENT_TYPE_EXTERNAL = "change_external_contact";
    /**
     * 事件类型：新增客户
     */
    public static final String EVENT_TYPE_EXTERNAL_ADD = "add_external_contact";
    /**
     * 事件类型：新增外部客户
     */
    public static final String EVENT_TYPE_EXTERNAL_ADDHALF = "add_half_external_contact";
    /**
     * 客户同意进行聊天内容存档事件回调
     */
    public static final String EVENT_TYPE_MSG_AUDIT_APPROVED = "msg_audit_approved";
    /**
     * 事件类型：删除客户
     */
    public static final String EVENT_TYPE_EXTERNAL_DEL = "del_external_contact";

    public static final String DELETE_BY_TRANSFER = "DELETE_BY_TRANSFER";
    /**
     * 事件类型：删除外部联系人
     */
    public static final String EVENT_TYPE_EXTERNAL_DELFLL = "del_follow_user";
    /**
     * 通讯录变更回调事件类型
     */
    public static final String EVENT_TYPE_CONTACT = "change_contact";

    /**
     * 客户群回调事件
     */
    public static final String EVENT_TYPE_EXTERNAL_CHAT = "change_external_chat";
    /**
     * 通讯录变更类型-新增成员
     */
    public static final String CHANGE_TYPE_CREATE_USER = "create_user";
    /**
     * 通讯录变更类型-更新成员
     */
    public static final String CHANGE_TYPE_UPDATE_USER = "update_user";
    /**
     * 通讯录变更类型-删除成员
     */
    public static final String CHANGE_TYPE_DELETE_USER = "delete_user";
    /**
     * 通讯录变更类型-新增部门
     */
    public static final String CHANGE_TYPE_CREATE_PARTY = "create_party";
    /**
     * 通讯录变更类型-更新部门
     */
    public static final String CHANGE_TYPE_UPDATE_PARTY = "update_party";
    /**
     * 通讯录变更类型-删除部门
     */
    public static final String CHANGE_TYPE_DELETE_PARTY = "delete_party";

    /**
     * 发送消息类型：文本
     */
    public static final String TEMP_TEXT_TEXT = "text";
    /**
     * 发送消息类型：文本卡片
     */
    public static final String TEMP_TEXT_CARD = "textcard";
    /**
     * 发送消息类型：图文消息
     */
    public static final String TEMP_TEXT_NEWS = "news";
    /**
     * 拼装用户组消息
     */
    public static final String TEMP_GROUPCHAT_TEXT = "groupchat";
    /**
     * 拼装用户组详情数据
     */
    public static final String TEMP_GROUPCHAT_CUST = "groupcust";



    /**
     * 群变更类型-客户群创建事件
     */
    public static final String CHAT_CHANGE_TYPE_CREATE = "create";
    /**
     * 群变更类型-客户群变更事件
     */
    public static final String CHAT_CHANGE_TYPE_UPDATE = "update";
    /**
     * 群变更类型-客户群解散事件
     */
    public static final String CHAT_CHANGE_TYPE_DISMISS = "dismiss";
    /**
     * 群变更类型-客户群变更事件-变更详情-成员入群
     */
    public static final String CHAT_CHANGE_TYPE_UPDATEDETAIL_ADD_MEMBER = "add_member";
    /**
     * 群变更类型-客户群变更事件-变更详情-成员退群
     */
    public static final String CHAT_CHANGE_TYPE_UPDATEDETAIL_DEL_MEMBER = "del_member";
    /**
     * 群变更类型-客户群变更事件-变更详情-群主变更
     */
    public static final String CHAT_CHANGE_TYPE_UPDATEDETAIL_CHANGE_OWNER   = "change_owner  ";
    /**
     * 群变更类型-客户群变更事件-变更详情-群名变更
     */
    public static final String CHAT_CHANGE_TYPE_UPDATEDETAIL_CHANGE_NAME  = "change_name ";
    /**
     * 群变更类型-客户群变更事件-变更详情-群公告变更
     */
    public static final String CHAT_CHANGE_TYPE_UPDATEDETAIL_CHANGE_NOTICE  = "change_notice ";

}
