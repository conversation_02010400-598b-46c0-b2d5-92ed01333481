/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.enums;

/**
 * <AUTHOR>
 * @description: 好买产品线枚举类
 * @date 2024/3/18 16:31
 * @since JDK 1.8
 */

public enum HbProductLineEnum {

    /**
     * 现金管理工具
     */
    CASH_MANAGEMENT_TOOLS("0", "现金管理工具"),
    /**
     * 债券型
     */
    BOND_TYPE("1", "债券型"),
    /**
     * 类固定收益
     */
    DIVISION("2", "类固定收益"),
    /**
     * 对冲
     */
    CLASS_FIXED_INCOME("3", "对冲"),
    /**
     * 股票型
     */
    STOCK_TYPE("4", "股票型"),
    /**
     * PE、VC
     */
    PE_VC("5", "PE、VC"),
    /**
     * 房地产基金
     */
    REAL_ESTATE_FUNDS("6", "房地产基金"),
    /**
     * 其他
     */
    OTHER("7", "其他"),
    /**
     * 海外
     */
    OVERSEAS("8", "海外"),
    ;

    private String code; // 编码

    private String description; // 描述

    HbProductLineEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 通过code获得
     *
     * @param code 系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code) {
        HbProductLineEnum statusEnum = getEnum(code);
        return statusEnum == null ? null : statusEnum.getDescription();
    }

    /**
     * 通过code直接返回 整个枚举类型
     *
     * @param code 系统返回参数编码
     * @return PreOccupyTypeEnum
     */
    public static HbProductLineEnum getEnum(String code) {
        for (HbProductLineEnum statusEnum : HbProductLineEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

}
