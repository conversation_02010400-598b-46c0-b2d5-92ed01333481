package com.howbuy.crm.wechat.service.repository;

import com.howbuy.crm.wechat.dao.mapper.CmWechatDeptChangeHisMapper;
import com.howbuy.crm.wechat.dao.mapper.CmWechatDeptMapper;
import com.howbuy.crm.wechat.dao.mapper.customize.CustomizeCmWechatDeptMapper;
import com.howbuy.crm.wechat.dao.po.CmWechatDeptChangeHisPO;
import com.howbuy.crm.wechat.dao.po.CmWechatDeptPO;
import com.howbuy.crm.wechat.service.commom.enums.ValidEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: crm-wechat 
 * @author: yu.zhang
 * @date: 2023/6/12 15:51 
 * @since JDK 1.8
 * @version: 1.0
 */
@Repository
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class CmWechatDeptRepository {

    @Autowired
    private CmWechatDeptMapper cmWechatDeptMapper;
    @Autowired
    private CmWechatDeptChangeHisMapper cmWechatDeptChangeHisMapper;
    @Autowired
    private CustomizeCmWechatDeptMapper customizeCmWechatDeptMapper;
    @Autowired
    private CommonRepository commonRepository;

    /**
     * @description:根据部门ID查询部门列表
     * @param deptId
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatDeptPO>
     * @author: yu.zhang
     * @date: 2023/6/12 17:43
     * @since JDK 1.8
     */
    public List<CmWechatDeptPO> listWechatDeptByDeptId(Integer deptId, String companyNo) {
        return customizeCmWechatDeptMapper.listWechatDeptByDeptId(deptId, companyNo);
    }

    /**
     * @description:新增企业微信部门
     * @param wechatDept
     * @return int
     * @author: yu.zhang
     * @date: 2023/6/12 16:01
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int insertWechatDept(CmWechatDeptPO wechatDept) {
        wechatDept.setId(commonRepository.getWechatDeptIdBySeq());
        wechatDept.setDelFlag(ValidEnum.VALID.getKey());
        wechatDept.setCreateTime(new Date());
        return cmWechatDeptMapper.insertSelective(wechatDept);
    }

    /**
     * @description:部门变更记录
     * @param wechatDept
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/12 17:39
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateWechatDeptByDeptId(CmWechatDeptPO wechatDept) {

        List<CmWechatDeptPO> wechatDeptList = this.listWechatDeptByDeptId(wechatDept.getDeptId(), wechatDept.getCompanyNo());
        if (CollectionUtils.isNotEmpty(wechatDeptList)) {
            wechatDeptList.forEach(cmWechatDept -> {

                // 插入部门修改记录
                if (StringUtils.isNotBlank(wechatDept.getDeptName())) {
                    CmWechatDeptChangeHisPO wechatDeptChangeHis = new CmWechatDeptChangeHisPO();
                    wechatDeptChangeHis.setId(commonRepository.getWechatDeptIdBySeq());
                    wechatDeptChangeHis.setDeptId(cmWechatDept.getDeptId());
                    wechatDeptChangeHis.setBeforeDeptName(cmWechatDept.getDeptName());
                    wechatDeptChangeHis.setAfterDeptName(wechatDept.getDeptName());
                    wechatDeptChangeHis.setChangeTime(new Date());

                    cmWechatDeptChangeHisMapper.insertSelective(wechatDeptChangeHis);
                }

                cmWechatDept.setParentDeptId(wechatDept.getParentDeptId());
                cmWechatDept.setDeptName(wechatDept.getDeptName());
                this.updateWechatDept(cmWechatDept);
            });
        }
    }

    /**
     * @description:部门变更记录
     * @param wechatDept
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/12 17:39
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int updateWechatDept(CmWechatDeptPO wechatDept) {
        wechatDept.setUpdateTime(new Date());
        return cmWechatDeptMapper.updateByPrimaryKeySelective(wechatDept);
    }

    /**
     * @description:逻辑删除部门
     * @param deptId
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/12 17:07
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void delWechatDeptByDeptId(Integer deptId, String companyNo) {

        List<CmWechatDeptPO> wechatDeptList = this.listWechatDeptByDeptId(deptId, companyNo);
        if (CollectionUtils.isNotEmpty(wechatDeptList)) {
            wechatDeptList.forEach(cmWechatDept -> {
                cmWechatDept.setDelTime(new Date());
                cmWechatDept.setDelFlag(ValidEnum.INVALID.getKey());
                cmWechatDeptMapper.updateByPrimaryKey(cmWechatDept);
            });
        }
    }

    /**
     * @description:批量新增
     * @param wechatDeptList
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/26 18:09
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void batchInsertWechatDept(List<CmWechatDeptPO> wechatDeptList) {
        if (CollectionUtils.isNotEmpty(wechatDeptList)) {
            wechatDeptList.forEach(this::insertWechatDept);
        }
    }

    /**
     * @description:批量将企业微信返回的部门列表数据与表中现有数据进行merge
     * @param wechatDeptList
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/27 9:16
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int batchMergeWechatDept(List<CmWechatDeptPO> wechatDeptList) {
        int affectCount = 0;
        if(CollectionUtils.isEmpty(wechatDeptList)){
            return affectCount;
        }
        //Crocodile's TODO : 整个同步流程都有逻辑问题。
        for (CmWechatDeptPO wechatDept : wechatDeptList) {
            List<CmWechatDeptPO> cmWeChatDeptList = this.listWechatDeptByDeptId(wechatDept.getDeptId(), wechatDept.getCompanyNo());
            if (CollectionUtils.isNotEmpty(cmWeChatDeptList)) {
                for (CmWechatDeptPO cmWechatDept : cmWeChatDeptList) {
                    if (Boolean.TRUE.equals(needUpdate(cmWechatDept, wechatDept))) {
                        cmWechatDept.setDeptName(wechatDept.getDeptName());
                        cmWechatDept.setParentDeptId(wechatDept.getParentDeptId());
                        affectCount += this.updateWechatDept(cmWechatDept);
                    }

                    if (!Objects.equals(ValidEnum.VALID.getKey(), cmWechatDept.getDelFlag())) {
                        affectCount += this.insertWechatDept(wechatDept);
                    }

                }
            } else {
                affectCount += this.insertWechatDept(wechatDept);
            }
        }
        return affectCount;
    }

    /**
     * @description:比对数据判断是否需要修改
     * @param cmWechatDept 数据库中查询的数据
     * @param wechatDept 准确的数据
     * @return java.lang.Boolean
     * @author: yu.zhang
     * @date: 2023/6/29 11:23
     * @since JDK 1.8
     */
    private Boolean needUpdate(CmWechatDeptPO cmWechatDept, CmWechatDeptPO wechatDept) {
        return Objects.equals(ValidEnum.VALID.getKey(), cmWechatDept.getDelFlag())
                && (!Objects.equals(cmWechatDept.getDeptName(), wechatDept.getDeptName())
                || !Objects.equals(cmWechatDept.getParentDeptId(), wechatDept.getParentDeptId()));
    }

    /**
     * @description:逻辑删除对应的部门数据
     * @param companyNo	企业编码
     * @param fullDeptList 全量微信最新 部门数据
     * @return int
     * @author: yu.zhang
     * @date: 2023/6/27 9:08
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int removeStaleWechatDept(String companyNo, List<CmWechatDeptPO> fullDeptList) {
        int affectCount = 0;
        //Crocodile's TODO : 历史实现逻辑有问题。
        //猜测为了同时同步companyNo =A && B  .导致此处 nonmatch的处理。  带来隐患：  companyNo=A中会有无法删除的部门数据！！！
        //暂不修改。标记：待修改
        List<CmWechatDeptPO> cmWechatDept = customizeCmWechatDeptMapper.listAllWechatDept(companyNo);
        List<CmWechatDeptPO> removeWechat = cmWechatDept.stream().filter(remove -> fullDeptList.stream()
                .noneMatch(all -> Objects.equals(remove.getDeptId(), all.getDeptId()) && Objects.equals(remove.getCompanyNo(), all.getCompanyNo())))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(removeWechat)) {
            for (CmWechatDeptPO wechatDept : removeWechat) {
                wechatDept.setDelTime(new Date());
                wechatDept.setDelFlag(ValidEnum.INVALID.getKey());
                affectCount += cmWechatDeptMapper.updateByPrimaryKey(wechatDept);
            }
        }
        return affectCount;
    }

}
