package com.howbuy.crm.wechat.service.repository;


import com.howbuy.crm.wechat.dao.mapper.CommonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * @description: 查询序列
 * @author: yu.zhang
 * @date: 2023/3/23 17:43
 * @version: 1.0
 * @since JDK 1.8
 */
@Repository
public class CommonRepository {

    @Autowired
    private CommonMapper commonMapper;

    /**
     * 企业微信部门表ID
     */
    private static final String SEQ_WECHAT_DEPT_ID = "SEQ_WECHAT_DEPT_ID";
    /**
     * 企业微信员工表ID
     */
    private static final String SEQ_WECHAT_EMP_ID = "SEQ_WECHAT_EMP_ID";
    /**
     * 企业微信外部联系人相关操作ID
     */
    private static final String SEQ_WECHAT_EXTERNAL_ID = "SEQ_WECHAT_EXTERNAL_ID";
    /**
     * 企业微信外部联系人ID
     */
    private static final String SEQ_WECHAT_CUST_ID = "SEQ_WECHAT_CUST_ID";

    /**
     * 企业微信外部联系人ID
     */
    private static final String SEQ_WECHAT_ID = "SEQ_WECHAT_ID";

    /**
     * @description:获取企业微信部门表ID
     * @param
     * @return java.lang.Long
     * @author: yu.zhang
     * @date: 2023/6/12 16:20
     * @since JDK 1.8
     */
    public Long getWechatDeptIdBySeq(){
        return commonMapper.getSeqValue(SEQ_WECHAT_DEPT_ID);
    }

    /**
     * @description:获取企业微信员工表ID
     * @param
     * @return java.lang.Long
     * @author: yu.zhang
     * @date: 2023/6/12 16:28
     * @since JDK 1.8
     */
    public Long getWechatEmpIdBySeq(){
        return commonMapper.getSeqValue(SEQ_WECHAT_EMP_ID);
    }

    /**
     * @description:获取企业微信外部联系人操作表ID
     * @param
     * @return java.lang.Long
     * @author: yu.zhang
     * @date: 2023/6/12 16:20
     * @since JDK 1.8
     */
    public Long getWechatExternalIdBySeq(){
        return commonMapper.getSeqValue(SEQ_WECHAT_EXTERNAL_ID);
    }

    /**
     * @description:获取企业微信外部联系人操作表ID
     * @param
     * @return java.lang.Long
     * @author: yu.zhang
     * @date: 2023/6/12 16:20
     * @since JDK 1.8
     */
    public Long getWechatCustIdBySeq(){
        return commonMapper.getSeqValue(SEQ_WECHAT_CUST_ID);
    }

    /**
     * @description:获取企业微信ID
     * @param
     * @return java.lang.Long
     * @author: yu.zhang
     * @date: 2023/6/12 16:20
     * @since JDK 1.8
     */
    public Long getWechatIdBySeq(){
        return commonMapper.getSeqValue(SEQ_WECHAT_ID);
    }
}
