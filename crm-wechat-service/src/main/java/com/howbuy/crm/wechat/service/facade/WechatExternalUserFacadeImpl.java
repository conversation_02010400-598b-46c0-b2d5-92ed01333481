/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.facade;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.externaluser.GetExternalUserRequest;
import com.howbuy.crm.wechat.client.domain.response.externaluser.GetExternalUserVO;
import com.howbuy.crm.wechat.client.facade.externaluser.WechatExternalUserFacade;
import com.howbuy.crm.wechat.service.service.externaluser.WechatExternalUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 企微外部联系人Dubbo接口实现
 * @date 2025-08-19 19:27:20
 * @since JDK 1.8
 */
@DubboService
@Slf4j
public class WechatExternalUserFacadeImpl implements WechatExternalUserFacade {

    @Resource
    private WechatExternalUserService wechatExternalUserService;

    @Override
    public Response<GetExternalUserVO> getExternalUser(GetExternalUserRequest request) {
        return Response.ok(wechatExternalUserService.getExternalUser(request));
    }
}