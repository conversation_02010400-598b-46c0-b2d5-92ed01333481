/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.outerservice.messagecenter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.google.common.collect.Lists;
import com.howbuy.cc.message.SendMsgResult;
import com.howbuy.cc.message.email.SendEmailByTemplateRequest;
import com.howbuy.cc.message.email.SendEmailResponse;
import com.howbuy.cc.message.email.SendEmailWithAttachSupportEncryRequest;
import com.howbuy.cc.message.phone.SendShortMsgByPhoneResponse;
import com.howbuy.cc.message.phone.SendShortMsgByTemplateRequest;
import com.howbuy.cc.message.request.SendAppMessageByTemplateRequest;
import com.howbuy.cc.message.send.auto.request.SendMsgByHboneNoRequest;
import com.howbuy.cc.message.send.auto.response.SendMsgByHboneNoResponse;
import com.howbuy.cc.message.send.company.SelfAppMessageRequest;
import com.howbuy.cc.message.send.company.SendBotMessageRequest;
import com.howbuy.cc.message.send.company.bot.model.Article;
import com.howbuy.cc.message.send.company.bot.model.NewsBotMessage;
import com.howbuy.cc.message.send.company.bot.model.TextBotMessage;
import com.howbuy.cc.message.send.single.model.Attachment;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.wechat.client.enums.MessageSendStatusEnum;
import com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO;
import com.howbuy.crm.wechat.service.business.CompanySendBusiness;
import com.howbuy.crm.wechat.service.commom.constant.DfileConstants;
import com.howbuy.crm.wechat.service.commom.utils.FileUtil;
import com.howbuy.crm.wechat.service.domain.message.SendResultDTO;
import com.howbuy.crm.wechat.service.domain.message.vo.*;
import com.howbuy.dfile.HFileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;

/**
 * @description: (企业微信发送消息)
 * <AUTHOR>
 * @date 2023/10/10 14:26
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CompanySendOuterService {

    @Autowired
    private CompanySendBusiness companySendBusiness;

    /**
     * 外部接口  状态码 0-成功
     */
    private static  final Integer OUTER_SUCCESS_CODE=0;
    /**
     * 文件存储路径 与 storeConfig的relativeDir一致
     */
    public static final String STORE_CONFIG_PATH = "/data/files/crmwechat/goodnews/";

    public static void main(String[] args) {
        String a = "/data/files/crmwechat/goodnews//20240618/885_162500.png";
        String filePath = a.substring(a.indexOf(STORE_CONFIG_PATH)+STORE_CONFIG_PATH.length(),a.lastIndexOf("/"));
        String fileName = a.substring(a.lastIndexOf(File.separator)+1,a.length());
        System.out.println(filePath);
        System.out.println(fileName);
    }
    /**
     * 中台消息中心-外部接口 0000-成功 状态码
     */
    private static final String ZT_MESSAGE_SUCCESS_CODE = "0000";


    /**
     *  企微群机器发送消息
     * @param botMessageVo
     */
    public SendResultDTO sendBotMessage(BotMessageVo botMessageVo) throws Exception {
        SendBotMessageRequest request = new SendBotMessageRequest();
        request.setMessageType(botMessageVo.getMessageType());
        request.setWebHookUrlKey(botMessageVo.getWebHookUrlKey());

        //fileData  转换
        if (StringUtil.isNotBlank(botMessageVo.getFileName())) {
            HFileService instance = HFileService.getInstance();
            //这里的后两个参数应该和放入文件的时候一样
            byte[] fileData = instance.read2Bytes(DfileConstants.MARKET_REPORT_CONFIG, botMessageVo.getRelativePath(), botMessageVo.getFileName());
            log.info("CompanySendOuterService|sendBotMessage|filePath:{},fileData:{}", botMessageVo.getFilePath(), fileData.length);
            request.setFileData(fileData); //如果filePath为空，fileData也为空
        }

        //文本类 转换
        TextBotMessageVo textVo = botMessageVo.getTextBotMessage();
        if (textVo != null) {
            TextBotMessage textBotMessage = new TextBotMessage();
            textBotMessage.setContent(textVo.getContent());
            textBotMessage.setMentioned_list(textVo.getMentionedList());
            textBotMessage.setMentioned_mobile_list(textVo.getMentionedMobileList());
            request.setTextBotMessage(textBotMessage);
        }
        //图文消息  转换
        NewsBotMessageVo newsVo = botMessageVo.getNewsBotMessage();
        if (newsVo != null) {
            NewsBotMessage newsBotMessage = new NewsBotMessage();
            if (CollectionUtils.isNotEmpty(newsVo.getArticles())) {
                newsBotMessage.setArticles(Lists.newArrayList());
                newsVo.getArticles().forEach(articleVo -> {
                    Article article = new Article();
                    article.setTitle(articleVo.getTitle());
                    article.setDescription(articleVo.getDescription());
                    article.setUrl(articleVo.getUrl());
                    article.setPicurl(articleVo.getPicurl());
                    newsBotMessage.getArticles().add(article);
                });
            }
            request.setNewsBotMessage(newsBotMessage);
        }

        SendResultDTO result = new SendResultDTO();
        try {
            SendMsgResult outerResult = companySendBusiness.sendBotMessage(request);
            //toJSONString 排除属性 fileData
            String requestLog = JSON.toJSONString(request, (PropertyFilter) (object, name, value) -> !"fileData".equals(name));

            log.info("企微群机器发送消息,request:{}, 结果:{}", requestLog, JSON.toJSONString(outerResult));
            if (outerResult != null) {
                result.setCode(String.valueOf(outerResult.getCode()));
                result.setMsg(outerResult.getMsg());
                result.setMessageUUID(outerResult.getMessageUUID());
                result.setSuccess(Objects.equals(OUTER_SUCCESS_CODE, outerResult.getCode()));
            }
        } catch (Exception e) {
            log.error("企微群机器发送消息异常", e);
            result.setSuccess(false);
        }
        return result;
    }

     /**
      * @description 自建应用发送企微消息
      * @param sendInfo
      * @return
      * <AUTHOR>
      * @date 2023/11/29 1:39 PM
      * @since JDK 1.8
      */
    public SendResultDTO newApplicationSendMsg(MessageSendInfoPO sendInfo) {
        log.info("自建应用发送企微消息,req={}", sendInfo);
        SendResultDTO result = new SendResultDTO();
        if (StringUtils.isEmpty(sendInfo.getTempleteParams())) {
            log.info("【自建应用发送企微】消息体为空，不做处理");
            result.setMsg("消息体为空，不做处理");
            return result;
        }
        NewApplicationMessageVO messageVO = JSON.parseObject(sendInfo.getTempleteParams(), NewApplicationMessageVO.class);
        SelfAppMessageRequest request = new SelfAppMessageRequest();
        request.setTouser(messageVO.getTouser());
        request.setAgentId(messageVO.getAgentId());
        request.setMessageType(messageVO.getMessageType());
        request.setContent(messageVO.getContent());
        request.setCompany(messageVO.getCompany());
        request.setTemplateCardModel(messageVO.getTemplateCardModel());
        SendMsgResult sendMsgResult = companySendBusiness.sendNewApplicationMsg(request);

        log.info("自建应用发送企微消息,request:{}, 结果:{}", JSON.toJSONString(messageVO), JSON.toJSONString(sendMsgResult));

        if(sendMsgResult != null){
            result.setCode(String.valueOf(sendMsgResult.getCode()));
            result.setMsg(sendMsgResult.getMsg());
            result.setMessageUUID(sendMsgResult.getMessageUUID());
            result.setSuccess(Objects.equals(OUTER_SUCCESS_CODE, sendMsgResult.getCode()));
            //NOTICE: CallOuterSystemResult callOuterSystemResult   为cc接口新增返回。此处暂未适配接入
        }
        return result;
    }


    /**
     * @description: 发送邮件
     * @param sendInfo
     * @return com.howbuy.crm.wechat.service.domain.message.SendResultDTO
     * @author: jin.wang03
     * @date: 2024/5/24 19:01
     * @since JDK 1.8
     */
    public SendResultDTO sendEmail(MessageSendInfoPO sendInfo) {
        log.info("自建应用发送企微消息,req={}", sendInfo);
        SendResultDTO result = new SendResultDTO();
        if (StringUtils.isEmpty(sendInfo.getTempleteParams())) {
            log.info("【自建应用发送企微】消息体为空，不做处理");
            result.setMsg("消息体为空，不做处理");
            return result;
        }
        EmailMessageVo messageVO = JSON.parseObject(sendInfo.getTempleteParams(), EmailMessageVo.class);

        SendEmailByTemplateRequest sendEmailRequest = new SendEmailByTemplateRequest();
        sendEmailRequest.setBusinessId(sendInfo.getTempleteId());

        sendEmailRequest.setEncryEmail(messageVO.getEncryEmail());
        sendEmailRequest.setTemplate(messageVO.getTemplateParams());
        sendEmailRequest.setTitle(messageVO.getEmailTitle());

        SendEmailResponse sendMsgResult = companySendBusiness.sendEmailMsg(sendEmailRequest);

        log.info("发送邮件,request:{}, 结果:{}", JSON.toJSONString(sendEmailRequest), JSON.toJSONString(sendMsgResult));
        if (sendMsgResult != null) {
            result.setCode(String.valueOf(sendMsgResult.getReturnCode()));
            result.setMsg(sendMsgResult.getDescription());
            result.setMessageUUID(sendMsgResult.getUuid());
            result.setSuccess(Objects.equals(ZT_MESSAGE_SUCCESS_CODE, sendMsgResult.getReturnCode()));
        }
        return result;
    }


    /**
     * @description: 发送带附件的邮件
     * @param sendInfo
     * @return com.howbuy.crm.wechat.service.domain.message.SendResultDTO
     * @author: jin.wang03
     * @date: 2024/10/28 15:55
     * @since JDK 1.8
     */
    public SendResultDTO endEmailWithAttachment(MessageSendInfoPO sendInfo) throws Exception {
        log.info("发送带附件的邮件,req={}", sendInfo);
        SendResultDTO result = new SendResultDTO();
        if (StringUtils.isEmpty(sendInfo.getTempleteParams())) {
            log.info("【发送带附件的邮件】消息体为空，不做处理");
            result.setMsg("消息体为空，不做处理");
            return result;
        }
        EmailWithAttachmentMessageVo messageVO = JSON.parseObject(sendInfo.getTempleteParams(), EmailWithAttachmentMessageVo.class);

        SendEmailWithAttachSupportEncryRequest sendEmailRequest = new SendEmailWithAttachSupportEncryRequest();
        sendEmailRequest.setBusinessId(sendInfo.getTempleteId());

        sendEmailRequest.setEncryEmail(messageVO.getEncryEmail());
        sendEmailRequest.setTemplate(messageVO.getTemplateParams());
        sendEmailRequest.setTitle(messageVO.getEmailTitle());

        // 发送附件
        List<Attachment> sendAttachmentList = new ArrayList<>();
        for (EmailAttachmentVO emailAttachmentVO : messageVO.getAttachmentList()) {
            Attachment attachment = new Attachment();
            // 获取附件字节流
            attachment.setFile(FileUtil.getBytesFromUrl(emailAttachmentVO.getHttpUrl()));
            // 附件展示名
            attachment.setDisplayFileName(emailAttachmentVO.getDisplayFileName());
            // 附件格式
            attachment.setFileFormat(emailAttachmentVO.getFileFormat());
            sendAttachmentList.add(attachment);
        }
        sendEmailRequest.setAttachmentList(sendAttachmentList);

        SendEmailResponse sendMsgResult = companySendBusiness.sendEmailWithAttachmentMsg(sendEmailRequest);

        log.info("发送带附件的邮件,request:{}, 结果:{}", JSON.toJSONString(messageVO), JSON.toJSONString(sendMsgResult));
        if (sendMsgResult != null) {
            if (Objects.equals(ZT_MESSAGE_SUCCESS_CODE, sendMsgResult.getReturnCode())) {
                result.setSendStatus(MessageSendStatusEnum.PUSHING.getCode());
            } else {
                result.setSendStatus(MessageSendStatusEnum.PUSH_FAIL.getCode());
            }
            result.setCode(String.valueOf(sendMsgResult.getReturnCode()));
            result.setMsg(sendMsgResult.getDescription());
            result.setMessageUUID(sendMsgResult.getUuid());
        }
        return result;
    }


    /**
     * @description: 发送短信
     * @param sendInfo
     * @return com.howbuy.crm.wechat.service.domain.message.SendResultDTO
     * @author: jin.wang03
     * @date: 2024/5/24 19:01
     * @since JDK 1.8
     */
    public SendResultDTO sendSms(MessageSendInfoPO sendInfo) {
        log.info("自建应用发送企微消息,req={}", sendInfo);
        SendResultDTO result = new SendResultDTO();
        if (StringUtils.isEmpty(sendInfo.getTempleteParams())) {
            log.info("【自建应用发送企微】消息体为空，不做处理");
            result.setMsg("消息体为空，不做处理");
            return result;
        }

        SmsMessageVo messageVO = JSON.parseObject(sendInfo.getTempleteParams(), SmsMessageVo.class);

        SendShortMsgByTemplateRequest sendMsgRequest = new SendShortMsgByTemplateRequest();
        sendMsgRequest.setBusinessId(sendInfo.getTempleteId());

        sendMsgRequest.setEncryPhone(messageVO.getEncryPhone());
        sendMsgRequest.setTemplate(messageVO.getTemplateParams());

        SendShortMsgByPhoneResponse sendMsgResult = companySendBusiness.sendSmsMsg(sendMsgRequest);

        log.info("发送短信,request:{}, 结果:{}", JSON.toJSONString(sendMsgRequest), JSON.toJSONString(sendMsgResult));
        if (sendMsgResult != null) {
            result.setCode(String.valueOf(sendMsgResult.getReturnCode()));
            result.setMsg(sendMsgResult.getDescription());
           //  result.setMessageUUID(sendMsgResult.get);
            result.setSuccess(Objects.equals(ZT_MESSAGE_SUCCESS_CODE, sendMsgResult.getReturnCode()));
        }
        return result;
    }


    /**
     * @description:(测试文件上传)
     * @param messageStr
     * @return void
     * @author: shuai.zhang
     * @date: 2024/6/20 11:14
     * @since JDK 1.8
     */
    public void testDfile(String messageStr) throws Exception {
        BotMessageVo botMessageVo= JSON.parseObject(messageStr,BotMessageVo.class);
        //fileData  转换
        String filePath= botMessageVo.getFilePath();
        if(StringUtil.isNotBlank(filePath)){
            //byte[] fileData= FileUtil.getContent(filePath);
            String sPath = filePath.substring(filePath.indexOf(STORE_CONFIG_PATH)+STORE_CONFIG_PATH.length(),filePath.lastIndexOf(File.separator));
            String sName = filePath.substring(filePath.lastIndexOf(File.separator)+1,filePath.length());
            log.info("CompanySendOuterService|testDfile|sPath:{},sName:{}",sPath,sName);
            HFileService instance = HFileService.getInstance();
            //这里的后两个参数应该和放入文件的时候一样
            byte[] fileData = instance.read2Bytes(DfileConstants.MARKET_REPORT_CONFIG, botMessageVo.getRelativePath(), botMessageVo.getFileName());
            log.info("CompanySendOuterService|testDfile|filePath:{},fileData:{}",filePath,fileData.length);

            // IC 和 HBC的海报模版不同，需要区分
            String imgName = "ic_market_report.png";
            //File imageFile = FileUtil.getFile(String.join(File.separator,getTemplateImgPath(), imgName));
            //这里的后两个参数应该和放入文件的时候一样
            byte[] fileData1 = instance.read2Bytes(DfileConstants.MARKET_REPORT_TEMPLATE_CONFIG, "", imgName);
            log.info("testDfile|getImgStream:fileData:{}", fileData1.length);
        }
    }

    /**
     * @description:(请在此添加描述)
     * @param sendInfo
     * @return com.howbuy.crm.wechat.service.domain.message.SendResultDTO
     * @author: jin.wang03
     * @date: 2025/6/19 15:55
     * @since JDK 1.8
     */
    public SendResultDTO sendHbApp(MessageSendInfoPO sendInfo) {
        log.info("在好买基金APP中给客户推送消息,req={}", sendInfo);
        SendResultDTO result = new SendResultDTO();
        if (StringUtils.isEmpty(sendInfo.getTempleteParams())) {
            log.info("【在好买基金APP中给客户推送消息】消息体为空，不做处理");
            result.setMsg("消息体为空，不做处理");
            return result;
        }

        HbAppMessageVo messageVO = JSON.parseObject(sendInfo.getTempleteParams(), HbAppMessageVo.class);

        SendAppMessageByTemplateRequest sendMsgRequest = new SendAppMessageByTemplateRequest();
        sendMsgRequest.setBusinessId(sendInfo.getTempleteId());

        sendMsgRequest.setCustNo(messageVO.getHboneNo());
        // 固定写死1，暂不清楚具体含义
        sendMsgRequest.setCustType(1);
        sendMsgRequest.setParamsJson(messageVO.getTemplateParams());

        SendMsgResult sendMsgResult = companySendBusiness.sendHbApp(sendMsgRequest);

        log.info("在好买基金APP中给客户推送消息,request:{}, 结果:{}", JSON.toJSONString(sendMsgRequest), JSON.toJSONString(sendMsgResult));
        if (sendMsgResult != null) {
            result.setCode(String.valueOf(sendMsgResult.getCode()));
            result.setMsg(sendMsgResult.getMsg());
            result.setSuccess(sendMsgResult.getCode() == 0);
        }
        return result;
    }


    /**
     * @description: 一账通号自动选渠道发送消息
     * @param sendInfo
     * @return com.howbuy.crm.wechat.service.domain.message.SendResultDTO
     * @author: jin.wang03
     * @date: 2025/6/19 17:58
     * @since JDK 1.8
     */
    public SendResultDTO sendAutoChannel(MessageSendInfoPO sendInfo) {
        log.info("一账通号自动选渠道发送消息,req={}", sendInfo);
        SendResultDTO result = new SendResultDTO();
        if (StringUtils.isEmpty(sendInfo.getTempleteParams())) {
            log.info("【一账通号自动选渠道发送消息】消息体为空，不做处理");
            result.setMsg("消息体为空，不做处理");
            return result;
        }

        AutoChannelMessageVo messageVO = JSON.parseObject(sendInfo.getTempleteParams(), AutoChannelMessageVo.class);

        SendMsgByHboneNoRequest sendMsgRequest = new SendMsgByHboneNoRequest();
        sendMsgRequest.setBusinessId(sendInfo.getTempleteId());

        sendMsgRequest.setHboneNo(messageVO.getHboneNo());
        sendMsgRequest.setTemplateVar(messageVO.getTemplateParams());

        SendMsgByHboneNoResponse sendMsgResult = companySendBusiness.sendAutoChannel(sendMsgRequest);

        log.info("一账通号自动选渠道发送消息,request:{}, 结果:{}", JSON.toJSONString(sendMsgRequest), JSON.toJSONString(sendMsgResult));
        if (sendMsgResult != null) {
            result.setCode(String.valueOf(sendMsgResult.getCode()));
            result.setMsg(sendMsgResult.getMsg());
            result.setSuccess(sendMsgResult.getCode() == 0);
        }
        return result;
    }

}