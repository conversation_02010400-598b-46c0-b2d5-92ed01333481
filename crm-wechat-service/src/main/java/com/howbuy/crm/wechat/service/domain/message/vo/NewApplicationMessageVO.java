/**
 * Copyright (c) 2023, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.domain.message.vo;

import com.howbuy.cc.message.send.company.company.model.TemplateCardModel;
import lombok.Data;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/11/29 11:18 AM
 * @since JDK 1.8
 */
@Data
public class NewApplicationMessageVO {
    /**
     * 公司主体， 默认使用- HMCF（好买财富）
     */
    private String company;

    /**
     * 自建企业应用的id（企业id和应用密钥通过线下方式给到消息中心系统配置）
     */
    private String agentId;

    /**
     * 成员ID列表（消息接收者，多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为@all，则向关注该企业应用的全部成员发送
     */
    private String touser;

    /**
     * 1:文本, 4:图片
     * 6-模板卡片消息 。  对应企微自建应用消息： "msgtype" : "template_card" ;
     */
    private Integer messageType;

    /**
     * 消息内容，最长不超过2048个字节，超过将截断
     */
    private String content;

    /**
     * 文件名，msgType = 4时必填
     */
    private String fileName;

    /**
     * 图片文件字节流,msgType = 4时必填
     */
    private byte[] fileData;


    /**
     * messageType=6 模板卡片消息 时
     * 企微自建应用消息。 msgtype=template_card时 消息体
     */
//    private TemplateCardVo templateCardModel;

    /**
     * messageType=6 模板卡片消息 时
     * 企微自建应用消息。 msgtype=template_card时 消息体
     */
    private TemplateCardModel templateCardModel;

}