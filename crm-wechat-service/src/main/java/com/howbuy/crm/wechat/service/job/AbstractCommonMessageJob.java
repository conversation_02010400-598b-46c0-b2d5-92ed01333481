/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.job;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.wechat.service.commom.enums.QuartzResultEnum;
import com.howbuy.crm.wechat.service.commom.utils.LoggerUtils;
import com.howbuy.message.MessageService;
import com.howbuy.message.SimpleMessage;
import com.howbuy.message.processor.MessageProcessor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;

/**
 * @description: 消息接收处理器 - 无业务lock ,无 回执处理 。适用于不需要锁的场景，如：发送消息，发送短信等
 * <AUTHOR>
 * @date 2023年10月7日 下午5:07:00
 * @since JDK 1.8
 */
@Slf4j
public abstract class AbstractCommonMessageJob extends MessageProcessor {


    @PostConstruct
    public void init() {
        // 加载消息处理器
        MessageService.getInstance().addMessageProcessor(this.getQuartMessageChannel(), this);
    }


    /**
     * @description:(消息队列名称)
     * @param
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2023/10/7 10:26
     * @since JDK 1.8
     */
    protected abstract String getQuartMessageChannel();

    /**
     * @description:(具体执行的回调方法)
     * @param message
     * @return void
     * @author: haoran.zhang
     * @date: 2023/10/7 10:26
     * @since JDK 1.8
     */
    protected abstract void doProcessMessage(SimpleMessage message);

    /**
     * @description: 处理消息
     * @param message	消息
     * @author: hongdong.xie
     * @date: 2023/3/8 18:04
     * @since JDK 1.8
     */
    @Override
    public void processMessage(SimpleMessage message) {
        long startTime = System.currentTimeMillis();
        LoggerUtils.setUUID();
        log.info("AbstractCommonMessageJob:{} |processMessage start -> message:{}", this.getClass().getSimpleName(), JSON.toJSONString(message));
        int quartzResult = 0;
        try {
            this.doProcessMessage(message);
            quartzResult = QuartzResultEnum.SUCCESS.getCode();
        } catch (Exception ex) {
            quartzResult = QuartzResultEnum.FAIL.getCode();
            log.error("Error process Message " + message.getContent(), ex);
        } finally {
            log.info("AbstractCommonMessageJob:{} |processMessage quartzResult:{}, content:{}, costTime:{}",
                    this.getClass().getSimpleName(), quartzResult,
                    JSON.toJSONString(message.getContent()),
                    System.currentTimeMillis() - startTime);
            LoggerUtils.clearConfig();
        }
    }


}