/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.domain.message.vo;

import com.howbuy.cc.message.send.company.bot.model.NewsBotMessage;
import com.howbuy.cc.message.send.company.bot.model.TextBotMessage;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (群机器发送消息-参数)
 * <AUTHOR>
 * @date 2023/10/12 10:23
 * @since JDK 1.8
 */
@Data
public class TextBotMessageVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 文本内容，最长不超过2048个字节，必须是utf8编码
     */
    private String content;

    /**
     * userid的列表，提醒群中的指定成员(@某个成员)，@all表示提醒所有人,如果开发者获取不到userid，可以使用mentioned_mobile_list
     */
    private List<String> mentionedList;

    /**
     * 手机号列表，提醒手机号对应的群成员(@某个成员)，@all表示提醒所有人
     */
    private List<String> mentionedMobileList;

}