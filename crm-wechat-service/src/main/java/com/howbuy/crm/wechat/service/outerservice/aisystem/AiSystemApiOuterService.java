/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.outerservice.aisystem;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.service.commom.exception.BusinessException;
import com.howbuy.crm.wechat.service.commom.utils.HttpUtils;
import com.howbuy.crm.wechat.service.commom.utils.LoggerUtils;
import com.howbuy.crm.wechat.service.commom.utils.MainLogUtils;
import com.howbuy.crm.wechat.service.outerservice.aisystem.domain.context.GetAccessTokenContext;
import com.howbuy.crm.wechat.service.outerservice.aisystem.domain.dto.GetAccessTokenDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: AI_SYSTEM系统HTTP接口封装服务
 * @date 2025-08-18 09:39:32
 * @since JDK 1.8
 */
@Slf4j
@Service
public class AiSystemApiOuterService {

    @Resource
    private RestTemplate restTemplate;
    
    @Value("${wecom.api.base.url:}")
    private String baseUrl;
    
    @Value("${wecom.api.timeout:30000}")
    private int timeout;

    private static final String REMOTE_NAME = "howbuy-wecom";

    /**
     * @description: 获取AI_SYSTEM系统AccessToken信息
     * @param corpId 企业ID
     * @return String 调用ai_system系统getAccessToken接口响应结果
     * <AUTHOR>
     * @date 2025-08-18 09:39:32
     * @since JDK 1.8
     */
    public String getAccessToken(String corpId) {
        log.info("AiSystemApiOuterService>>>getAccessToken请求开始，参数：{}", corpId);
        long start = System.currentTimeMillis();
        // 构建请求URL
        String url = buildRequestUrl();
        // 请求示例 GET /api/corp_access_token/ww1234567890abcdef
        url = url + corpId;

        try {
            
            log.info("AiSystemApiOuterService>>>getAccessToken请求URL：{}", url);
            
            // 使用HttpUtils发送GET请求
            ResponseEntity<String> response = HttpUtils.get(url, String.class, restTemplate);
            
            log.info("AiSystemApiOuterService>>>getAccessToken响应结果，状态码：{}，响应体：{}", 
                response.getStatusCode(), response.getBody());
            
            // 处理响应结果
            String result = processResponse(response);

            long  end = System.currentTimeMillis();
            MainLogUtils.httpCallOut(url, baseUrl, REMOTE_NAME, ResponseCodeEnum.SUCCESS.getCode(), end - start);
            return result;
        } catch (Exception e) {
            log.error("AiSystemApiOuterService>>>getAccessToken调用异常", e);
            MainLogUtils.httpCallOut(url, baseUrl, REMOTE_NAME, ResponseCodeEnum.SYS_ERROR.getCode(), System.currentTimeMillis() - start);
            throw new BusinessException(ResponseCodeEnum.SYS_ERROR.getCode(),
                "AI_SYSTEM系统getAccessToken接口调用异常：" + e.getMessage());
        }
    }
    
    /**
     * 构建请求URL
     */
    private String buildRequestUrl() {
        String apiPath = "/api/corp_access_token/";
        return baseUrl + apiPath;
    }
    
    /**
     * 处理响应结果
     */
    private String processResponse(ResponseEntity<String> response) {
        // 检查HTTP状态码
        if (!response.getStatusCode().is2xxSuccessful()) {
            log.error("AiSystemApiOuterService>>>getAccessToken HTTP请求失败，状态码：{}", response.getStatusCode());
            throw new BusinessException(ResponseCodeEnum.SYS_ERROR.getCode(),
                "AI_SYSTEM系统接口调用失败，HTTP状态码：" + response.getStatusCode());
        }
        
        String responseBody = response.getBody();
        if (responseBody == null || responseBody.trim().isEmpty()) {
            log.error("AiSystemApiOuterService>>>getAccessToken响应体为空");
            return null;
        }
        
        try {
            // 解析JSON响应
            GetAccessTokenDTO responseDto = JSON.parseObject(responseBody, GetAccessTokenDTO.class);
            log.info("AiSystemApiOuterService>>>getAccessToken响应结果：{}", JSON.toJSONString(responseDto));
            // 检查业务状态码
            if (!isBusinessSuccess(responseDto)) {
                log.error("AiSystemApiOuterService>>>getAccessToken业务处理失败，响应：{}", responseBody);
                return null;
            }
            
            // 返回简单类型
            return extractSimpleResult(responseDto);
            
        } catch (Exception e) {
            log.error("AiSystemApiOuterService>>>getAccessToken解析响应JSON异常：{}", responseBody, e);
            throw new BusinessException(ResponseCodeEnum.SYS_ERROR.getCode(),
                "AI_SYSTEM系统响应解析异常：" + e.getMessage());
        }
    }
    
    /**
     * 检查业务是否成功
     */
    private boolean isBusinessSuccess(GetAccessTokenDTO response) {
        return response != null && response.getSuccess() != null && response.getSuccess();
    }
    
    /**
     * 提取简单结果
     */
    private String extractSimpleResult(GetAccessTokenDTO response) {
        if (response == null) {
            return null;
        }
        
        // 从响应数据中提取accessToken
        return response.getAccessToken();
    }
}