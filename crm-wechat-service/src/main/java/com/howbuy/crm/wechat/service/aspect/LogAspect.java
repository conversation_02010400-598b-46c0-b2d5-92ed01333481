/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.aspect;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.JacksonUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.wechat.service.commom.utils.LoggerUtils;
import com.howbuy.crm.wechat.service.commom.utils.MainLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;

/**
 * @description: 日志打印（Controller层）
 * <AUTHOR>
 * @date 2023/7/5 10:40
 * @since JDK 1.8
 */
@Slf4j
@Aspect
@Component
public class LogAspect {

    private static final Logger mainLogger = LogManager.getLogger("mainlog");

    /**
     * GET请求
     */
    private static final String GET = "GET";
    /**
     * POST请求
     */
    private static final String POST = "POST";

    /**
     * UTF-8编码
     */
    private static final String CHARSET = "UTF-8";

    @Pointcut("execution(* com.howbuy.crm.wechat.service.controller..*.*(..))")
    public void entryPoint() {
    }

    /**
     * @description: 切点实现
     * @param pjp 参数
     * @return java.lang.Object
     * @author: hongdong.xie
     * @date: 2023/7/5 10:48
     * @since JDK 1.8
     */
    @Around("entryPoint()")
    public Object doAround(ProceedingJoinPoint pjp) throws Throwable {
        LoggerUtils.setReqId(getReqId());
        LoggerUtils.setRanNo(LoggerUtils.createRanNo());
        long start = System.currentTimeMillis();
        // 1.请求处理
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) requestAttributes;
        assert servletRequestAttributes != null;
        HttpServletRequest request = servletRequestAttributes.getRequest();

        Signature signature = pjp.getSignature();
        String signatureStr = signature.toShortString();

        // 处理请求日志
        printRequestLog(servletRequestAttributes);
        // 2.返回处理
        Object result = null;
        // 默认请求状态码 200
        int status = 200;
        try {
            result = pjp.proceed();
        } catch (Throwable e) {
            // 异常状态码 500
            status = 500;
            throw new RuntimeException(e);
        } finally {
            long end = System.currentTimeMillis();
            // 处理返回日志
            printResponseLog(request,result,start,end);
            // 记录main.log
            printMainLog(status, signatureStr, end - start);
        }
        LoggerUtils.clearConfig();
        return result;
    }

    /**
     * @description: 打印main.log
     * @param
     * @return java.lang.String
     * @throws
     * @since JDK 1.8
     */
    private void printMainLog(int status, String signatureStr, long time) {
        if (mainLogger.isInfoEnabled()) {
            try {
                String reqId = LoggerUtils.getReqId();
                String ranNo = LoggerUtils.getRanNo();
                MainLogUtils.httpCallIn(reqId, ranNo, signatureStr, String.valueOf(status), time);
            } catch (Exception e) {
                log.error("LogAspect printMainLog error.|methodName:{}", signatureStr);
                log.error("", e);
            }
        }
    }

    /**
     * @param
     * @return java.lang.String
     * @throws
     * @description: 获取reqId
     * @since JDK 1.8
     */
    private String getReqId() {
        // dubbo接口调用
        String reqId = (RpcContext.getServerAttachment() == null) ? null : RpcContext.getServerAttachment().getAttachment(LoggerUtils.REQ_ID);
        if (StringUtils.isEmpty(reqId)) {
            // http接口调用
            reqId = getHttpReqId();
        }
        return reqId;
    }

    /**
     * @description: 获取当前请求的reqId
     * @param
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2024/9/24 10:06
     * @since JDK 1.8
     */
    private String getHttpReqId(){
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        String reqId = UUID.randomUUID().toString();
        if(Objects.isNull(requestAttributes)){
            return reqId;
        }
        //从获取RequestAttributes中获取HttpServletRequest的信息
        try {
            HttpServletRequest request = (HttpServletRequest) requestAttributes.resolveReference(RequestAttributes.REFERENCE_REQUEST);
            if(Objects.isNull(request)){
                return reqId;
            }
            String tradeId = request.getHeader(LoggerUtils.REQ_ID);
            if (StringUtils.isNotEmpty(tradeId)) {
                reqId = tradeId;
            }
        } catch (Exception e) {
            log.warn("获取HttpServletRequest失败: {}", e.getMessage());
        }

        return reqId;
    }

    /**
     * @description: 打印返回日志
     * @param request 请求
     * @param result 返回结果
     * @param start 开始时间
     * @param end 结束时间
     * @return void
     * @author: hongdong.xie
     * @date: 2023/7/6 09:55
     * @since JDK 1.8
     */
    private void printResponseLog(HttpServletRequest request,Object result,long start,long end){
        String uri = request.getRequestURI();
        // 获取请求方法
        String method = request.getMethod();
        try {
            log.info(">>>>> Response Info,URI: '{}', method: '{}',ResponseData:{}", uri, method , JSON.toJSONString(getLogDetail(result)));
        }catch (Exception e){
            log.error("printResponseLog error.",e);
        }
        log.info(">>>>> Response Info,URI: '{}',cost:{} ms.", uri , (end-start));
    }

    /**
     * @description: 处理请求日志
     * @param servletRequestAttributes 参数
     * @author: hongdong.xie
     * @date: 2023/7/5 11:01
     * @since JDK 1.8
     */
    private void printRequestLog(ServletRequestAttributes servletRequestAttributes){
        try {
            HttpServletRequest request = servletRequestAttributes.getRequest();
            String uri = request.getRequestURI();
            // 获取请求方式
            String method = request.getMethod();
            Map<String, String> paramsObj = null;
            // GET
            if (GET.equals(method)) {
                paramsObj = getQueryStringMap(request);
            }else if (POST.equals(method)) {
                paramsObj = getPostParams(request);
            }
            log.info(">>>>> Request Info,URI: '{}', method: '{}', Data:{}", uri, method, JSON.toJSONString(getLogDetail(paramsObj)));
        }catch (Exception e){
            log.error("printRequestLog error.",e);
        }
    }

    /**
     * @description: 处理POST请求参数
     * @param request	请求
     * @return java.util.Map<java.lang.String,java.lang.String>
     * @author: hongdong.xie
     * @date: 2023/7/5 20:15
     * @since JDK 1.8
     */
    private Map<String,String> getPostParams(HttpServletRequest request){
        Enumeration<?> parameterNames = request.getParameterNames();
        Map<String, String> params = new HashMap<String, String>();
        if (parameterNames != null) {
            while (parameterNames.hasMoreElements()) {
                String paramName = (String) parameterNames.nextElement();
                params.put(paramName, request.getParameter(paramName));
            }
        }
        return params;
    }

    /**
     * @description: 处理get请求参数
     * @param request	请求
     * @return java.util.Map<java.lang.String,java.lang.String>
     * @author: hongdong.xie
     * @date: 2023/7/5 20:15
     * @since JDK 1.8
     */
    private Map<String, String> getQueryStringMap(HttpServletRequest request) {
        Map<String, String> params = new HashMap<String, String>();
        if (request == null) {
            log.error("http request is null!");
            return params;
        }
        String queryString = request.getQueryString();
        if(StringUtil.isEmpty(queryString)) {
            return params;
        }
        String[] arrSplit = queryString.split("&");
        for (String strSplit : arrSplit) {
            String[] arrSplitEqual = strSplit.split("=");
            String key = null;
            String value = null;
            if (arrSplitEqual.length > 1) {
                key = arrSplitEqual[0];
                value = arrSplitEqual[1];
            }
            if(arrSplitEqual.length == 1 && !StringUtil.isEmpty(arrSplitEqual[0])) {
                key = arrSplitEqual[0];
                value = "";
            }
            try {
                value = URLDecoder.decode(value,CHARSET);
            } catch (UnsupportedEncodingException e) {
                log.error("network URLDecoder decode is error:", e);
            }
            params.put(key, value);

        }
        return params;
    }

    /**
     * 列表长度
     */
    private static final int LIST_MAX_SIZE = 100;
    /**
     * 日志最大长度
     */
    private static final int LOG_MAX_LENGTH = 100000;
    /**
     * @description: 获取日志详情
     * @param object 原始日志内容
     * @return com.howbuy.crm.hb.web.aspect.LogAspect.LogDetail
     * @author: hongdong.xie
     * @date: 2023/7/6 09:42
     * @since JDK 1.8
     */
    private LogDetail getLogDetail(Object object) {
        LogDetail li = new LogDetail();
        if (object == null) {
            return li;
        }
        int itemSize = -1;
        int byteLength = -1;
        if(object instanceof List) {
            itemSize = ((List<?>) object).size();
            li.setItemSize(itemSize);
        }
        if(itemSize <= LIST_MAX_SIZE) {
            String result = object instanceof String ? (String)object : JacksonUtil.objToJson(object);
            if(result != null) {
                byteLength = result.getBytes().length;
                li.setByteLength(byteLength);
                if(byteLength <= LOG_MAX_LENGTH) {
                    li.setContent(new String(result.getBytes()));
                }
            }
        }
        li.setLogDetail(itemSize <= LIST_MAX_SIZE || byteLength <= LOG_MAX_LENGTH);
        return li;
    }

    private static class LogDetail {
        private int itemSize = 0;
        private int byteLength = 0;
        private String content = null;
        private boolean isLogDetail = true;

        public int getItemSize() {
            return itemSize;
        }
        public void setItemSize(int itemSize) {
            this.itemSize = itemSize;
        }
        public int getByteLength() {
            return byteLength;
        }
        public void setByteLength(int byteLength) {
            this.byteLength = byteLength;
        }
        public String getContent() {
            return content;
        }
        public void setContent(String content) {
            this.content = content;
        }
        public boolean isLogDetail() {
            return isLogDetail;
        }
        public void setLogDetail(boolean isLogDetail) {
            this.isLogDetail = isLogDetail;
        }
    }
}