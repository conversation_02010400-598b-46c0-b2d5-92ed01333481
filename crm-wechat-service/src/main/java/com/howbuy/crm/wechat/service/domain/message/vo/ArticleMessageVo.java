/**
 * Copyright (c) 2023, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.domain.message.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (群机器发送消息-参数)
 * <AUTHOR>
 * @date 2023/10/12 10:23
 * @since JDK 1.8
 */
@Data
public class ArticleMessageVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标题，不超过128个字节，超过会自动截断
     */
    private String title;

    /**
     * 描述，不超过512个字节，超过会自动截断
     */
    private String description;

    /**
     * 点击后跳转的链接
     */
    private String url;


    /**
     * 图文消息的图片链接(http://)，支持JPG、PNG格式，较好的效果为大图 1068455，小图150150
     */
    private String picurl;

}