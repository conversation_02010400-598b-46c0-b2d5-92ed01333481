package com.howbuy.crm.wechat.service.domain.callback;

import lombok.*;

import java.io.Serializable;

/**
 * @description: 企业微信签名DTO
 * @author: yu.zhang
 * @date: 2023/6/8 15:17 
 * @since JDK 1.8
 * @version: 1.0
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class WechatCallbackSignatureDTO implements Serializable {
    
    /**
     * 消息签名
     */
    private String signature;
    /**
     * 时间戳
     */
    private String timestamp;
    /**
     * 随机串，对应URL参数的
     */
    private String nonce;
    /**
     * 签名解析之后的原文
     */
    private String result;

}
