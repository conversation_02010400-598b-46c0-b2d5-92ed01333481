/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.message.build;

import com.google.common.collect.Maps;
import com.howbuy.crm.wechat.client.enums.AcceptMessageTypeEnum;
import com.howbuy.crm.wechat.dao.po.message.MessageAcceptInfoPO;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: (构建待发送消息工厂类    用于构建待发送消息 )
 * <AUTHOR>
 * @date 2023/10/7 16:27
 * @since JDK 1.8
 */
@Component
public class MessageBuildFactory implements ApplicationContextAware {

    /**
     * 消息构建 服务类 MessageBuildService的所有实现
     */
    private static final Map<AcceptMessageTypeEnum, MessageBuildService> map = Maps.newConcurrentMap();
    /**
     * spring 上下文
     */
    private static ApplicationContext appCtx;



    /**
     * @description:构建待发送消息
     * @param acceptInfo 消息接收信息
     * @return void
     * @throws
     * @since JDK 1.8
     */
    public void buidMessage(MessageAcceptInfoPO acceptInfo){
        AcceptMessageTypeEnum typeEnum=AcceptMessageTypeEnum.getEnum(acceptInfo.getMessageType());
        MessageBuildService messageDealService = map.get(typeEnum);
        messageDealService.buildMessage(acceptInfo);
    }



    /**
     * @description:构建待发送消息
     * @param acceptInfoList 消息接收信息列表
     * @return void
     * @throws
     * @since JDK 1.8
     */
    public void buidMessageList(List<MessageAcceptInfoPO> acceptInfoList) {
        //按照消息类型分组
        Map<AcceptMessageTypeEnum, List<MessageAcceptInfoPO>> groupByMsgType =
                acceptInfoList.stream()
                        .collect(Collectors.groupingBy(acceptInfo -> AcceptMessageTypeEnum.getEnum(acceptInfo.getMessageType())));
        groupByMsgType.forEach((type, dataList) -> {
            MessageBuildService messageDealService = map.get(type);
            messageDealService.buildMessageList(dataList);
        });
    }



    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        appCtx = applicationContext;
    }

    @PostConstruct
    public void initMap() {
        Map<String, MessageBuildService> beansOfType = appCtx.getBeansOfType(MessageBuildService.class);
        for (MessageBuildService messageDealService : beansOfType.values()) {
            List<AcceptMessageTypeEnum> supportTypeList = messageDealService.getMsgTypeList();
            for (AcceptMessageTypeEnum classEnum : supportTypeList) {
                map.put(classEnum, messageDealService);
            }
        }
    }

}