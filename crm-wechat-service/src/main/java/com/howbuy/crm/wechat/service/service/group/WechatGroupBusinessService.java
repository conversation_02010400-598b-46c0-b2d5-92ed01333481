/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.group;

import com.howbuy.crm.wechat.client.domain.request.group.GetGroupInfoByUserIdRequest;
import com.howbuy.crm.wechat.client.domain.request.group.GetGroupInfoByChatIdRequest;
import com.howbuy.crm.wechat.client.domain.request.group.GetGroupInfoByExternalUserIdRequest;
import com.howbuy.crm.wechat.client.domain.response.group.GroupChatInfoVO;
import com.howbuy.crm.wechat.client.domain.response.group.GroupChatMemberInfoVO;
import com.howbuy.crm.wechat.service.domain.externaluser.GroupChatInfo;
import com.howbuy.crm.wechat.service.domain.externaluser.GroupChatMemberInfo;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 微信群管理业务服务
 * @date 2025-08-19 19:05:03
 * @since JDK 1.8
 */
@Service
@Slf4j
public class WechatGroupBusinessService {

    @Resource
    private WechatGroupService wechatGroupService;

    /**
     * @param request 请求参数
     * @return 返回结果
     * @description: 根据员工账号查询所有有他的群
     * <AUTHOR>
     * @date 2025-08-19 19:05:03
     * @since JDK 1.8
     */
    public List<GroupChatInfoVO> getGroupInfoByUserId(GetGroupInfoByUserIdRequest request) {
        List<GroupChatInfo> groupChatInfos = wechatGroupService.getGroupInfoByUserId(request.getCompanyNo(), request.getUserId());
        return convertGroupChatInfoList(groupChatInfos);
    }

    /**
     * @param request 请求参数
     * @return 返回结果
     * @description: 根据群ID获取群信息
     * <AUTHOR>
     * @date 2025-08-19 19:05:03
     * @since JDK 1.8
     */
    public GroupChatInfoVO getGroupInfoByChatId(GetGroupInfoByChatIdRequest request) {
        GroupChatInfo groupChatInfo;
        if (StringUtil.isNotNullStr(request.getCorpId()) && StringUtil.isNotNullStr(request.getCorpSecret())) {
            groupChatInfo = wechatGroupService.getDynamicGroupInfoByChatId(request.getChatId(), request.getCorpId(), request.getCorpSecret());
        } else {
            groupChatInfo = wechatGroupService.getGroupInfoByChatId(request.getChatId(), request.getCompanyNo());
        }
        return convertGroupChatInfo(groupChatInfo);
    }

    /**
     * @param request 请求参数
     * @return 返回结果
     * @description: 根据外部客户ID查询所有有他的群
     * <AUTHOR>
     * @date 2025-08-19 19:05:03
     * @since JDK 1.8
     */
    public List<GroupChatInfoVO> getGroupInfoByExternalUserId(GetGroupInfoByExternalUserIdRequest request) {
        List<GroupChatInfo> groupChatInfos = wechatGroupService.getGroupInfoByUserId(request.getCompanyNo(), request.getExternalUserId());
        return convertGroupChatInfoList(groupChatInfos);
    }

    /**
     * @param groupChatInfos 群聊信息列表
     * @return 群聊信息VO列表
     * @description: 将群聊信息对象列表转换为VO对象列表
     * <AUTHOR>
     * @date 2025-08-19 19:05:03
     * @since JDK 1.8
     */
    private List<GroupChatInfoVO> convertGroupChatInfoList(List<GroupChatInfo> groupChatInfos) {
        if (groupChatInfos == null || groupChatInfos.isEmpty()) {
            return new ArrayList<>();
        }
        return groupChatInfos.stream()
                .map(this::convertGroupChatInfo)
                .collect(Collectors.toList());
    }

    /**
     * @param groupChatInfo 群聊信息对象
     * @return 群聊信息VO对象
     * @description: 将群聊信息对象转换为VO对象
     * <AUTHOR>
     * @date 2025-08-19 19:05:03
     * @since JDK 1.8
     */
    private GroupChatInfoVO convertGroupChatInfo(GroupChatInfo groupChatInfo) {
        if (groupChatInfo == null) {
            return null;
        }
        GroupChatInfoVO vo = new GroupChatInfoVO();
        vo.setChatId(groupChatInfo.getChatId());
        vo.setChatName(groupChatInfo.getChatName());
        vo.setChatOwner(groupChatInfo.getChatOwner());
        vo.setCreateTime(groupChatInfo.getCreateTime());
        vo.setNotice(groupChatInfo.getNotice());
        vo.setAdminUserIdList(groupChatInfo.getAdminUserIdList());
        
        if (groupChatInfo.getChatMemberList() != null) {
            List<GroupChatMemberInfoVO> memberVOs = groupChatInfo.getChatMemberList().stream()
                    .map(this::convertGroupChatMemberInfo)
                    .collect(Collectors.toList());
            vo.setChatMemberList(memberVOs);
        }
        
        return vo;
    }

    /**
     * @param memberInfo 群成员信息对象
     * @return 群成员信息VO对象
     * @description: 将群成员信息对象转换为VO对象
     * <AUTHOR>
     * @date 2025-08-19 19:05:03
     * @since JDK 1.8
     */
    private GroupChatMemberInfoVO convertGroupChatMemberInfo(GroupChatMemberInfo memberInfo) {
        if (memberInfo == null) {
            return null;
        }
        GroupChatMemberInfoVO vo = new GroupChatMemberInfoVO();
        vo.setUserId(memberInfo.getUserId());
        vo.setType(memberInfo.getType());
        vo.setUnionid(memberInfo.getUnionid());
        vo.setJoinTime(memberInfo.getJoinTime());
        vo.setJoinScene(memberInfo.getJoinScene());
        vo.setInvitorUserId(memberInfo.getInvitorUserId());
        vo.setGroupNickname(memberInfo.getGroupNickname());
        vo.setName(memberInfo.getName());
        return vo;
    }
}