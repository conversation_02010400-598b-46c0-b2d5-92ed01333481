/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.facade;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.custrelation.SelectRelationListByVoRequest;
import com.howbuy.crm.wechat.client.domain.response.custrelation.SelectRelationListByVoVO;
import com.howbuy.crm.wechat.client.facade.custrelation.WechatCustRelationFacade;
import com.howbuy.crm.wechat.service.service.custrelation.WechatCustRelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 企业微信客户关系管理接口实现类
 * @date 2025-08-19 19:52:46
 * @since JDK 1.8
 */
@DubboService
@Slf4j
public class WechatCustRelationFacadeImpl implements WechatCustRelationFacade {

    @Resource
    private WechatCustRelationService wechatCustRelationService;

    @Override
    public Response<SelectRelationListByVoVO> selectRelationListByVo(SelectRelationListByVoRequest request) {
        return Response.ok(wechatCustRelationService.selectRelationListByVo(request));
    }
}