/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.facade;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.wechatjssdk.GetConfigSignatureRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatjssdk.GetConfigSignatureVO;
import com.howbuy.crm.wechat.client.facade.wechatjssdk.WechatJsSdkFacade;
import com.howbuy.crm.wechat.service.service.wechatjssdk.WechatJsSdkService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 微信JS SDK接口实现
 * @date 2024/9/19 9:07
 * @since JDK 1.8
 */
@DubboService
public class WechatJsSdkFacadeImpl implements WechatJsSdkFacade {

    @Resource
    private WechatJsSdkService wechatJsSdkService;


    @Override
    public Response<GetConfigSignatureVO> getConfigSignature(GetConfigSignatureRequest request) {
        return wechatJsSdkService.getConfigSignature(request);
    }
}
