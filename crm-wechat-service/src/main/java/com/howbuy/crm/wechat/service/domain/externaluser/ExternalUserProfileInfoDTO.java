package com.howbuy.crm.wechat.service.domain.externaluser;

import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @classname: ExternalUserInfoDTO
 * @author: yu.zhang
 * @description:  * 外部联系人/客户的自定义展示信息，
 *  可以有多个字段和多种类型，包括文本，网页和小程序，仅当联系人类型是企业微信用户时有此字段，字段详情见对外属性；
 *  see:https://work.weixin.qq.com/api/doc/90000/90135/92230
 * @creatdate: 2021-02-08 16:48
 * @since: JDK1.8
 */
@Getter
@Setter
@ToString
public class ExternalUserProfileInfoDTO implements Serializable {

    /**
     * profile自定义类型
     */
    private String type;

    /**
     *profile自定义名称
     */
    private String name;

    /**
     *profile的详细属性
     */
    private Map<String,List<Map<String,String>>>  profileMap= Maps.newHashMap();


}
