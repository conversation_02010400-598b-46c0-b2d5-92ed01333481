package com.howbuy.crm.wechat.service.service.externalcontact;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.howbuy.common.date.DateUtil;
import com.howbuy.crm.nt.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.wechat.client.enums.OutSysTypeEnum;
import com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO;
import com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO;
import com.howbuy.crm.wechat.dao.po.CmWechatExternalInfoPO;
import com.howbuy.crm.wechat.service.commom.constant.WxTempConstant;
import com.howbuy.crm.wechat.service.commom.enums.WechatApplicationTypeEnum;
import com.howbuy.crm.wechat.service.commom.enums.WechatStatusEnum;
import com.howbuy.crm.wechat.service.domain.callback.ExternalContactDTO;
import com.howbuy.crm.wechat.service.domain.externaluser.ExternalUserInfoDTO;
import com.howbuy.crm.wechat.service.domain.externaluser.ExternalUserRelationInfoDTO;
import com.howbuy.crm.wechat.service.outerservice.acccenter.QueryCustSensitiveInfoOuterService;
import com.howbuy.crm.wechat.service.outerservice.acccenter.QueryOuterAcctLoginBindService;
import com.howbuy.crm.wechat.service.outerservice.acccenter.QueryWechatAcctBindService;
import com.howbuy.crm.wechat.service.outerservice.crm.QueryConscustListOuterService;
import com.howbuy.crm.wechat.service.outerservice.crm.QueryCustconstantInfoOuterService;
import com.howbuy.crm.wechat.service.outerservice.hkacccenter.QueryHkWechatAcctBindOuterService;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WechatExternalContactOuterService;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.domain.ExternalWeChatBaseDTO;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.sendwechat.WechatMessageSendOuterServer;
import com.howbuy.crm.wechat.service.repository.CmWechatCustInfoRepository;
import com.howbuy.crm.wechat.service.repository.CmWechatCustRelationRepository;
import com.howbuy.crm.wechat.service.repository.CmWechatExternalInfoRepository;
import com.howbuy.crm.wechat.service.service.config.BaseConfigServce;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: 微信外部联系人回调处理service
 * @author: yu.zhang
 * @date: 2023/6/12 10:17 
 * @since JDK 1.8
 * @version: 1.0
 */
@Slf4j
@Service
public class ChangeExternalContactEventService {

    @Autowired
    private CmWechatExternalInfoRepository cmWechatExternalInfoRepository;
    @Autowired
    private WechatExternalContactOuterService wechatExternalContactOuterService;
    @Autowired
    private QueryOuterAcctLoginBindService queryOuterAcctLoginBindService;
    @Autowired
    private QueryWechatAcctBindService queryWechatAcctBindService;
    @Autowired
    private QueryConscustListOuterService queryConscustListOuterService;
    @Autowired
    private WechatMessageSendOuterServer wechatMessageSendOuterServer;
    @Autowired
    private CmWechatCustRelationRepository cmWechatCustRelationRepository;
    @Autowired
    private CmWechatCustInfoRepository cmWechatCustInfoRepository;
    @Autowired
    private QueryCustSensitiveInfoOuterService queryCustSensitiveInfoOuterService;
    @Autowired
    private QueryCustconstantInfoOuterService queryCustconstantInfoOuterService;
    @Autowired
    private QueryHkWechatAcctBindOuterService queryHkWechatAcctBindOuterService;

    @Autowired
    private BaseConfigServce baseConfigServce;


    /**
     * @description:微信与外部联系人回调
     * @param externalContactDTO
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/12 17:46
     * @since JDK 1.8
     */
    public void processExternalChangeType(ExternalContactDTO externalContactDTO) {
        String changeType = externalContactDTO.getChangeType();
        if (changeType.equals(WxTempConstant.EVENT_TYPE_EXTERNAL_ADD)
                || changeType.equals(WxTempConstant.EVENT_TYPE_EXTERNAL_ADDHALF)
                || changeType.equals(WxTempConstant.EVENT_TYPE_MSG_AUDIT_APPROVED)
        ) {
            //1.1记录数据，转换后落表
            this.dealExternalUser(externalContactDTO);
            this.dealExternalContactMessage(externalContactDTO);
        }
        //删除外部联系人
        else if (changeType.equals(WxTempConstant.EVENT_TYPE_EXTERNAL_DEL)) {
            this.dealExternalContactMessage(externalContactDTO);

        }//删除跟进成员事件
        else if (changeType.equals(WxTempConstant.EVENT_TYPE_EXTERNAL_DELFLL)) {
            this.dealExternalContactMessage(externalContactDTO);
        }
    }

    /**
     * @description:处理外部联系人
     * @param externalContactDTO
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/19 15:17
     * @since JDK 1.8
     */
    public void dealExternalUser(ExternalContactDTO externalContactDTO) {

        log.info("接收dealExternalUser开始:{}" + JSON.toJSON(externalContactDTO));

        //获取 公司编号
        String corpId= externalContactDTO.getCorpId();
        String companyNoEnum= getStringByCorpId(corpId);

        List<CmWechatExternalInfoPO> externalInfoList =
                cmWechatExternalInfoRepository.listCmWechatExternalInfo(externalContactDTO.getExternalUserID(), externalContactDTO.getChangeType(), companyNoEnum);
        //过滤3天内新增处理的数据
        externalInfoList = externalInfoList.stream().filter(externalInfo -> DateUtil.before(externalInfo.getCreateTime(), 24 * 3)).collect(Collectors.toList());

        //如果数据存在则走企业微信获取信息发送消息
        if (CollectionUtils.isEmpty(externalInfoList)) {
            //通过外部userid调用微信API接口获取unionid
            ExternalUserInfoDTO userinfo = wechatExternalContactOuterService.getExternalUser(externalContactDTO.getExternalUserID(), companyNoEnum, true);
            CmWechatExternalInfoPO cmWechatExternalInfo = new CmWechatExternalInfoPO();
            cmWechatExternalInfo.setExternalUserId(userinfo.getExternalUserId());
            cmWechatExternalInfo.setMsgType(externalContactDTO.getMsgType());
            cmWechatExternalInfo.setEvent(externalContactDTO.getEvent());
            cmWechatExternalInfo.setChangeType(externalContactDTO.getChangeType());
            cmWechatExternalInfo.setUnionid(userinfo.getUnionid());

            cmWechatExternalInfoRepository.insertWechatExternal(cmWechatExternalInfo, companyNoEnum);

            //通过unionid调用账户中心接口获取国内一账通信息
            String hboneNo = getHboneNoByAccCenter(userinfo.getUnionid());
            //通过unionid调用账户中心接口获取国内一账通信息
            String hkHboneNo = getHkhboneNoByHkAcccenter(userinfo.getUnionid());
            String usedHbOneNo = StringUtils.isNotBlank(hboneNo) ? hboneNo : hkHboneNo;
            String message;
            if (StringUtils.isNotEmpty(usedHbOneNo)) {
                ConscustInfoDomain conscustInfo = queryConscustListOuterService.queryConscustInfo(usedHbOneNo);
                if (conscustInfo != null) {
                    message = MessageFormat.format("客户:{0}在CRM系统已存在，客户姓名:{1}，所属投顾：{2}，一账通号:{3}",
                            userinfo.getExternalUserName(), conscustInfo.getCustname(), conscustInfo.getConscode(), usedHbOneNo);
                } else {
                    message = MessageFormat.format("客户:{0}在CRM系统中不存在对应的投顾客户信息", userinfo.getExternalUserName());
                }
            } else {
                message = MessageFormat.format("客户:{0}微信未绑定一账通", userinfo.getExternalUserName());
            }
            //未绑定一账通号，提示投顾该客户未有绑定一账通号，并附上注册/登录链接（后提供）；

            //获取 默认 企业内 发送消息应用
            String applicationCode=baseConfigServce.getApplicationCodeByType(companyNoEnum, WechatApplicationTypeEnum.NOTICE);
            if(applicationCode!=null){
                sendMsgByType(WxTempConstant.TEMP_TEXT_TEXT, externalContactDTO.getUserID(), null, message, null, applicationCode);
            }else {
                log.warn("CompanyNo:{},未配置默认企微内建消息应用！",companyNoEnum);
            }
        }
    }


    private void sendMsgByType(String type,
                               String userid,
                               String title,
                               String msg,
                               String url,
                               String applicationCode) {
        Map<String, Object> paramMap = Maps.newHashMap();
        //拼装消息体
        Map<String, Object> contentmap = Maps.newHashMap();
        if (WxTempConstant.TEMP_TEXT_TEXT.equals(type)) {
            String newmsg = msg;
            if (StringUtils.isNotBlank(title)) {
                newmsg = title + "<br/>" + msg;
            }
            contentmap.put("content", newmsg);
            paramMap.put("text", contentmap);
        } else if (WxTempConstant.TEMP_TEXT_CARD.equals(type)) {
            contentmap.put("title", title);
            contentmap.put("description", "<div class='normal'>" + msg + "</div>");
            contentmap.put("url", url);
            contentmap.put("btntxt", "详情");
            paramMap.put("textcard", contentmap);
        }
        paramMap.put("touser", userid);
        paramMap.put("msgtype", type);
//        paramMap.put("agentid", corpId);

        // 使用的是  WechatApplicationEnum.WEALTH_CRM
        wechatMessageSendOuterServer.sendMsgByApplicationCode(paramMap, applicationCode);
    }

    /**
     * @param unionid
     * @return java.lang.String
     * @description:根据unionid获取账户中心的一账通ID
     * @author: yu.zhang
     * @date: 2023/6/19 15:16
     * @since JDK 1.8
     */
    public String getHboneNoByAccCenter(String unionid) {
        String hboneNo = queryOuterAcctLoginBindService.getAcctLoginHboneNoByUnionid(unionid);
        if (StringUtils.isNotEmpty(hboneNo)) {
            return hboneNo;
        }
        return queryWechatAcctBindService.getAcctHboneNoByUnionid(unionid);
    }



    private String getStringByCorpId(String corpId) {
        return baseConfigServce.getCompanyNo(corpId);
    }


    /**
     * @param unionId
     * @return java.lang.String
     * @description:(根据unionid获取账户中心的一账通ID)
     * @author: xufanchao
     * @date: 2025/5/22 10:12
     * @since JDK 1.8
     */
    public String getHkhboneNoByHkAcccenter(String unionId) {
        String hboneNo = null;
        String hkCustNo = queryHkWechatAcctBindOuterService.getAcctLoginHboneNoByUnionid(unionId, OutSysTypeEnum.WECHAT.getType());
        if (StringUtils.isNotEmpty(hkCustNo)) {
            hboneNo = queryHkWechatAcctBindOuterService.queryHboneNoByHkCustNo(hkCustNo);
        }
        return hboneNo;
    }

    /**
     * @param externalContactDTO
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/25 15:44
     * @since JDK 1.8
     */
    public void dealExternalContactMessage(ExternalContactDTO externalContactDTO) {
        log.info("接收dealExternalContactMessage变更事件:" + JSON.toJSON(externalContactDTO));
        //处理时间戳*1000

        String wechatConsCode = externalContactDTO.getUserID();
        externalContactDTO.setSource(StringUtils.isEmpty(externalContactDTO.getSource()) ? "" : externalContactDTO.getSource());
        //插入或更新CM_WECHAT_CUST_RELATION 状态和删除时间
        //微信账号翻译成CRM投顾号
        String realConsCode = queryCustconstantInfoOuterService.queryConsultantByWeChatConsCode(wechatConsCode);
        if (StringUtils.isNotEmpty(realConsCode)) {
            externalContactDTO.setUserID(realConsCode);
        }

        //获取 公司编号
        String corpId= externalContactDTO.getCorpId();
        String companyNoEnum= getStringByCorpId(corpId);

        cmWechatCustRelationRepository.insertCmWechatCustRelation(setWechatCustRelation(externalContactDTO),companyNoEnum);
        String externalUserID = externalContactDTO.getExternalUserID();
        ExternalUserInfoDTO externalUser = wechatExternalContactOuterService.getExternalUser(externalContactDTO.getExternalUserID(), companyNoEnum, false);
        //通过unionid调用账户中心接口获取一账通信息
        String hboneNo = getHboneNoByAccCenter(externalUser.getUnionid());
        // 通过unionid调用香港账户中心接口获取香港一账通信息
        String hkHboneNo = getHkhboneNoByHkAcccenter(externalUser.getUnionid());
        // 如果国内一账通有值,就用国内的一账通查询实名,否则用国外一账通
        String usedHbOneNo = StringUtils.isNotBlank(hboneNo) ? hboneNo : hkHboneNo;
        // 如果一账通号不为空, 则调用企业微信接口修改该客户在改投顾的备注
        if (StringUtils.isNotEmpty(usedHbOneNo)) {
            externalUser.setHboneNo(hboneNo);
            externalUser.setHkHboneNo(hkHboneNo);
            //参数1 客户一账通    参数2客户企业微信ID 参数3 投顾的企业微信号 注意可能会和投顾号不一致 对应CM_CONSULTANT表的wechatconscode
            modifyCustRemarkForConsCode(usedHbOneNo, externalUserID, wechatConsCode, companyNoEnum);
        }
        log.info("insertCmWechatCustInfo开始");
        cmWechatCustInfoRepository.insertCmWechatCustInfo(setWechatCustInfo(externalUser), companyNoEnum);
        List<ExternalUserRelationInfoDTO> followUserList = externalUser.getFollowUserList();
        log.info("insertCmWechatCustInfo followUserList：{}", JSON.toJSONString(followUserList));
        if (CollectionUtils.isNotEmpty(followUserList)) {
            followUserList.forEach(relationInfo -> {
                if (wechatConsCode.equals(relationInfo.getUserid())) {
                    CmWechatCustRelationPO wechatCustRelation = setWechatCustRelation(externalContactDTO);
                    wechatCustRelation.setAddWay(relationInfo.getAddWay());
                    cmWechatCustRelationRepository.insertCmWechatCustRelation(wechatCustRelation,companyNoEnum );
                }
            });
        }
    }

    /**
     * @description:修改某个投顾企业微信上的某个客户的备注
     * @param hboneNo 国内客户一账通
     * @param externalUserId  客户企业微信ID
     * @param wechatConsCode 投顾的企业微信号 注意可能会和投顾号不一致
     * @param @param companyNoEnum 企微-企业主体
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/25 15:57
     * @since JDK 1.8
     */
    public void modifyCustRemarkForConsCode(String hboneNo, String externalUserId, String wechatConsCode, String companyNo) {
        //根据一账通号获取客户姓名
        boolean ifRealNameByHboneNo = queryCustSensitiveInfoOuterService.getIfRealNameByHboneNo(hboneNo);
        log.info("根据一账通:{}获取是否实名结果:{}", hboneNo, ifRealNameByHboneNo);
        if (ifRealNameByHboneNo) {
            //实名才更新
            ConscustInfoDomain conscustInfo = queryConscustListOuterService.queryConscustInfo(hboneNo);
            log.info("获取到客户信息:{}", JSON.toJSONString(conscustInfo));
            if (StringUtils.isEmpty(externalUserId)) {
                CmWechatCustInfoPO cmWechatCustInfo = cmWechatCustInfoRepository.getExternalUserByHboneNo(hboneNo, companyNo);
                log.info("获取到客户CmWechatCustInfo:{}", JSON.toJSONString(cmWechatCustInfo));
            }
            //客户姓名  客户企业微信ID 不为空
            if (conscustInfo != null && StringUtils.isNotEmpty(conscustInfo.getCustname()) && StringUtils.isNotEmpty(externalUserId)
                    && StringUtils.isNotEmpty(wechatConsCode)) {
                //调用 修改客户备注接口
                ExternalWeChatBaseDTO markDto= wechatExternalContactOuterService.modifyRemark(wechatConsCode, externalUserId, conscustInfo.getCustname(), companyNo);
                log.info("投顾：{}，修改外部用户Id：{}，备注信息：{}，是否成功：{}",
                        wechatConsCode,externalUserId, conscustInfo.getCustname(),markDto.isSuccess());
            }
        }
    }

    /**
     * @description:通过企业微信回调数据封装成对应的企业微信客户对象
     * @param externalUser
     * @return com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO
     * @author: yu.zhang
     * @date: 2023/6/25 15:47
     * @since JDK 1.8
     */
    public CmWechatCustInfoPO setWechatCustInfo(ExternalUserInfoDTO externalUser) {
        CmWechatCustInfoPO cmWechatCustInfo = new CmWechatCustInfoPO();
        cmWechatCustInfo.setHboneNo(externalUser.getHboneNo());
        cmWechatCustInfo.setExternalUserId(externalUser.getExternalUserId());
        cmWechatCustInfo.setUnionid(externalUser.getUnionid());
        cmWechatCustInfo.setNickName(externalUser.getExternalUserName());
        cmWechatCustInfo.setHkHboneNo(externalUser.getHkHboneNo());
        cmWechatCustInfo.setWechatAvatar(externalUser.getWechatAvatar());
        return cmWechatCustInfo;
    }

    /**
     * @description:通过企业微信客户详情API封装成对应的关联关系
     * @param externalContactDTO
     * @return com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO
     * @author: yu.zhang
     * @date: 2023/6/25 15:47
     * @since JDK 1.8
     */
    public CmWechatCustRelationPO setWechatCustRelation(ExternalContactDTO externalContactDTO) {
        CmWechatCustRelationPO cmWechatCustRelation = new CmWechatCustRelationPO();

        cmWechatCustRelation.setConscode(externalContactDTO.getUserID());
        cmWechatCustRelation.setExternalUserId(externalContactDTO.getExternalUserID());
        if (Objects.equals(WxTempConstant.EVENT_TYPE_EXTERNAL_ADD, externalContactDTO.getChangeType())
                || Objects.equals(WxTempConstant.EVENT_TYPE_EXTERNAL_ADDHALF, externalContactDTO.getChangeType())
                || Objects.equals(WxTempConstant.EVENT_TYPE_MSG_AUDIT_APPROVED, externalContactDTO.getChangeType())) {
            cmWechatCustRelation.setStatus(WechatStatusEnum.ADD.getKey());
            cmWechatCustRelation.setAddTime(new Date(externalContactDTO.getCreateTime()));
        }

        if (Objects.equals(WxTempConstant.EVENT_TYPE_EXTERNAL_DEL, externalContactDTO.getChangeType())
                || Objects.equals(WxTempConstant.EVENT_TYPE_EXTERNAL_DELFLL, externalContactDTO.getChangeType())) {
            cmWechatCustRelation.setStatus(WechatStatusEnum.DELETE_BY_USER.getKey());
            cmWechatCustRelation.setDelTime(new Date(externalContactDTO.getCreateTime()));

            if (Objects.equals(WxTempConstant.EVENT_TYPE_EXTERNAL_DEL, externalContactDTO.getChangeType())
                    && Objects.equals(WxTempConstant.DELETE_BY_TRANSFER, externalContactDTO.getSource())) {
                cmWechatCustRelation.setStatus(WechatStatusEnum.DELETE_BY_TRANSFER.getKey());
            } else if (Objects.equals(WxTempConstant.EVENT_TYPE_EXTERNAL_DEL, externalContactDTO.getChangeType())
                    && !Objects.equals(WxTempConstant.DELETE_BY_TRANSFER, externalContactDTO.getSource())) {
                cmWechatCustRelation.setStatus(WechatStatusEnum.DELETE.getKey());
            }
        }
        cmWechatCustRelation.setCreateTime(new Date());
        cmWechatCustRelation.setState(externalContactDTO.getState());
        return cmWechatCustRelation;

    }
}
