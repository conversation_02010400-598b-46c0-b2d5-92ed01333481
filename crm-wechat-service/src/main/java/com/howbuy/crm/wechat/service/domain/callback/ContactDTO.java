package com.howbuy.crm.wechat.service.domain.callback;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description: 企业微信通讯录DTO
 * @author: yu.zhang
 * @date: 2023/6/8 18:34 
 * @since JDK 1.8
 * @version: 1.0
 */
@Getter
@Setter
@ToString
public class ContactDTO implements Serializable {
    /**
     * 应用ID
     */
    private String corpId;
    /**
     * 企业微信CorpID
     */
    private String toUserName;
    /**
     * 此事件该值固定为sys，表示该消息由系统生成
     */
    private String fromUserName;
    /**
     * 消息创建时间 （整型）
     */
    private String createTime;
    /**
     * 消息的类型
     */
    private String msgType;
    /**
     * 事件的类型
     */
    private String event;
    /**
     * 触发类型
     */
    private String changeType;
    /**
     * 企业服务人员的UserID
     */
    private String userID;
    /**
     * 新的UserID，变更时推送（userid由系统生成时可更改一次）
     */
    private String NewUserID;
    /**
     * 主部门
     */
    private Integer MainDepartment;
    /**
     * 父部门id
     */
    private Integer ParentId;
    /**
     * 部门Id
     */
    private Integer Id;
    /**
     * 成员名称/部门名称;代开发自建应用需要管理员授权才返回
     */
    private String Name;
    /**
     * 外部联系人的userid，注意不是企业成员的帐号
     */
    private String externalUserID;
    /**
     * 添加此用户的「联系我」方式配置的state参数，或在获客链接中指定的customer_channel参数，可用于识别添加此用户的渠道
     */
    private String state;
    /**
     * 欢迎语code，可用于发送欢迎语
     */
    private String welcomeCode;
    /**
     * 删除客户的操作来源，DELETE_BY_TRANSFER表示此客户是因在职继承自动被转接成员删除
     */
    private String source;
    /**
     * 头像url
     */
    private String avatar;
}
