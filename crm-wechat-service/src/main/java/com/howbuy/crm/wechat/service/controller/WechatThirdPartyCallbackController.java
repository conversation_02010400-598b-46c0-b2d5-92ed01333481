package com.howbuy.crm.wechat.service.controller;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.wechat.service.commom.aes.WXBizMsgCrypt;
import com.howbuy.crm.wechat.service.domain.callback.WechatCallbackSignatureDTO;
import com.howbuy.crm.wechat.service.service.WechatThirdPartyCallBackService;
import com.howbuy.crm.wechat.service.service.config.BaseConfigServce;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.util.Objects;

/**
 * @classname: WechatThirdPartyCallbackController
 * @author: hongdong.xie
 * @description: 企业微信第三方应用回调控制器
 * @date: 2025-08-19 16:57:27
 * @since: JDK1.8
 */
@Slf4j
@RestController
@RequestMapping("/wechat/thirdparty")
public class WechatThirdPartyCallbackController {

    @Autowired
    private WechatThirdPartyCallBackService wechatThirdPartyCallBackService;

    @Autowired
    private BaseConfigServce baseConfigServce;

    /**
     * @api {GET,POST} /wechat/thirdparty/callback/{companyNo} thirdPartyCallback()
     * @apiVersion 1.0.0
     * @apiGroup WechatThirdPartyCallbackController
     * @apiName thirdPartyCallback()
     * @apiDescription 企业微信第三方应用回调入口方法
     * @apiParam {String} companyNo 企业编码
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * null
     */
    @RequestMapping(value = "/callback/{companyNo}", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public void thirdPartyCallback(@PathVariable("companyNo") String companyNo,
                                   HttpServletRequest request, 
                                   HttpServletResponse response) {
        log.info("第三方应用回调，企业编码：{}", companyNo);
        
        // 安全验证：企业编码格式校验
        if (companyNo == null || companyNo.trim().isEmpty()) {
            log.error("第三方应用回调：企业编码为空");
            this.responseError(response, "企业编码不能为空");
            return;
        }
        
        // 安全验证：验证企业类型为第三方应用
        if (!baseConfigServce.isThirdPartyApplication(companyNo)) {
            log.error("第三方应用回调：企业编码：{} 不是第三方应用类型或配置不存在", companyNo);
            this.responseError(response, "企业配置错误");
            return;
        }
        
        // 安全验证：验证第三方应用配置完整性
        if (!baseConfigServce.validateThirdPartyConfig(companyNo)) {
            log.error("第三方应用回调：企业编码：{} 配置不完整", companyNo);
            this.responseError(response, "企业配置不完整");
            return;
        }
        
        this.verify(request, response, companyNo);
    }

    /**
     * @description:企业微信第三方应用回调验证方法+消息返回
     * @param request
     * @param response
     * @param companyNo 企微-企业主体
     * @return void
     * @author: hongdong.xie
     * @date: 2025-08-19 16:57:27
     * @since JDK 1.8
     */
    private void verify(HttpServletRequest request, HttpServletResponse response, String companyNo) {

        boolean isGet = "get".equalsIgnoreCase(request.getMethod());
        log.info("companyNo:{},接收企业微信第三方应用消息方式:{}",companyNo, isGet);
        if (Objects.equals(Boolean.TRUE, isGet)) {
            this.sendVerifySuccess(request, response, companyNo);
        } else {
            this.acceptMessage(request, response, companyNo);
        }
    }
    
    /**
     * acceptMessage 触发回调，用于实际的业务请求，比如应用菜单的点击事件，用户消息等。当有回调的行为发生时，企业微信服务端会向该回调URL发起一个 Post 请求
     * @param request
     * @param response
     * @param companyNo 企微-企业主体
     * @return void
     * @Author: hongdong.xie on 2025-08-19 16:57:27
     */
    private void acceptMessage(HttpServletRequest request, HttpServletResponse response, String companyNo) {
        log.info("companyNo:{},第三方应用acceptMessage开始",companyNo);
        long s = System.currentTimeMillis();
        
        try {
            request.setCharacterEncoding("UTF-8");
            response.setCharacterEncoding("UTF-8");

            //1.解密微信发过来的消息
            WechatCallbackSignatureDTO wechatCallbackSignature = this.getDecryptMsg(request, companyNo);

            //2.调用第三方应用消息业务类接收消息、处理消息
            String respMessage = wechatThirdPartyCallBackService.getEncryptRespMessage(wechatCallbackSignature, companyNo);

            //3.响应消息
            PrintWriter out = response.getWriter();
            out.print(respMessage);
            out.close();
            log.info("第三方应用acceptMessage返回,耗时:{}", System.currentTimeMillis() - s);
        } catch (IOException e) {
            log.error("第三方应用acceptMessage IOException返回:{}", e.getMessage());
        }
    }

    /**
     * @description:第三方应用回调验证，仅用于在应用创建配置应用信息时的验证，企业微信服务端会向回调URL发起一个 Get 请求
     * @param request
     * @param response
     * @param companyNo 企微-企业主体
     * @return void
     * @author: hongdong.xie
     * @date: 2025-08-19 16:57:27
     * @since JDK 1.8
     */
    private void sendVerifySuccess(HttpServletRequest request,
                                   HttpServletResponse response,
                                   String companyNo) {
        String verifyMsgSig = request.getParameter("msg_signature");
        String verifyTimeStamp = request.getParameter("timestamp");
        String verifyNonce = request.getParameter("nonce");
        String verifyEchoStr = request.getParameter("echostr");
        String echoStr;

        log.info("第三方应用GetMapping,签名串:{},时间戳:{},随机串:{},消息体:{}", verifyMsgSig, verifyTimeStamp, verifyNonce, verifyEchoStr);

        try {
            PrintWriter out = response.getWriter();
            WXBizMsgCrypt wxcpt = baseConfigServce.buildWXBizMsgCrypt(companyNo);
            //URL验证
            echoStr = wxcpt.VerifyURL(verifyMsgSig, verifyTimeStamp,
                    verifyNonce, verifyEchoStr);
            log.info("companyNo:{},第三方应用sendVerifySuccess echoStr:{} ", companyNo,echoStr);
            out.print(echoStr);
            out.close();
        } catch (Exception e) {
            //验证URL失败，错误原因请查看异常
            log.error("第三方应用sendVerifySuccess Exception:{}", e.getMessage());
        }
    }

    /**
     * @description:从request中获取第三方应用消息明文
     * @param request
     * @param companyNo 企微-企业主体
     * @return com.howbuy.crm.wechat.service.domain.callback.WechatCallbackSignatureDTO
     * @author: hongdong.xie
     * @date: 2025-08-19 16:57:27
     * @since JDK 1.8
     */
    private WechatCallbackSignatureDTO getDecryptMsg(HttpServletRequest request, String companyNo) {
        // 密文，对应POST请求的数据
        StringBuilder postData = new StringBuilder();
        // 明文，解密之后的结果
        String result = Strings.EMPTY;

        // 微信加密签名
        String signature = request.getParameter("msg_signature");
        String timestamp = request.getParameter("timestamp");
        String nonce = request.getParameter("nonce");

        WechatCallbackSignatureDTO signatureDTO = new WechatCallbackSignatureDTO();
        signatureDTO.setSignature(signature);
        signatureDTO.setTimestamp(timestamp);
        signatureDTO.setNonce(nonce);

        log.info("companyNo:{},第三方应用getDecryptMsg开始,signatureDTO:{}",companyNo, JSON.toJSONString(signatureDTO));
        try {
            //1.获取加密的请求消息：使用输入流获得加密请求消息postData
            ServletInputStream in = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(in));
            //作为输出字符串的临时串，用于判断是否读取完毕
            String tempStr;
            while (null != (tempStr = reader.readLine())) {
                postData.append(tempStr);
            }

            log.info("companyNo:{},第三方应用getDecryptMsg开始,postData:{}", companyNo,postData);

            //2.获取消息明文：对加密的请求消息进行解密获得明文
            WXBizMsgCrypt wxBizMsgCrypt = baseConfigServce.buildWXBizMsgCrypt(companyNo);
            result = wxBizMsgCrypt.DecryptMsg(signature, timestamp, nonce, postData.toString());

            signatureDTO.setResult(result);
        } catch (Exception e) {
            log.error("第三方应用Exception",e);
            log.info("companyNo:{},第三方应用getDecryptMsg开始,IOException:{}",companyNo, e.getMessage());
        }

        return signatureDTO;
    }

    /**
     * @description:错误响应处理
     * @param response
     * @param errorMsg 错误消息
     * @return void
     * @author: hongdong.xie
     * @date: 2025-08-19 16:57:27
     * @since JDK 1.8
     */
    private void responseError(HttpServletResponse response, String errorMsg) {
        try {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            PrintWriter out = response.getWriter();
            out.print(errorMsg);
            out.close();
        } catch (IOException e) {
            log.error("第三方应用响应错误信息失败:{}", e.getMessage());
        }
    }
}