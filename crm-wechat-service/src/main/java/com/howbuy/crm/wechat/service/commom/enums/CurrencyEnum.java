/**
 * Project Name:crm-conscard-client
 * File Name:ConscardConstantEnum.java
 * Package Name:com.howbuy.crm.conscard.base
 * Date:2017年5月26日下午5:59:27
 * Copyright (c) 2017, <EMAIL> All Rights Reserved.
 *
 */

package com.howbuy.crm.wechat.service.commom.enums;

/**
 * Function: 币种  <br/>
 * description: 156-人民币  840-美元 344-港元  978-欧元  392-日元  826-英镑 953-法郎 279-马克  <br/>
 * Date: 2021年10月15日17:43:33<br/>
 */
public enum CurrencyEnum {

	/**
	 * 156-人民币
	 */
	RMB("156", "人民币"),
	/**
	 * 840-美元
	 */
	USD("840", "美元"),
	/**
	 * 344-港元
	 */
	HKD("344", "港元"),

	/**
	 * 978-欧元
	 */
	EUR("978", "欧元"),
	/**
	 * 392-日元
	 */
	JPY("392", "日元"),
	/**
	 * 826-英镑
	 */
	GBP("826", "英镑"),
	/**
	 * 953-法郎
	 */
	FRF("953", "法郎"),
	/**
	 * 279-马克
	 */
	DEM("279", "马克");

	private String code; // 编码

	private String description; // 描述

	private CurrencyEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	/**
	 * 通过code获得
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return description 描述
	 */
	public static String getDescription(String code) {
		CurrencyEnum statusEnum=getEnum(code);
		return statusEnum==null?null :statusEnum.getDescription();
	}

	/**
	 * 通过code直接返回 整个枚举类型
	 * 
	 * @param code
	 *            系统返回参数编码
	 * @return BaseConstantEnum
	 */
	public static CurrencyEnum getEnum(String code) {
		for(CurrencyEnum statusEnum : CurrencyEnum.values()){
			if(statusEnum.getCode().equals(code)){
				return statusEnum;
			}
		}
		return null;
//		return Stream.of(DepositMatchStatusEnum.values()).filter(tmp -> tmp.code.equals(code)).findFirst().orElse(null);
	}


	/**
	 * 通过 description 直接返回 整个枚举类型
	 * @param description
	 * @return
	 */
	public static CurrencyEnum getEnumByDesc(String description) {
		for(CurrencyEnum statusEnum : CurrencyEnum.values()){
			if(statusEnum.getDescription().equals(description)){
				return statusEnum;
			}
		}
		return null;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

}
