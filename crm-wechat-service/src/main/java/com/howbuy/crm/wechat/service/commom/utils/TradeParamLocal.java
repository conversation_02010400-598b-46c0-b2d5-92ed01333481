package com.howbuy.crm.wechat.service.commom.utils;

import java.util.Date;

/**
 * 交易接口公共参数-threadlocal类
 * 
 * ##使用完, 一定要在合适的地方清空交易公共参数
 * 
 * @reason:
 * <AUTHOR>
 * @date 2016年9月29日 上午10:31:05
 * @since JDK 1.7
 */
public class TradeParamLocal {
    private static final ThreadLocal<TradeParamLocal> LOCAL = ThreadLocal.withInitial(() -> new TradeParamLocal());

    /**
     * getTradeParam:(获取TradeParam)
     * 
     * @return TradeParamLocal
     * <AUTHOR>
     * @date 2016年9月29日 下午4:55:36
     */
    public static TradeParamLocal getTradeParam() {
        return LOCAL.get();
    }

    /**
     * removeTradeParam:(移除TradeParam)
     * 
     * <AUTHOR>
     * @date 2016年9月29日 下午5:02:04
     */
    public static void removeTradeParam() {
        LOCAL.remove();
    }

    /**
     * 交易渠道号
     */
    private String txChannel;
    /**
     * 分销渠道号
     */
    private String disCode;
    /**
     * 网点号: app应用市场
     */
    private String outletCode;
    /**
     * 当前时间
     */
    private Date currDate;
    /**
     * 接口申请日期
     */
    private String appDt;
    /**
     * 接口申请时间
     */
    private String appTm;

    public static String getTxChannel() {
        return TradeParamLocal.getTradeParam().txChannel;
    }

    public static void setTxChannel(String txChannel) {
        TradeParamLocal.getTradeParam().txChannel = txChannel;
    }

    public static String getDisCode() {
        return TradeParamLocal.getTradeParam().disCode;
    }

    public static void setDisCode(String disCode) {
        TradeParamLocal.getTradeParam().disCode = disCode;
    }

    public static String getOutletCode() {
        return TradeParamLocal.getTradeParam().outletCode;
    }

    public static void setOutletCode(String outletCode) {
        TradeParamLocal.getTradeParam().outletCode = outletCode;
    }

    public static Date getCurrDate() {
        return TradeParamLocal.getTradeParam().currDate;
    }

    public static void setCurrDate(Date currDate) {
        TradeParamLocal.getTradeParam().currDate = currDate;
    }

    public static String getAppDt() {
        return TradeParamLocal.getTradeParam().appDt;
    }

    public static void setAppDt(String appDt) {
        TradeParamLocal.getTradeParam().appDt = appDt;
    }

    public static String getAppTm() {
        return TradeParamLocal.getTradeParam().appTm;
    }

    public static void setAppTm(String appTm) {
        TradeParamLocal.getTradeParam().appTm = appTm;
    }

}
