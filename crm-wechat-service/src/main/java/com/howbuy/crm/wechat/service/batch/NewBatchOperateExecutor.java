package com.howbuy.crm.wechat.service.batch;

import com.howbuy.crm.wechat.dao.vo.PageVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 批量操作执行器，提供批量操作的基础方法，业务方只需要实现对应的接口即可，然后调用对应的方法即可
 * <AUTHOR>
 * @date 2023/10/7 9:59
 * @since JDK 1.8
 */
@Service
@SuppressWarnings("all")
public class NewBatchOperateExecutor {
    @Resource
    private BatchOperateExecutor batchOperateExecutor;

    public <P> int businessExecute(List<P> persistList, BatchPersistService<P> callbackService) {
        return this.batchOperateExecutor.businessExecute(persistList, callbackService);
    }

    public <P> int batchExecute(int perSize, List<P> persistList, BatchPersistService<P> callbackService) {
        return this.batchOperateExecutor.batchExecute(perSize, persistList, callbackService);
    }

    public <V extends PageVo, P> int queryBeforeExecute(V queryVo, QueryPageService<V, P> callbackService) {
        return this.batchOperateExecutor.queryBeforeExecute(queryVo, callbackService);
    }

    public <V extends PageVo, P> int poolExecute(final V queryVo, final QueryPageService<V, P> callbackService) {
        return this.batchOperateExecutor.poolExecute(queryVo, callbackService);
    }

    public <V extends PageVo, P> void poolExecute(final V queryVo, final QueryPageService<V, P> callbackService, int poolSize) {
        this.batchOperateExecutor.poolExecute(queryVo, callbackService, poolSize);
    }

    public <V extends PageVo, P> List<P> pageQuery(V queryVo, PageQueryService<V, P> pageQueryService) {
        return this.batchOperateExecutor.pageQuery(queryVo, pageQueryService);
    }
}
