/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.domain.message;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (crm接受消息 对象)
 * <AUTHOR>
 * @date 2023/10/9 15:26
 * @since JDK 1.8
 */
@Data
public class AcceptMessageJobDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息类型
     */
    private String type;

    /**
     * 消息参数 json格式
     */
    private String params;

    /**
     * 消息唯一标识 -业务唯一标识
     */
    private String uniqueId;

    /**
     * 消息 hboneNo -一账通号
     */
    private String hbOneNo;

    /**
     * 消息模版id
     */
    private String templateId;

    /**
     * 消息模版id 对应的消息参数
     */
    private String templateParams;

}