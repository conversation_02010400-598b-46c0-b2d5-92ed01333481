/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.domain.message;

import java.io.Serializable;

/**
 * @description: 更新消息发送状态
 * <AUTHOR>
 * @date 2024/10/30 11:13
 * @since JDK 1.8
 */

public class UpdateMessageSendStatusDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * SEND_STATUS
     * 发送状态， 1-未推送；2-推送中；3-推送成功；4-推送失败；5-重新推送
     */
    private String sendStatus;

    /**
     * RESPONSE_CODE
     * 接口返回码
     */
    private String responseCode;

    /**
     * RESPONSE_MSG
     * 接口返回信息
     */
    private String responseMsg;

    public String getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(String sendStatus) {
        this.sendStatus = sendStatus;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public String getResponseMsg() {
        return responseMsg;
    }

    public void setResponseMsg(String responseMsg) {
        this.responseMsg = responseMsg;
    }
}