/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.business;

import com.howbuy.cc.message.SendMsgResult;
import com.howbuy.cc.message.SendMsgService;
import com.howbuy.cc.message.email.*;
import com.howbuy.cc.message.model.CompanyAttachment;
import com.howbuy.cc.message.phone.SendShortMsgByPhoneResponse;
import com.howbuy.cc.message.phone.SendShortMsgByPhoneService;
import com.howbuy.cc.message.phone.SendShortMsgByTemplateRequest;
import com.howbuy.cc.message.request.SendAppMessageByTemplateRequest;
import com.howbuy.cc.message.request.SendCompanyCommonRequest;
import com.howbuy.cc.message.send.auto.request.SendMsgByHboneNoRequest;
import com.howbuy.cc.message.send.auto.response.SendMsgByHboneNoResponse;
import com.howbuy.cc.message.send.auto.service.AutoSendMessageService;
import com.howbuy.cc.message.send.company.CompanySendService;
import com.howbuy.cc.message.send.company.CompanyTaskRequest;
import com.howbuy.cc.message.send.company.SelfAppMessageRequest;
import com.howbuy.cc.message.send.company.SendBotMessageRequest;
import com.howbuy.crm.http.service.WebChatService;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.commom.utils.CorpUtilityDTO;
import com.howbuy.crm.wechat.service.service.config.BaseConfigServce;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/11/3 13:55
 * @since JDK 1.8
 */

@Slf4j
@Component
public class CompanySendBusiness {
    @DubboReference(registry = Constants.ZK_MESSAGE_PUBLIC, check = false)
    private CompanySendService companySendService;

    @DubboReference(registry = Constants.ZK_MESSAGE_PUBLIC, check = false)
    private SendMsgService sendMsgService;

    @DubboReference(registry = Constants.ZK_CRM_TD, check = false)
    private WebChatService webChatService;

    @DubboReference(registry = Constants.ZK_MESSAGE_PUBLIC, check = false)
    private SendEmailService sendEmailService;


    @DubboReference(registry = Constants.ZK_MESSAGE_PUBLIC, check = false)
    private SendShortMsgByPhoneService sendShortMsgByPhoneService;

    @DubboReference(registry = Constants.ZK_MESSAGE_PUBLIC, check = false)
    private SendEmailWithAttachSupportEncryService emailWithAttachService;

    @Autowired
    private BaseConfigServce baseConfigServce;

    //    ASSET_BACK_URL
    @Value("${ASSET_BACK_URL}")
    private String ASSET_BACK_URL;

    @Value("${ASSET_PIC_URL}")
    private String ASSET_PIC_URL;

    public String sendGroupMessage(String messageType,
                                   String title,
                                   String wechatConsCode,
                                   String url,
                                   List<String> custNos,
                                   String messageContent,
                                   String asseetId){
        CompanyTaskRequest companyTaskRequest = new CompanyTaskRequest();
        companyTaskRequest.setMessageType(Integer.valueOf(messageType));
        companyTaskRequest.setTitle(title);
        companyTaskRequest.setUrl(url);
        companyTaskRequest.setCreateUser(wechatConsCode);
        companyTaskRequest.setCustNos(custNos);
        companyTaskRequest.setMessageContent(messageContent);
        Long taskId = companySendService.createCompanyTask(companyTaskRequest);

        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("materialParentType", "3");
        paramsMap.put("materialId", asseetId);

        // 获取企微转发的url
        String generatedUrl = webChatService.getGeneratedUrl(ASSET_BACK_URL, paramsMap);
        if(taskId != null){
            SendCompanyCommonRequest sendCompanyCommonRequest = new SendCompanyCommonRequest();
            sendCompanyCommonRequest.setTaskId(taskId);
            sendCompanyCommonRequest.setCustNos(custNos);
            sendCompanyCommonRequest.setTitle(title);
            sendCompanyCommonRequest.setUrl(generatedUrl);
            sendCompanyCommonRequest.setMessageType(Integer.valueOf(messageType));
            sendCompanyCommonRequest.setConsCode(wechatConsCode);
            sendCompanyCommonRequest.setMessageContent(messageContent);
            //实际是 发送给 好买财富-客户联系
            //固定发送给 【好买财富】
            CorpUtilityDTO corpUtilityDTO= baseConfigServce.getCorpUtilityDTO(Constants.DEFAULT_COMPANY_NO);
            sendCompanyCommonRequest.setSecret(corpUtilityDTO.getCustomerSecret());
            sendCompanyCommonRequest.setAttachments(buildAttachMent(messageType, title, generatedUrl));
            companySendService.sendCompanyCommonMsg(sendCompanyCommonRequest);
            return taskId.toString();
        }
        return null;
    }

    /**
     * @description:(拼接处理附件数据)
     * @param messageType
     * @param title
     * @param url
     * @return java.util.List<com.howbuy.cc.message.model.CompanyAttachment>
     * @author: xufanchao
     * @date: 2025/8/11 16:32
     * @since JDK 1.8
     */
    private List<CompanyAttachment> buildAttachMent(String messageType, String title, String url) {
        List<CompanyAttachment> attachments = new ArrayList<>();
        CompanyAttachment attachment = new CompanyAttachment();
        attachment.setTitle(title);
        attachment.setMsgType(messageType);
        attachment.setUrl(url);
        attachment.setDesc("请查收您的专属IPS报告。");
        attachment.setPicUrl(ASSET_PIC_URL);
        attachment.setMsgType("link");
        attachments.add(attachment);
        return attachments;
    }

    @DubboReference(registry = Constants.ZK_MESSAGE_PUBLIC, check = false)
    private AutoSendMessageService autoSendMessageService;


    /**
     * @description:(请在此添加描述)
     * @param request
     * @return com.howbuy.cc.message.SendMsgResult
     * @author: jin.wang03
     * @date: 2023/11/3 13:57
     * @since JDK 1.8
     */
    public SendMsgResult sendBotMessage(SendBotMessageRequest request) {
        return companySendService.sendBotMessage(request);
    }

    /**
     * @description 自建应用发送企微消息
     * @param selfAppMessageRequest
     * @return
     * <AUTHOR>
     * @date 2023/11/29 10:48 AM
     * @since JDK 1.8
     */
    public SendMsgResult sendNewApplicationMsg(SelfAppMessageRequest selfAppMessageRequest) {
        return companySendService.sendSelfBuiltApplicationMessage(selfAppMessageRequest);
    }


    /**
     * @description: 发送邮件
     * @param selfAppMessageRequest
     * @return com.howbuy.cc.message.email.SendEmailResponse
     * @author: jin.wang03
     * @date: 2024/5/27 18:11
     * @since JDK 1.8
     */
    public SendEmailResponse sendEmailMsg(SendEmailByTemplateRequest selfAppMessageRequest) {
        return sendEmailService.sendEmailByTemplate(selfAppMessageRequest);
    }


    /**
     * @description: 发送好买基金APP内消息
     * @param appMessageRequest
     * @return com.howbuy.cc.message.SendMsgResult
     * @author: jin.wang03
     * @date: 2025/6/19 15:50
     * @since JDK 1.8
     */
    public SendMsgResult sendHbApp(SendAppMessageByTemplateRequest appMessageRequest) {
        return sendMsgService.sendAppMessageByTemplate(appMessageRequest);
    }


    /**
     * @description: 发送自动渠道
     * @param appMessageRequest
     * @return com.howbuy.cc.message.send.auto.response.SendMsgByHboneNoResponse
     * @author: jin.wang03
     * @date: 2025/6/19 18:01
     * @since JDK 1.8
     */
    public SendMsgByHboneNoResponse sendAutoChannel(SendMsgByHboneNoRequest appMessageRequest) {
        return autoSendMessageService.sendMsgByHboneNo(appMessageRequest);
    }

    /**
     * @description: 发送短信
     * @param selfAppMessageRequest
     * @return com.howbuy.cc.message.phone.SendShortMsgByPhoneResponse
     * @author: jin.wang03
     * @date: 2024/5/27 18:12
     * @since JDK 1.8
     */
    public SendShortMsgByPhoneResponse sendSmsMsg(SendShortMsgByTemplateRequest selfAppMessageRequest) {
        return sendShortMsgByPhoneService.sendMsgByTemplate(selfAppMessageRequest);
    }

    /**
     * @description: 发送邮件带附件
     * @param request
     * @return com.howbuy.cc.message.email.SendEmailResponse
     * @author: jin.wang03
     * @date: 2024/10/28 15:37
     * @since JDK 1.8
     */
    public SendEmailResponse sendEmailWithAttachmentMsg(SendEmailWithAttachSupportEncryRequest request) {
        return emailWithAttachService.execute(request);
    }

}