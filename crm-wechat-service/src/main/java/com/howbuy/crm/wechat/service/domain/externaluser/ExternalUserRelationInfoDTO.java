package com.howbuy.crm.wechat.service.domain.externaluser;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @classname: ExternalUserInfoDTO
 * @author: yu.zhang
 * @description:  外部客户 vs 企业微信成员  绑定关系
 * @creatdate: 2021-02-08 16:48
 * @since: JDK1.8
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ExternalUserRelationInfoDTO implements Serializable {

    /**
     * 添加了此外部联系人的企业成员userid
     */
    private String userid;
    /**
     * 外部联系人的userid
     */
    private String externalUserId;
    /**
     * 企业成员-->外部联系人
     * 备注
     */
    private String remark;
    /**
     * 企业成员-->外部联系人
     * 描述
     */
    private String description;
    /**
     * 企业成员-->外部联系人
     * 时间
     */
    private Date createtime;
    /**
     * 企业成员-->外部联系人
     * 备注的企业名称
     */
    private String remarkCorpName;
    /**
     * 企业成员-->外部联系人
     * 备注的手机号码
     * 代开发自建应用需要管理员授权才可以获取，第三方不可获取
     */
    private List<String> remarkMobiles;
    /**
     * 企业成员-->外部联系人
     * 来源
     */
    private String addWay;
    /**
     * 企业成员-->外部联系人
     * 发起添加的userid
     * 如果 企业成员 主动添加，为成员的userid；
     * 如果是 客户 主动添加，则为客户的外部联系人 externalUserId；
     * 如果是内部成员共享/管理员分配，则为对应的成员/管理员userid
     */
    private String operUserid;
    /**
     * 企业成员-->外部联系人
     * 企业自定义的state参数，
     * 用于区分客户具体是通过哪个「联系我」添加，由企业通过创建「联系我」方式指定
     */
    private String state;

    /**
     * 企业成员-->外部联系人
     * 打标签的分组名称
     */
    private List<UserRelationTagInfoDTO> tagList;

    /**
     *
     * @param userid  企业成员用户Id  NOT NULL
     * @param externalUserId  外部联系人id NOT NULL
     */
    public ExternalUserRelationInfoDTO(String userid, String externalUserId) {
        this.userid = userid;
        this.externalUserId = externalUserId;
    }
}
