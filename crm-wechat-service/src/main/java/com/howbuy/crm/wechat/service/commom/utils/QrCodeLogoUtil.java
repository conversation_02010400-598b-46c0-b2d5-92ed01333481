/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.utils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;
import java.util.Base64;

/**
 * <AUTHOR>
 * @description: 二维码添加logo例子
 * @date 2024/9/20 13:20
 * @since JDK 1.8
 */
public class QrCodeLogoUtil {

    public static void main(String[] args) {
        try {
            String s = transferToBase64("https://open.work.weixin.qq.com/wwopen/userQRCode?vcode=vc6aa76259aa2f1ff3",
                    "https://wework.qpic.cn/wwpic3az/729045_FlpvL5DbQIGdv9W_1718708447/100");
            System.out.println(s);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * @description:(把企微url地址转成base64字符串)
     * @param qrCodeUrl
     * @return java.lang.String
     * @author: xufanchao
     * @date: 2025/8/4 10:47
     * @since JDK 1.8
     */
    public static String transferToBase64(String qrCodeUrl) throws Exception {
        // 二维码图片
        URL url = new URL(qrCodeUrl);
        // 读取二维码图片
        BufferedImage qrCodeImage = ImageIO.read(url);
        return bufferedImageToBase64(qrCodeImage);
    }



    /**
     * @param qrCodeUrl      企微二维码http地址
     * @param thumbAvatarUrl 企微头像http地址
     * @return java.lang.String
     * @description: 将企微头像放置在企微二维码的中间，并把图片转成base64字符串
     * @author: jin.wang03
     * @date: 2024/9/20 15:14
     * @since JDK 1.8
     */
    public static String transferToBase64(String qrCodeUrl, String thumbAvatarUrl) throws Exception {
        // 二维码图片
        URL url = new URL(qrCodeUrl);
        // 读取二维码图片
        BufferedImage qrCodeImage = ImageIO.read(url);
        // 读取logo
        URL logoUrl = new URL(thumbAvatarUrl);
        // 读取图片
        BufferedImage logoImage = ImageIO.read(logoUrl);
        // 合并图片
        BufferedImage bufferedImage = encodeImgLogo(qrCodeImage, logoImage);

        return bufferedImageToBase64(bufferedImage);
    }

    /**
     * @description: 将图片转成base64字符串
     * @param image 图片
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2024/9/20 15:38
     * @since JDK 1.8
     */
    public static String bufferedImageToBase64(BufferedImage image) throws IOException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ImageIO.write(image, "png", bos);
        byte[] imageBytes = bos.toByteArray();

        return Base64.getEncoder().encodeToString(imageBytes);
    }

    /**
     * @param qrCodeImg 二维码图片
     * @param logoImg 头像图片
     * @return java.awt.image.BufferedImage
     * @description: 将 二维码图片 和 头像图片 合并
     * @author: jin.wang03
     * @date: 2024/9/20 15:34
     * @since JDK 1.8
     */
    public static BufferedImage encodeImgLogo(BufferedImage qrCodeImg, BufferedImage logoImg) {
        //获取画笔
        Graphics2D g = qrCodeImg.createGraphics();
        //读取logo图片
        //设置二维码大小，太大，会覆盖二维码，此处30%
        int logoWidth = Math.min(logoImg.getWidth(null), qrCodeImg.getWidth() * 3 / 10);
        int logoHeight = Math.min(logoImg.getHeight(null), qrCodeImg.getHeight() * 3 / 10);
        // 确定二维码的中心位置坐标，设置logo图片放置的位置
        int x = (qrCodeImg.getWidth() - logoWidth) / 2;
        int y = (qrCodeImg.getHeight() - logoHeight) / 2;
        //开始合并绘制图片
        g.drawImage(logoImg, x, y, logoWidth, logoHeight, null);
        g.drawRoundRect(x, y, logoWidth, logoHeight, 15, 15);
        //logo边框大小
        g.setStroke(new BasicStroke(2));
        //logo边框颜色
        g.setColor(Color.WHITE);
        g.drawRect(x, y, logoWidth, logoHeight);
        g.dispose();
        logoImg.flush();
        qrCodeImg.flush();

        return qrCodeImg;
    }

}