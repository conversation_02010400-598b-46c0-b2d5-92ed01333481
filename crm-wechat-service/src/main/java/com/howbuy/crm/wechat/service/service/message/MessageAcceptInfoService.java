package com.howbuy.crm.wechat.service.service.message;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.howbuy.crm.wechat.client.enums.AcceptMessageStatusEnum;
import com.howbuy.crm.wechat.dao.mapper.message.MessageAcceptInfoMapper;
import com.howbuy.crm.wechat.dao.po.message.MessageAcceptInfoPO;
import com.howbuy.crm.wechat.dao.vo.message.MessageAcceptInfoVO;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/10/7 9:59
 * @since JDK 1.8
 */
@Service
public class MessageAcceptInfoService{

    @Resource
    private MessageAcceptInfoMapper messageAcceptInfoMapper;

    
    public int deleteByPrimaryKey(Long id) {
        return messageAcceptInfoMapper.deleteByPrimaryKey(id);
    }

    
    public int insert(MessageAcceptInfoPO record) {
        record.setCreateTime(new Date());
        return messageAcceptInfoMapper.insert(record);
    }


    
    public MessageAcceptInfoPO selectByPrimaryKey(Long id) {
        return messageAcceptInfoMapper.selectByPrimaryKey(id);
    }

    /**
     * 根据业务唯一标识查询
     * @param messageType
     * @param uniqueId
     * @return
     */
    public MessageAcceptInfoPO selectByUniqueId(String messageType,String  uniqueId){
        return messageAcceptInfoMapper.selectByUniqueId(messageType,uniqueId);
    }

    
    public int updateByPrimaryKeySelective(MessageAcceptInfoPO record) {
        return messageAcceptInfoMapper.updateByPrimaryKeySelective(record);
    }

    
    public int updateByPrimaryKey(MessageAcceptInfoPO record) {
        return messageAcceptInfoMapper.updateByPrimaryKey(record);
    }

    /**
     * 分页查询待构建消息
     * @param acceptInfoVO
     * @return
     */
    public Page<MessageAcceptInfoPO> selectPageByVo(MessageAcceptInfoVO acceptInfoVO){
        PageHelper.startPage(acceptInfoVO.getPage(), acceptInfoVO.getRows());
        return messageAcceptInfoMapper.selectPageByVo(acceptInfoVO);
    }


    /**
     * @description: (根据消息类型和业务唯一标识查询)
     * @param messageType 消息类型
     * @param uniqueIdList 业务唯一标识list
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.message.MessageAcceptInfoPO> 接收到的消息list
     * @author: jin.wang03
     * @date: 2024/3/22 14:07
     * @since JDK 1.8
     */
    public List<MessageAcceptInfoPO> selectByTypeAndUniqueIdList(String messageType, List<String> uniqueIdList) {
        return messageAcceptInfoMapper.selectByTypeAndUniqueIdList(messageType, uniqueIdList);
    }

    /**
     * 批量 更新  消息构建状态
     * @param statusEnum
     * @param idList
     * @return
     */
    public int batchUpdateStatus(AcceptMessageStatusEnum statusEnum, List<Long> idList){
        Assert.notNull(statusEnum,"statusEnum is null");
        Assert.notEmpty(idList,"idList is empty");
        return messageAcceptInfoMapper.batchUpdateStatus(statusEnum.getCode(),idList);
    }

}
