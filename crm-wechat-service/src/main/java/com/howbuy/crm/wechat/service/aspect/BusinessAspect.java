/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.aspect;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.client.producer.BaseRequest;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.commom.exception.BusinessException;
import com.howbuy.crm.wechat.service.commom.utils.DateUtils;
import com.howbuy.crm.wechat.service.commom.utils.LoggerUtils;
import com.howbuy.crm.wechat.service.commom.utils.MainLogUtils;
import net.sf.oval.Validator;
import net.sf.oval.exception.ConstraintsViolatedException;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * @description: 业务处理切面(DUBBO接口用)
 * <AUTHOR>
 * @date 2023/3/15 11:28
 * @since JDK 1.8
 */
@Component
@Aspect
public class BusinessAspect {
    private static final Logger log = LogManager.getLogger(BusinessAspect.class);
    private static final Logger mainLogger = LogManager.getLogger("mainlog");

    @Pointcut("execution(* com.howbuy.crm.wechat.service.exposeimpl..*.*(..)) || execution(* com.howbuy.crm.wechat.service.facade..*.*(..))")
    public void entryPoint() {
    }

    @Around("entryPoint()")
    public Object around(ProceedingJoinPoint pjp) {
        LoggerUtils.setReqId(getReqId());
        LoggerUtils.setRanNo(LoggerUtils.createRanNo());
        long start = System.currentTimeMillis();
        // 获取方法参数
        Object[] args = pjp.getArgs();
        Signature signature = pjp.getSignature();
        String signatureStr = signature.toShortString();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        BaseRequest request = null;
        Response<Object> response = null;
        String name = (null!= method.getDeclaringClass()) ? method.getDeclaringClass().getName():"";
        String methodName = method.getName();
        try {
            if(log.isInfoEnabled()){
                log.info("BusinessAspect|class:{},methodName:{},args:{}",name, methodName, JSON.toJSONString(args));
            }
            if (args != null && args.length > 0 && args[0] instanceof BaseRequest) {
                request = (BaseRequest) args[0];
                Validator validator = new Validator();
                validator.assertValid(request);
            }
            response = (Response) pjp.proceed();
        }catch (ConstraintsViolatedException e){
            //参数校验异常
            log.error("BusinessAspect|msg:{}", e.getMessage());
            response = new Response<>();
            response.setCode(ResponseCodeEnum.PARAM_ERROR.getCode());
            response.setDescription(e.getMessage());
            return response;
        } catch (BusinessException e) {
            log.error("BusinessAspect|msg:{}",e.getExceptionDesc(), e);
            response = new Response<Object>();
            response.setCode(e.getExceptionCode());
            response.setDescription(e.getExceptionDesc());
        } catch (Throwable e) {
            log.error("BusinessAspect|msg:{}",e.getMessage(), e);
            response = new Response<Object>();
            response.setCode(ResponseCodeEnum.SYS_ERROR.getCode());
            response.setDescription(e.getMessage());
        } finally {
            if(log.isInfoEnabled()){
                log.info("BusinessAspect|response:{}", JSON.toJSONString(response));
            }
            long cost = System.currentTimeMillis() - start;
            log.info("cost:{}", cost);
            // 记录main.log
            printMainLog(null != response ? response.getCode() : ResponseCodeEnum.SUCCESS.getCode(), signatureStr, cost);
            LoggerUtils.clearConfig();
        }

        LoggerUtils.clearConfig();
        return response;
    }

    /**
     * @description: 打印main.log
     * @param
     * @return java.lang.String
     * @throws
     * @since JDK 1.8
     */
    private void printMainLog(String returnCode, String signatureStr, long time) {
        if (mainLogger.isInfoEnabled()) {
            try {
                String reqId = LoggerUtils.getReqId();
                String ranNo = LoggerUtils.getRanNo();
                MainLogUtils.dubboCallIn(reqId, ranNo, signatureStr, returnCode, time);
            } catch (Exception e) {
                log.error("LogAspect printMainLog error.|methodName:{}", signatureStr);
                log.error("", e);
            }
        }
    }

    /**
     * @description:获取当前日期
     * @param
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2023/3/15 13:32
     * @since JDK 1.8
     */
    private String getTimeStr() {
        return DateUtils.formatToString(LocalDateTime.now(), DateUtils.YYYY_MM_DD_HH_MM_SS_SSS);
    }

    /**
     * @description: 获取请求ID
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2024/8/31 13:52
     * @since JDK 1.8
     */
    private String getReqId() {
        // dubbo接口调用
        String reqId = (RpcContext.getServerAttachment()==null) ? null : RpcContext.getServerAttachment().getAttachment(Constants.REQ_ID);
        if (StringUtils.isEmpty(reqId)) {
            // http接口调用
            reqId = LoggerUtils.getUuid();
            // 如果都没有则生成一个
            if (StringUtils.isEmpty(reqId)) {
                reqId = UUID.randomUUID().toString().replace("-", "");
            }
        }
        return reqId;
    }
}