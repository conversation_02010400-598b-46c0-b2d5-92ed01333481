/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.repository;

import com.howbuy.crm.wechat.dao.mapper.CmWechatGroupUserMapper;
import com.howbuy.crm.wechat.dao.po.CmWechatGroupUserPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/10/26 14:06
 * @since JDK 1.8
 */

@Repository
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class CmWechatGroupUserRepository {

    @Autowired
    private CmWechatGroupUserMapper cmWechatGroupUserMapper;

    /**
     * @description: 根据客户群id查询客户群成员列表
     * @param chatId 客户群id
     * @param  userChatFlag 群成员状态 0在群 1退群,null 全部
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatGroupUserPo> 客户群成员列表
     * @author: jin.wang03
     * @date: 2023/10/26 15:34
     * @since JDK 1.8
     */
    public List<CmWechatGroupUserPO> listByChatId(String chatId,String userChatFlag) {
        return cmWechatGroupUserMapper.listByChatId(chatId,userChatFlag);
    }

    /**
     * @description: 批量插入
     * @param bathInsertList 批量插入的数据
     * @author: jin.wang03
     * @date: 2023/10/26 15:35
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void batchInsert(List<CmWechatGroupUserPO> bathInsertList) {
        cmWechatGroupUserMapper.batchInsert(bathInsertList);
    }

    /**
     * @description: 根据客户群id和用户id更新数据
     * @param cmWechatGroupUserPo 更新数据
     * @author: jin.wang03
     * @date: 2023/10/26 15:37
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateByChatIdAndUserId(CmWechatGroupUserPO cmWechatGroupUserPo) {
        cmWechatGroupUserMapper.updateByChatIdAndUserId(cmWechatGroupUserPo);
    }

    /**
     * @description: 根据客户群id和用户id删除数据
     * @param batchDeleteList 批量删除的数据
     * @author: jin.wang03
     * @date: 2023/10/27 9:27
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void batchDeleteByChatIdAndUserId(String chatId, List<String> batchDeleteList, Date leaveTime) {
        cmWechatGroupUserMapper.batchDelete(chatId, batchDeleteList, leaveTime);
    }


    /**
     * @param chatId 批量删除的数据
     * @description: 根据客户群id删除数据，如果leave_time字段为空，则设置成date
     * @author: jin.wang03
     * @date: 2023/10/27 9:27
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int deleteByChatId(String chatId) {
        Date date = new Date();
        return cmWechatGroupUserMapper.deleteByChatId(chatId,date);
    }

    /**
     * @description:(有效的群成员)
     * @param chatId
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatGroupUserPO>
     * @author: shuai.zhang
     * @date: 2024/5/28 11:32
     * @since JDK 1.8
     */
    public List<CmWechatGroupUserPO> listByChatIdEffective(String chatId) {
        return cmWechatGroupUserMapper.listByChatIdEffective(chatId);
    }
}