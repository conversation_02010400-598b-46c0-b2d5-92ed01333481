package com.howbuy.crm.wechat.service.service.message.build;

import com.howbuy.crm.wechat.client.enums.AcceptMessageTypeEnum;
import com.howbuy.crm.wechat.dao.po.message.MessageAcceptInfoPO;

import java.util.List;

/**
 * @description: (消息处理 接口 )
 * <AUTHOR>
 * @date 2023/10/7 9:59
 * @since JDK 1.8
 */
public interface MessageBuildService {


    /**
     * @description: (获取支持的 消息类型)
     * @return
     * @return: java.util.List<com.howbuy.crm.wechat.client.enums.AcceptMessageTypeEnum>
     * @since JDK 1.8
     */
    List<AcceptMessageTypeEnum> getMsgTypeList();

    
    /*
     * @description:(根据消息，构建待发送消息)
     * @param acceptInfoPO	
     * @return void
     * @author: haoran.zhang
     * @date: 2023/10/7 16:35
     * @since JDK 1.8
     */
    void buildMessage(MessageAcceptInfoPO acceptInfo);


    /**
     * @description:构建待发送消息
     * @param acceptInfoList 消息接收信息列表
     * @return void
     * @throws
     * @since JDK 1.8
     */
    void buildMessageList(List<MessageAcceptInfoPO> acceptInfoList);



}
