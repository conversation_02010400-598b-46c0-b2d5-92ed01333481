/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.outerservice.crm;

import com.google.common.collect.Lists;
import com.howbuy.crm.account.client.request.consultantinfo.ConsultantWechatInfoRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultantinfo.CmConsultantInfo;
import com.howbuy.crm.account.client.response.consultantinfo.ConsultantWechatInfoRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;
import com.howbuy.crm.wechat.service.commom.constant.CrmAccountPathConstant;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @description: 用CRM账户服务接口实现类
 * <AUTHOR>
 * @date ********
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CrmAccountOuterService extends AbstractCrmAccountOuterService {
    /**
     * @description:(查询所有需要刷新客户关系的投顾企微账号)
     * @param
     * @return java.util.List<java.lang.String>
     * @author: shuai.zhang
     * @date: 2024/3/27 14:05
     * @since JDK 1.8
     */
    public List<String> getAllNeedRefreshWechatConsCode() {
        Response<List<String>> resp
                = getEntity(CrmAccountPathConstant.GET_ALL_NEEDREFRESH_WECHATCONSCODE,
                new ParameterizedTypeReference<Response<List<String>>>() {
                });
        if(resp.isSuccess()){
            return resp.getData();
        }
        return null;
    }

    /**
     * @description:(查询入参的投顾账号对应的企微账号)
     * @param conscodeList
     * @return java.util.List<com.howbuy.crm.account.client.response.consultantinfo.CmConsultantInfo>
     * @author: shuai.zhang
     * @date: 2024/3/28 15:12
     * @since JDK 1.8
     */
    public List<CmConsultantInfo> getWechatConscodesByConscodes(List<String> conscodeList) {
        ConsultantWechatInfoRequest request = new ConsultantWechatInfoRequest();
        request.setConsCodeList(conscodeList);
        Response<ConsultantWechatInfoRespVO> resp
                = postEntity(CrmAccountPathConstant.GET_WECHATCONSCODES_BYCONSCODES,request,
                new ParameterizedTypeReference<Response<ConsultantWechatInfoRespVO>>() {
                });
        if(resp.isSuccess()&& Objects.nonNull(resp.getData())){
            return resp.getData().getConsultantWechatInfoList();
        }
        return Lists.newArrayList();
    }
}