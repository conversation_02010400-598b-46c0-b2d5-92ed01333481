/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.business.syncchatgroup;

import com.howbuy.crm.wechat.dao.po.CmWechatGroupPO;
import com.howbuy.crm.wechat.service.commom.utils.ListUtil;
import com.howbuy.crm.wechat.service.domain.externaluser.ExternalGroupInfoDTO;
import com.howbuy.crm.wechat.service.repository.CmWechatGroupRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description: 同步群组信息
 * <AUTHOR>
 * @date 2023/10/30 14:20
 * @since JDK 1.8
 */

@Component
@Slf4j
public class SyncChatGroupBusiness {

    @Autowired
    private CmWechatGroupRepository cmWechatGroupRepository;

    /**
     * @description: 将企微中查询到的客户群列表同步到数据库
     * @param chatGroupList	客户群列表
     * @author: jin.wang03
     * @date: 2023/10/25 18:17
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class)
    public void persistChatGroupList(String companyNo, List<ExternalGroupInfoDTO> chatGroupList) {
        List<CmWechatGroupPO> batchInsertList = new ArrayList<>();
        for (ExternalGroupInfoDTO groupInfoDTO : chatGroupList) {
            String chatId = groupInfoDTO.getChatId();
            String status = groupInfoDTO.getStatus();
            CmWechatGroupPO cmWechatGroupPo = cmWechatGroupRepository.getByChatId(chatId,companyNo);
            if (cmWechatGroupPo == null) {
                cmWechatGroupPo = new CmWechatGroupPO();
                cmWechatGroupPo.setChatId(chatId);
                cmWechatGroupPo.setStatus(status);
                cmWechatGroupPo.setCompanyNo(companyNo);
                cmWechatGroupPo.setCreateTime(new Date());
                batchInsertList.add(cmWechatGroupPo);
            } else {
                if (!StringUtils.equals(cmWechatGroupPo.getStatus(), status)) {
                    cmWechatGroupPo = new CmWechatGroupPO();
                    cmWechatGroupPo.setChatId(chatId);
                    cmWechatGroupPo.setStatus(status);
                    cmWechatGroupPo.setUpdateTime(new Date());
                    cmWechatGroupRepository.updateByChatId(cmWechatGroupPo);
                }
            }
        }

        // 批量新增
        if (!CollectionUtils.isEmpty(batchInsertList)) {
            int insertCount=0;
            List<List<CmWechatGroupPO>> dataGroup = ListUtil.splitList(batchInsertList, 500);
            for (List<CmWechatGroupPO> group : dataGroup) {
                insertCount += cmWechatGroupRepository.batchInsert(group);
            }
            log.info("companyNo:{}, 批量新增客户群群数据：{}条", companyNo,insertCount);
        }
    }

}