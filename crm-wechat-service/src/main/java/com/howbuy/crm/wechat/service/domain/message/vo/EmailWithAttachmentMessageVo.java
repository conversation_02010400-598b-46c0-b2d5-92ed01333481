/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.domain.message.vo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (邮箱 发送消息 - 参数)
 * @date 2023/10/12 10:23
 * @since JDK 1.8
 */

public class EmailWithAttachmentMessageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 接收人邮箱地址密文
     */
    private String encryEmail;

    /**
     * 标题
     */
    private String emailTitle;

    /**
     * 附件
     */
    private List<EmailAttachmentVO> attachmentList;


    /**
     * 模板内容
     */
    private String templateParams;

    public String getEncryEmail() {
        return encryEmail;
    }

    public void setEncryEmail(String encryEmail) {
        this.encryEmail = encryEmail;
    }

    public String getEmailTitle() {
        return emailTitle;
    }

    public void setEmailTitle(String emailTitle) {
        this.emailTitle = emailTitle;
    }

    public List<EmailAttachmentVO> getAttachmentList() {
        return attachmentList;
    }

    public void setAttachmentList(List<EmailAttachmentVO> attachmentList) {
        this.attachmentList = attachmentList;
    }

    public String getTemplateParams() {
        return templateParams;
    }

    public void setTemplateParams(String templateParams) {
        this.templateParams = templateParams;
    }
}