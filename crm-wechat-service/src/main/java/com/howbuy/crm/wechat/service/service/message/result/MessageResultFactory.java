/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.message.result;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.crm.wechat.client.enums.MessageSendChannelEnum;
import com.howbuy.crm.wechat.client.enums.MessageSendStatusEnum;
import com.howbuy.crm.wechat.dao.po.message.MessageSendInfoPO;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.domain.message.UpdateMessageSendStatusDTO;
import com.howbuy.crm.wechat.service.outerservice.messagecenter.CompanyResultOuterService;
import com.howbuy.crm.wechat.service.service.message.MessageSendInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 获取消息结果工厂
 * @date 2024/10/30 9:23
 * @since JDK 1.8
 */

@Slf4j
@Component
public class MessageResultFactory {


    @Autowired
    private CompanyResultOuterService companyResultOuterService;

    @Autowired
    private MessageSendInfoService messageSendInfoService;


    /**
     * @description: 更新消息发送结果
     * @param messageInfo
     * @return void
     * @author: jin.wang03
     * @date: 2024/10/30 10:56
     * @since JDK 1.8
     */
    public void update(MessageSendInfoPO messageInfo) {
        UpdateMessageSendStatusDTO result = getResultByChannel(messageInfo);
        if (Objects.isNull(result)) {
            return;
        }
        int updateCount = messageSendInfoService.updateStatusForPushing(result, messageInfo.getId());
        log.info("更新消息发送结果，消息id:{},发送结果:{},更新条数:{}", messageInfo.getId(), JSON.toJSONString(result), updateCount);
    }


    /**
     * @param sendInfo
     * @return com.howbuy.crm.wechat.service.domain.message.SendResultDTO
     * @description:(请在此添加描述)
     * @author: jin.wang03
     * @date: 2024/10/30 9:50
     * @since JDK 1.8
     */
    public UpdateMessageSendStatusDTO getResultByChannel(MessageSendInfoPO sendInfo) {
        MessageSendChannelEnum channelEnum = MessageSendChannelEnum.getEnum(sendInfo.getSendChannel());
        if (channelEnum == null) {
            throw new BusinessException(Constants.SYSTEM_ERROR, "未知消息类型！");
        }
        UpdateMessageSendStatusDTO resultDTO = null;
        switch (channelEnum) {
            case BOT_MESSAGE:
            case EMAIL:
            case NEW_APPLICATION_MESSAGE:
            case SMS:
                break;
            case EMAIL_WITH_ATTACHMENT:
                resultDTO = getEmailResult(sendInfo);
                break;
            default:
                throw new BusinessException(Constants.SYSTEM_ERROR, "未知消息类型！");
        }
        return resultDTO;
    }


    /**
     * @description: 获取邮件发送结果
     * @param sendInfo
     * @return com.howbuy.crm.wechat.service.domain.message.SendResultDTO
     * @author: jin.wang03
     * @date: 2024/10/30 11:05
     * @since JDK 1.8
     */
    private UpdateMessageSendStatusDTO getEmailResult(MessageSendInfoPO sendInfo) {
        UpdateMessageSendStatusDTO resultDTO = new UpdateMessageSendStatusDTO();
        try {
            resultDTO = companyResultOuterService.getEmailResult(sendInfo);
        } catch (Exception e) {
            resultDTO.setSendStatus(MessageSendStatusEnum.PUSH_FAIL.getCode());
            resultDTO.setResponseMsg(e.getMessage());
        }
        return resultDTO;
    }


}