/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.utils;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

/**
 * @description: HTTP请求工具类
 * <AUTHOR>
 * @date 2025-03-19 20:10:14
 * @since JDK 1.8
 */
@Slf4j
public class HttpUtils {

    /**
     * @description: 发送POST请求，请求体为JSON格式
     * @param url 请求URL
     * @param requestBody 请求体对象
     * @param responseType 响应类型
     * @param restTemplate RestTemplate实例
     * @return 响应实体
     * @author: hongdong.xie
     * @date: 2025-03-19 20:10:14
     * @since JDK 1.8
     */
    public static <T, R> ResponseEntity<R> postForJson(String url, T requestBody, Class<R> responseType, RestTemplate restTemplate) {
        // 创建HttpHeaders对象并设置Content-Type为APPLICATION_JSON
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        // 创建HttpEntity对象，包含请求体和请求头
        HttpEntity<String> httpEntity = new HttpEntity<>(
                JSON.toJSONString(requestBody), headers);
        
        // 使用exchange方法发送POST请求
        return restTemplate.exchange(
                url,
                HttpMethod.POST,
                httpEntity,
                responseType);
    }
    
    /**
     * @description: 发送GET请求
     * @param url 请求URL
     * @param responseType 响应类型
     * @param restTemplate RestTemplate实例
     * @return 响应实体
     * @author: hongdong.xie
     * @date: 2025-03-19 20:10:14
     * @since JDK 1.8
     */
    public static <R> ResponseEntity<R> get(String url, Class<R> responseType, RestTemplate restTemplate) {
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<?> httpEntity = new HttpEntity<>(headers);
        
        return restTemplate.exchange(
                url,
                HttpMethod.GET,
                httpEntity,
                responseType);
    }
    
    /**
     * @description: 发送GET请求并设置请求头
     * @param url 请求URL
     * @param headers 请求头
     * @param responseType 响应类型
     * @param restTemplate RestTemplate实例
     * @return 响应实体
     * @author: hongdong.xie
     * @date: 2025-03-19 20:10:14
     * @since JDK 1.8
     */
    public static <R> ResponseEntity<R> get(String url, HttpHeaders headers, Class<R> responseType, RestTemplate restTemplate) {
        HttpEntity<?> httpEntity = new HttpEntity<>(headers);
        
        return restTemplate.exchange(
                url,
                HttpMethod.GET,
                httpEntity,
                responseType);
    }
    
    /**
     * @description: 发送PUT请求，请求体为JSON格式
     * @param url 请求URL
     * @param requestBody 请求体对象
     * @param responseType 响应类型
     * @param restTemplate RestTemplate实例
     * @return 响应实体
     * @author: hongdong.xie
     * @date: 2025-03-19 20:10:14
     * @since JDK 1.8
     */
    public static <T, R> ResponseEntity<R> putForJson(String url, T requestBody, Class<R> responseType, RestTemplate restTemplate) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<String> httpEntity = new HttpEntity<>(
                JSON.toJSONString(requestBody), headers);
        
        return restTemplate.exchange(
                url,
                HttpMethod.PUT,
                httpEntity,
                responseType);
    }
    
    /**
     * @description: 发送DELETE请求
     * @param url 请求URL
     * @param responseType 响应类型
     * @param restTemplate RestTemplate实例
     * @return 响应实体
     * @author: hongdong.xie
     * @date: 2025-03-19 20:10:14
     * @since JDK 1.8
     */
    public static <R> ResponseEntity<R> delete(String url, Class<R> responseType, RestTemplate restTemplate) {
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<?> httpEntity = new HttpEntity<>(headers);
        
        return restTemplate.exchange(
                url,
                HttpMethod.DELETE,
                httpEntity,
                responseType);
    }
} 