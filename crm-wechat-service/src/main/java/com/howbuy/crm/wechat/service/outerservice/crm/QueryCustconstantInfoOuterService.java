package com.howbuy.crm.wechat.service.outerservice.crm;

import com.howbuy.crm.conscust.request.QueryCustconstantInfoRequest;
import com.howbuy.crm.conscust.response.QueryCustconstantInfoResponse;
import com.howbuy.crm.conscust.service.QueryCustconstantInfoService;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @description: crm-wechat 
 * @author: yu.zhang
 * @date: 2023/6/25 16:37 
 * @since JDK 1.8
 * @version: 1.0
 */
@Slf4j
@Service
public class QueryCustconstantInfoOuterService {

//    @DubboReference(registry = Constants.ZK_CRM_CORE, check = false)
    @Autowired
    private QueryCustconstantInfoService queryCustconstantInfoService;

    /**
     * @description:通过企业微信用户查询对应的CRM投顾code
     * @param weChatConsCode
     * @return java.lang.String
     * @author: yu.zhang
     * @date: 2023/6/25 16:39
     * @since JDK 1.8
     */
    public String queryConsultantByWeChatConsCode(String weChatConsCode) {
        //通过一账通获取客户所属投顾信息
        QueryCustconstantInfoRequest consreq = new QueryCustconstantInfoRequest();
        consreq.setWeChatConsCode(weChatConsCode);
        QueryCustconstantInfoResponse consrep = queryCustconstantInfoService.queryConsultantByWeChatConsCode(consreq);
        if (consrep != null && consrep.getCustconstantInfo() != null) {
            return consrep.getCustconstantInfo().getConscode();
        }

        return null;
    }
}
