package com.howbuy.crm.wechat.service.domain.externaluser;

import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @classname: ExternalUserInfoDTO
 * @author: yu.zhang
 * @description: 外部客户详情 - 全量信息 [包括 profile 和 relation信息 ]
 * @creatdate: 2021-02-08 16:48
 * @since: JDK1.8
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class ExternalUserInfoDTO extends  ExternalUserSimpleInfo{

    /**
     * 外部联系人的自定义展示信息，
     * 可以有多个字段和多种类型，包括文本，网页和小程序，
     * 仅当联系人类型是企业微信用户时有此字段，字段详情见对外属性；
     */
    private List<ExternalUserProfileInfoDTO> externalUserProfileList= Lists.newArrayList();
    /**
     * 外部客户 vs 企业微信成员  绑定关系列表
     */
    private List<ExternalUserRelationInfoDTO> followUserList=Lists.newArrayList();
    /**
     * 用于批量获取时   单个的follow_info
     */
    private ExternalUserRelationInfoDTO followInfo;
    /**
     * 国内一账通账号
     */
    private String hboneNo;

    /**
     * 香港微信对应的一帐通号
     */
    private String hkHboneNo;


}
