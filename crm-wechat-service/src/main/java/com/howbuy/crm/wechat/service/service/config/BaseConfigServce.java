/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.config;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.wechat.client.enums.CorpTypeEnum;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.service.commom.aes.WXBizMsgCrypt;
import com.howbuy.crm.wechat.service.commom.enums.WechatApplicationTypeEnum;
import com.howbuy.crm.wechat.service.commom.exception.BusinessException;
import com.howbuy.crm.wechat.service.commom.utils.ApplicationUtilityDTO;
import com.howbuy.crm.wechat.service.commom.utils.CorpUtilityDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: (企微基础配置service)
 * <AUTHOR>
 * @date 2025/7/24 10:01
 * @since JDK 1.8
 */
@Slf4j
@Service
public class BaseConfigServce {

    // 第三方应用套件ID
    @Value("${wechat.thirdparty.suiteId}")
    private String thirdPartySuiteId;

    @Autowired
    private  CacheBaseConfigServce cacheBaseConfigServce;

    /**
     * 获取企业微信corpId
     * @param companyNo  企微-企业主体
     * @return
     */
    public String  getCorpId(String companyNo){
        CorpUtilityDTO utilityDTO= getCorpUtilityDTO(companyNo);
        if(utilityDTO==null){
            throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
        }
        return utilityDTO.getCorpId();
    }



    /**
     * 获取企业微信corpId
     * @param corpId
     * @return
     */
    public String getCompanyNo(String corpId){
        String companyNo = null;
        Map<String,CorpUtilityDTO>  corpConfigMap= cacheBaseConfigServce.getCorpConfigMap() ;
        for (Map.Entry<String, CorpUtilityDTO> entry : corpConfigMap.entrySet()) {
            String entryCompanyNo = entry.getKey();
            CorpUtilityDTO utilityDTO = entry.getValue();
            if (utilityDTO.getCorpId().equals(corpId)) {
                companyNo = entryCompanyNo;
            }
        }
        return companyNo;

//        CmWechatCompanyPO companyInfo= corpConfigRepository.getCorpInfoByCorpId(corpId);
//        return companyInfo==null?null:companyInfo.getCompanyNo();
    }

    public String getReceiveId(String companyNo){
        CorpUtilityDTO corpUtilityDTO = getCorpUtilityDTO(companyNo);
        if (corpUtilityDTO == null) {
            throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
        }
        // 如果是自建应用返回corpId
        if (Objects.equals(corpUtilityDTO.getCorpType(), CorpTypeEnum.INTERNAL.getCode())) {
            return corpUtilityDTO.getCorpId();
        }
        // 如果是第三方应用返回suiteId
        return thirdPartySuiteId;
    }

    /**
     * @description: 获取企业微信工具类对象
     * @param companyNo
     * @return com.howbuy.crm.wechat.service.commom.utils.CorpUtilityDTO
     * @author: haoran.zhang
     * @date: 2025/7/11 15:01
     * @since JDK 1.8
     */
    public CorpUtilityDTO getCorpUtilityDTO(String companyNo) {
//        CmWechatCompanyPO companyPO= getCorpInfo(companyNoEnum);
//        if(companyPO==null){
//            throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
//        }
//        return new CorpUtilityDTO(companyNoEnum.getCode(),companyPO.getCorpId(), companyPO.getToken(), companyPO.getEncodingAesKey());
        //从cache中获取
        CorpUtilityDTO  utilityDTO=cacheBaseConfigServce.getCorpConfig(companyNo);
        if(utilityDTO==null){
            throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
        }
        return utilityDTO;

    }


    /**
     * @description:构建WXBizMsgCrypt对象
     * @param companyNo
     * @return com.howbuy.crm.wechat.service.commom.utils.WXBizMsgCrypt
     * @author: yu.zhang
     * @date: 2023/6/8 15:09
     * @since JDK 1.8
     */
    public  WXBizMsgCrypt buildWXBizMsgCrypt(String companyNo) {
        WXBizMsgCrypt wxcpt = null;
        try {
            CorpUtilityDTO corpUtilityDTO = getCorpUtilityDTO(companyNo);
            wxcpt = new WXBizMsgCrypt(corpUtilityDTO.getToken(), corpUtilityDTO.getEncodingAesKey(), getReceiveId(companyNo));
        } catch (Exception e) {
            log.error("buildWXBizMsgCrypt Exception:{}", e.getMessage(),e);
        }
        return wxcpt;
    }



    /**
     * @description: 获取应用工具类对象
     * @param applicationCode
     * @return com.howbuy.crm.wechat.service.commom.utils.ApplicationUtilityDTO
     * @author: haoran.zhang
     * @date: 2025/7/11 15:01
     * @since JDK 1.8
     */
    public ApplicationUtilityDTO getApplicationUtilityDTO(String applicationCode) {

        //从cache中获取
        ApplicationUtilityDTO utilityDTO=cacheBaseConfigServce.getApplicationConfig(applicationCode);
        if(utilityDTO==null){
            log.info("企微应用：{} 配置不存在！",applicationCode);
            throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
        }
        return utilityDTO;



//        CmWechatApplicationPO applicationInfo=corpConfigRepository.getApplicationInfo(applicationEnum.getCode());
//        if(applicationInfo==null){
//            throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
//        }
//        //归属 公司
//        String companyNo=applicationInfo.getCompanyNo();
//        CmWechatCompanyPO   corpInfo= corpConfigRepository.getCorpInfoByCompanyNo(companyNo);
//        if (corpInfo==null){
//            log.info("企微应用：{} 对应companyNo:{},配置不存在！",applicationEnum,companyNo);
//            throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
//        }
//
//
//        ApplicationUtilityDTO utilityDTO = new ApplicationUtilityDTO();
//        //应用信息
//        utilityDTO.setApplicationCode(applicationEnum.getCode());
//        utilityDTO.setAgentId(applicationInfo.getAgentId());
//        utilityDTO.setAccessSecret(applicationInfo.getAccessSecret());
//
//        //补充该公司信息
//        utilityDTO.setCorpId(corpInfo.getCorpId());
//        utilityDTO.setToken(corpInfo.getToken());
//        utilityDTO.setEncodingAesKey(corpInfo.getEncodingAesKey());
//
//        return utilityDTO;

    }

    /**
     * @description: 根据公司编号和应用类型获取 应用枚举
     * @param companyNo  企微-企业主体
     * @param typeEnum 企微应用类型枚举
     * @return com.howbuy.crm.wechat.service.commom.enums.WechatApplicationEnum
     * @author: haoran.zhang
     * @date: 2025/7/11 15:01
     * @since JDK 1.8
     */
    public String getApplicationCodeByType(String companyNo,
                                           WechatApplicationTypeEnum typeEnum) {

        ApplicationUtilityDTO utilityDTO= getApplicationUtilityByType(companyNo,typeEnum);
        if(utilityDTO!=null){
            return utilityDTO.getApplicationCode();
        }
        return null;
    }


    /**
     * @description: 根据公司编号和应用类型获取 应用工具类对象
     * @param companyNo  企微-企业主体
     * @param typeEnum 企微应用类型枚举
     * @return com.howbuy.crm.wechat.service.commom.utils.ApplicationUtilityDTO
     * @author: haoran.zhang
     * @date: 2025/7/11 15:01
     * @since JDK 1.8
     */
    public ApplicationUtilityDTO getApplicationUtilityByType(String companyNo,
                                           WechatApplicationTypeEnum typeEnum) {

        Map<String,ApplicationUtilityDTO>  cacheMap=cacheBaseConfigServce.getApplicationConfigMap();
        List<ApplicationUtilityDTO> custAppList =
                cacheMap.values().stream()
                        //过滤： 公司
                        .filter(applicationUtilityDTO -> companyNo.equals(applicationUtilityDTO.getCompanyNo()))
                        //过滤： 应用类型 ： 客户联系
                        .filter(applicationUtilityDTO -> typeEnum.getCode().equals(applicationUtilityDTO.getApplicationType()))
                        .collect(Collectors.toList());

        if(custAppList.size()==1){
            return custAppList.get(0);
        }else {
            log.error("companyNo:{},获取应用：[{}]配置信息：{}，配置信息有误！",
                    companyNo,typeEnum.getDescription(), JSON.toJSONString(custAppList));
        }
        return null;
    }

    /**
     * @description: 验证企业是否为第三方应用类型
     * @param companyNo 企微-企业主体
     * @return boolean
     * @author: hongdong.xie
     * @date: 2025-08-19 16:57:27
     * @since JDK 1.8
     */
    public boolean isThirdPartyApplication(String companyNo) {
        try {
            if (companyNo == null || companyNo.trim().isEmpty()) {
                log.warn("验证企业类型失败：企业编码为空");
                return false;
            }
            
            CorpUtilityDTO corpUtilityDTO = getCorpUtilityDTO(companyNo);
            if (corpUtilityDTO == null) {
                log.warn("验证企业类型失败：企业配置不存在，companyNo:{}", companyNo);
                return false;
            }
            
            String corpType = corpUtilityDTO.getCorpType();
            boolean isThirdParty = CorpTypeEnum.THIRD_PARTY.getCode().equals(corpType);
            
            if (!isThirdParty) {
                log.warn("企业类型验证失败：companyNo:{}, 当前类型:{}, 期望类型:{}", 
                        companyNo, corpType, CorpTypeEnum.THIRD_PARTY.getCode());
            }
            
            return isThirdParty;
        } catch (Exception e) {
            log.error("验证企业类型异常，companyNo:{}, error:{}", companyNo, e.getMessage(), e);
            return false;
        }
    }

    /**
     * @description: 验证第三方应用配置完整性
     * @param companyNo 企微-企业主体
     * @return boolean
     * @author: hongdong.xie
     * @date: 2025-08-19 16:57:27
     * @since JDK 1.8
     */
    public boolean validateThirdPartyConfig(String companyNo) {
        try {
            if (!isThirdPartyApplication(companyNo)) {
                return false;
            }
            
            CorpUtilityDTO corpUtilityDTO = getCorpUtilityDTO(companyNo);
            
            // 验证必要的配置信息是否完整
            if (corpUtilityDTO.getCorpId() == null || corpUtilityDTO.getCorpId().trim().isEmpty()) {
                log.error("第三方应用配置验证失败：CorpId为空，companyNo:{}", companyNo);
                return false;
            }
            
            if (corpUtilityDTO.getToken() == null || corpUtilityDTO.getToken().trim().isEmpty()) {
                log.error("第三方应用配置验证失败：Token为空，companyNo:{}", companyNo);
                return false;
            }
            
            if (corpUtilityDTO.getEncodingAesKey() == null || corpUtilityDTO.getEncodingAesKey().trim().isEmpty()) {
                log.error("第三方应用配置验证失败：EncodingAesKey为空，companyNo:{}", companyNo);
                return false;
            }
            
            return true;
        } catch (Exception e) {
            log.error("验证第三方应用配置异常，companyNo:{}, error:{}", companyNo, e.getMessage(), e);
            return false;
        }
    }

}