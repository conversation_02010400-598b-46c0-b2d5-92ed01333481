package com.howbuy.crm.wechat.service.outerservice.acccenter;

import com.howbuy.acc.common.enums.OuterSysTypeEnum;
import com.howbuy.acccenter.facade.query.queryouteracctloginbind.QueryOuterAcctLoginBindFacade;
import com.howbuy.acccenter.facade.query.queryouteracctloginbind.QueryOuterAcctLoginBindRequest;
import com.howbuy.acccenter.facade.query.queryouteracctloginbind.QueryOuterAcctLoginBindResponse;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.commom.enums.BaseReturnCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: 账户中心企业微信登录张继接口
 * @author: yu.zhang
 * @date: 2023/6/19 15:04 
 * @since JDK 1.8
 * @version: 1.0
 */
@Slf4j
@Service
public class QueryOuterAcctLoginBindService {

//    @DubboReference(registry = Constants.ZK_ACC_CENTER, check = false)
    @Autowired
    private QueryOuterAcctLoginBindFacade queryOuterAcctLoginBindFacade;

    /**
     * @description:客户通过微信授权登录企业APP，建立的微信unionID与一账通关联
     * @param unionid
     * @return java.lang.String
     * @author: yu.zhang
     * @date: 2023/6/19 15:07
     * @since JDK 1.8
     */
    public String getAcctLoginHboneNoByUnionid(String unionid) {

        String hboneno = Strings.EMPTY;
        //通过unionid调用账户中心接口获取一账通信息
        QueryOuterAcctLoginBindRequest req = new QueryOuterAcctLoginBindRequest();
        req.setOuterAcct(unionid);
        req.setOuterSysType(OuterSysTypeEnum.WeChat);
        QueryOuterAcctLoginBindResponse rep = queryOuterAcctLoginBindFacade.execute(req);
        if (rep != null && BaseReturnCodeEnum.ACCSUCCESS.getCode().equals(rep.getReturnCode())) {
            hboneno = rep.getHboneNo();
        }

        return hboneno;
    }
}
