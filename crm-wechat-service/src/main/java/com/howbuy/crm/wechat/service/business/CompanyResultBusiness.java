/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.business;

import com.howbuy.cc.message.request.QuerySystemSendEmailStatusRequest;
import com.howbuy.cc.message.response.QuerySystemSendEmailStatusResponse;
import com.howbuy.cc.message.service.QuerySystemSendEmailStatusService;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/11/3 13:55
 * @since JDK 1.8
 */

@Slf4j
@Component
public class CompanyResultBusiness {
    
    @DubboReference(registry = Constants.ZK_MESSAGE_PUBLIC, check = false)
    private QuerySystemSendEmailStatusService querySystemSendEmailStatusService;

    
    
    /**
     * @description: 查询[消息中心]邮件发送状态
     * @param request
     * @return com.howbuy.cc.message.response.QuerySystemSendEmailStatusResponse
     * @author: jin.wang03
     * @date: 2024/10/30 10:08
     * @since JDK 1.8
     */
    public QuerySystemSendEmailStatusResponse querySendEmailStatus(QuerySystemSendEmailStatusRequest request) {
        return querySystemSendEmailStatusService.execute(request);
    }
    

}