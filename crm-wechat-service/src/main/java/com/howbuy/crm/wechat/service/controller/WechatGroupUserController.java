/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.crm.base.model.BaseConstantEnum;
import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.client.producer.req.QueryUserGroupRequest;
import com.howbuy.crm.wechat.client.producer.vo.*;
import com.howbuy.crm.wechat.service.commom.utils.CompanyNoUtils;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.service.wechatgroup.WechatGroupUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/5/27 1:45 PM
 * @since JDK 1.8
 */
@Slf4j
@RestController
@RequestMapping("/wechatgroupuser")
public class WechatGroupUserController {
    @Autowired
    private WechatGroupUserService wechatGroupUserService;

    /**
     * @api {POST} /wechatgroupuser/queryusergrouplist queryUserGroupList()
     * @apiVersion 1.0.0
     * @apiGroup WechatGroupUserController
     * @apiName 用户群数据查询接口
     * @apiDescription 用户群数据查询接口
     * @apiParam (请求体) {String} hbOneNo 一账通号
     * @apiParam (请求体) {String} wechatNickName 用户微信昵称
     * @apiParam (请求体) {Array} deptIdList 部门ID列表
     * @apiParam (请求体) {Number} pageNo 页码
     * @apiParam (请求体) {Number} pageSize 每页大小
     * @apiParamExample 请求体示例
     * {"hbOneNo":"w3","deptIdList":[7672],"pageNo":913,"pageSize":8157,"wechatNickName":"ia"}
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 0002-参数错误 9999-系统错误 0004-未查询到数据
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} returnObject 返回内容
     * @apiSuccess (响应结果) {Array} returnObject.userGroupDetailVOList 明细列表
     * @apiSuccess (响应结果) {String} returnObject.userGroupDetailVOList.hbOneNo 一账通号
     * @apiSuccess (响应结果) {String} returnObject.userGroupDetailVOList.wechatNickName 微信昵称
     * @apiSuccess (响应结果) {Array} returnObject.userGroupDetailVOList.groupIdList 客户当前所在群id列表
     * @apiSuccess (响应结果) {Array} returnObject.userGroupDetailVOList.hisGroupIdList 历史所在群id列表
     * @apiSuccess (响应结果) {String} returnObject.userGroupDetailVOList.addAssistantTime 添加助手时间 yyyyMMddHHmmss
     * @apiSuccess (响应结果) {Number} returnObject.totalNum 总条数
     * @apiSuccess (响应结果) {Number} returnObject.totalPage 总页数
     * @apiSuccessExample 响应结果示例
     * {"returnObject":{"userGroupDetailVOList":[{"hbOneNo":"I5p1m2Ndb2","groupIdList":["W"],"addAssistantTime":"ncVjO9IeA","wechatNickName":"0A9shqPMlO","hisGroupIdList":["YYJuis0RxZ"]}],"totalNum":4613,"totalPage":9512},"code":"Du8Bqj68J","description":"8XLBqP"}
     */
    @PostMapping("/queryusergrouplist")
    public Response<UserGroupVO> queryUserGroupList(@RequestBody QueryUserGroupRequest queryUserGroupRequest){
        log.info("queryUserGroupList request:{}", JSON.toJSONString(queryUserGroupRequest));
        List<Integer> deptIdList = queryUserGroupRequest.getDeptIdList();
        if (CollectionUtils.isEmpty(deptIdList)) {
            return new Response<>(BaseConstantEnum.PARAM_ERROR.getCode(), BaseConstantEnum.PARAM_ERROR.getDescription(), null);
        }
        String hbOneNo = queryUserGroupRequest.getHbOneNo();
        String wechatNickName = queryUserGroupRequest.getWechatNickName();
        Integer pageNo = queryUserGroupRequest.getPageNo();
        Integer pageSize = queryUserGroupRequest.getPageSize();
        //入口 companyNo 默认赋值：
        String companyNo = CompanyNoUtils.getOrDefault(queryUserGroupRequest.getCompanyNo());
        UserGroupVO result = wechatGroupUserService.queryUserGroupList(companyNo,
                hbOneNo, wechatNickName, deptIdList, pageNo, pageSize);
        return Response.ok(result);
    }

    /**
     * @api {POST} /wechatgroupuser/getgroupidbyhboneno getGroupIdByHbOneNo()
     * @apiVersion 1.0.0
     * @apiGroup WechatGroupUserController
     * @apiName 查询客户当前所在群id接口
     * @apiDescription 查询客户当前所在群id
     * @apiParam (请求参数) {String} hbOneNo 一账通号
     * @apiParamExample 请求参数示例
     * hbOneNo=VfopU
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 0002-参数错误 9999-系统错误
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} returnObject 返回内容
     * @apiSuccess (响应结果) {Array} returnObject.groupIdList 群id列表
     * @apiSuccess (响应结果) {String} returnObject.groupIdList.groupId 群id
     * @apiSuccess (响应结果) {String} returnObject.groupIdList.joinGroupTime 进群时间 yyyyMMddHHmmss
     * @apiSuccessExample 响应结果示例
     * {"returnObject":{"joinGroupTime":"CG5Ofni","groupId":"mH7nSkn"},"code":"dp","description":"JIjswfNy6"}
     */
    @PostMapping("/getgroupidbyhboneno")
    public Response<GroupIdVO> getGroupIdByHbOneNo(String hbOneNo){
        //入口 companyNo 默认赋值：
        String companyNo=Constants.DEFAULT_COMPANY_NO;
        GroupIdVO vo = wechatGroupUserService.queryNormalByHboneNo(companyNo,hbOneNo);
        return Response.ok(vo);
    }

    /**
     * @api {POST} /wechatgroupuser/getjoingroupresult getJoinGroupResult()
     * @apiVersion 1.0.0
     * @apiGroup WechatGroupUserController
     * @apiName 查询客户加群结果接口
     * @apiDescription 查询客户加群结果
     * @apiParam (请求参数) {String} hbOneNo 一账通号
     * @apiParam (请求参数) {String} groupId 群id
     * @apiParamExample 请求参数示例
     * hbOneNo=Xlo&groupId=rC
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 0002-参数错误 9999-系统错误
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} returnObject 返回内容
     * @apiSuccess (响应结果) {String} returnObject.joinGroupStatus 入群结果：1-未入群 2-已入群 3-已退群(主动退群)
     * @apiSuccess (响应结果) {String} returnObject.joinGroupTime 进群时间 yyyyMMddHHmmss
     * @apiSuccess (响应结果) {String} returnObject.existGroupTime 退群时间 yyyyMMddHHmmss
     * @apiSuccessExample 响应结果示例
     * {"returnObject":{"joinGroupTime":"ncp","joinGroupStatus":"UVCIswk8x","existGroupTime":"EB1J"},"code":"JHnEzgAKge","description":"oG"}
     */
    @PostMapping("/getjoingroupresult")
    public Response<JoinGroupResultVO> getJoinGroupResult(String hbOneNo, String groupId){
        if (StringUtils.isEmpty(hbOneNo) || StringUtils.isEmpty(groupId)) {
            return new Response<>(BaseConstantEnum.PARAM_ERROR.getCode(), BaseConstantEnum.PARAM_ERROR.getDescription(), null);
        }
        //入口 companyNo 默认赋值：
        String companyNo=Constants.DEFAULT_COMPANY_NO;
        JoinGroupResultVO vo = wechatGroupUserService.queryJoinGroup(companyNo,hbOneNo, groupId);
        return Response.ok(vo);
    }


    /**
     * @api {POST} /wechatgroupuser/getgroupbreakstatus getGroupBreakStatus()
     * @apiVersion 1.0.0
     * @apiGroup WechatGroupUserController
     * @apiName 查询群是否解散接口
     * @apiParam (请求参数) {String} groupId
     * @apiParamExample 请求参数示例
     * groupId=aaUjtX
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 0002-参数错误 9999-系统错误 0004-未查询到数据
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} returnObject 返回内容
     * @apiSuccess (响应结果) {String} returnObject.groupBreakStatus 是否解散 0-否 1-是
     * @apiSuccess (响应结果) {String} returnObject.breakGroupTime 群解散时间
     * @apiSuccessExample 响应结果示例
     * {"returnObject":{"breakGroupTime":"rf","groupBreakStatus":"o1dVxGGI2"},"code":"lYCV","description":"9"}
     */
    @PostMapping("/getgroupbreakstatus")
    public Response<GroupBreakStatusVO> getGroupBreakStatus(String groupId){
        if (StringUtils.isEmpty(groupId)) {
            return new Response<>(BaseConstantEnum.PARAM_ERROR.getCode(), BaseConstantEnum.PARAM_ERROR.getDescription(), null);
        }
        GroupBreakStatusVO vo = wechatGroupUserService.queryGroupStatus(groupId);
        if(Objects.isNull(vo)){
            return new Response<>(BaseConstantEnum.DATA_NOT_FUND.getCode(), BaseConstantEnum.DATA_NOT_FUND.getDescription(), null);
        }
        return Response.ok(vo);
    }

    /**
     * @api {POST} /wechatgroupuser/queryusergroupinfobyhbone queryUserGroupInfoByHbOneNo()
     * @apiVersion 1.0.0
     * @apiGroup WechatGroupUserController
     * @apiName queryUserGroupInfoByHbOneNo()
     * @apiParam (请求参数) {String} hbOneNo
     * @apiParamExample 请求参数示例
     * hbOneNo=8
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 0002-参数错误 9999-系统错误 0004-未查询到数据
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Array} returnObject 返回内容
     * @apiSuccess (响应结果) {String} returnObject.status
     * @apiSuccess (响应结果) {String} returnObject.exitGroupTime 退群时间 yyyyMMddHHmmss
     * @apiSuccess (响应结果) {String} returnObject.joinGroupTime 入群时间 yyyyMMddHHmmss
     * @apiSuccess (响应结果) {String} returnObject.groupId 群id
     * @apiSuccessExample 响应结果示例
     * {"returnObject":[{"joinGroupTime":"RGF5Bcnv","exitGroupTime":"xAlv","groupId":"luk","status":"zdzexaa"}],"code":"UDaI5LH","description":"npKd7uV0"}
     */
    @PostMapping("/queryusergroupinfobyhbone")
    public Response<List<UserGroupInfoVO>> queryUserGroupInfoByHbOneNo(String hbOneNo) {
        //入口 companyNo 默认赋值：
        String companyNo=Constants.DEFAULT_COMPANY_NO;
        try {
            return Response.ok(wechatGroupUserService.queryUserGroupInfoByHbOneNo(companyNo,hbOneNo));
        } catch (BusinessException be) {
            return new Response<>(ResponseCodeEnum.PARAM_ERROR.getCode(), be.getErrorDesc(), null);
        } catch (Exception e) {
            log.error("queryUserGroupInfoByHbOneNo error:{}", Throwables.getStackTraceAsString(e));
            return new Response<>(ResponseCodeEnum.SYS_ERROR.getCode(), "System error", null);
        }
    }

}