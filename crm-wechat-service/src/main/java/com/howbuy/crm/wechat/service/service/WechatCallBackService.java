package com.howbuy.crm.wechat.service.service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.howbuy.crm.wechat.service.commom.aes.WXBizMsgCrypt;
import com.howbuy.crm.wechat.service.commom.constant.WxTempConstant;
import com.howbuy.crm.wechat.service.commom.utils.DateUtils;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.commom.utils.MessageUtil;
import com.howbuy.crm.wechat.service.domain.callback.*;
import com.howbuy.crm.wechat.service.service.config.BaseConfigServce;
import com.howbuy.crm.wechat.service.service.contact.ChangeContactEventService;
import com.howbuy.crm.wechat.service.service.externalcontact.ChangeExternalContactEventService;
import com.howbuy.crm.wechat.service.service.group.ChangeChatEventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @classname: WechatCallBackService
 * @author: yu.zhang
 * @description: 企业微信回调服务
 * @creatdate: 2021-02-08 16:22
 * @since: JDK1.8
 */
@Slf4j
@Service
public class WechatCallBackService {

    @Autowired
    private ChangeContactEventService changeContactEventService;
    @Autowired
    private ChangeChatEventService changeChatEventService;
    @Autowired
    private ChangeExternalContactEventService changeExternalContactEventService;

    @Autowired
    private BaseConfigServce baseConfigServce;



    /**
     * @description:根据解密后的消息进行落表分发
     * @param wechatCallbackSignature
     * @param companyNoEnum
     * @return java.lang.String
     * @author: yu.zhang
     * @date: 2023/6/8 15:27
     * @since JDK 1.8
     */
    public String getEncryptRespMessage(WechatCallbackSignatureDTO wechatCallbackSignature, String companyNoEnum) {
        String respMessage = null;
        try {
            log.info("getEncryptRespMessage开始xmlMsg:{}", wechatCallbackSignature);
            //2.解析xml字符串
            Map<String, String> requestMap = MessageUtil.parseXml(wechatCallbackSignature.getResult());
            //日志打印解析串
            log.info(JSON.toJSONString(requestMap));
            //3.获取请求参数
            //3.1 企业微信
            String fromUserName = requestMap.get("FromUserName");
            //3.2 成员UserID
            String toUserName = requestMap.get("ToUserName");
            //3.3 消息类型与事件
            String msgType = requestMap.get("MsgType");
            //业务触发
            Map<String, Object> finalRequestMap = new HashMap<>();
            requestMap.forEach((k,v)->{
                finalRequestMap.put(k,v);
            });
            this.getRespContentByMsgType(finalRequestMap, companyNoEnum);
            //4.组装 回复文本消息
            MessageDTO textMessage = new MessageDTO();
            textMessage.setToUserName(fromUserName);
            textMessage.setFromUserName(toUserName);
            textMessage.setCreateTime(System.currentTimeMillis());
            textMessage.setMsgType(WxTempConstant.TEMP_TEXT_TEXT);
            //4.1.获取回复消息的内容 ：消息的分类处理
            textMessage.setContent("成功");

            //5.获取xml字符串： 将（被动回复消息型的）文本消息对象 转成  xml字符串
            respMessage = MessageUtil.textMessageToXml(textMessage);

            //6.加密
            WXBizMsgCrypt wXBizMsgCrypt = baseConfigServce.buildWXBizMsgCrypt(companyNoEnum);
            respMessage = wXBizMsgCrypt.EncryptMsg(respMessage, wechatCallbackSignature.getTimestamp(), msgType);

        } catch (Exception e) {
            e.printStackTrace();
            log.error("getEncryptRespMessage Exception:" + e.getMessage(), e);
        }

        return respMessage;
    }

    /**
     * 处理消息：根据消息类型获取回复内容
     * getRespContentByMsgType
     * @param requestMap
     * @param companyNoEnum 企微-企业主体
     * @return void
     * @Author: yu.zhang on 2021/2/10 9:25
     */
    public void getRespContentByMsgType(Map<String, Object> requestMap, String companyNoEnum) {

        String msgType = (String) requestMap.get("MsgType");
        //事件推送 客户联系对应的回调event
        if (msgType.equals(WxTempConstant.REQ_MESSAGE_TYPE_EVENT)) {
            this.processEevent(requestMap, companyNoEnum);
        }
    }

    /**
     * @description:根据消息类型判断是微信添加成员回调还是通讯录回调
     * @param requestMap
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/8 18:20
     * @since JDK 1.8
     */
    public void processEevent(Map<String, Object> requestMap,String companyNoEnum ) {
        String corpId = baseConfigServce.getCorpId(companyNoEnum);
        log.info("WechatCallBackService|processEevent|requestMap:{},corpId:{}", JSON.toJSONString(requestMap), corpId);
        //获取类型
        String eventType = (String) requestMap.get("Event");
        ObjectMapper mapper = new ObjectMapper();
        //忽略驼峰大小写问题
        mapper.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
        //遇到未知属性，直接抛弃
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 获取创建时间
        Long createTime = getCrteateTime(requestMap);
        if (Objects.equals(eventType, WxTempConstant.EVENT_TYPE_EXTERNAL)) {
            ExternalContactDTO externalContactDTO = mapper.convertValue(requestMap, ExternalContactDTO.class);
            externalContactDTO.setCorpId(corpId);
            externalContactDTO.setCreateTime(createTime);
            changeExternalContactEventService.processExternalChangeType(externalContactDTO);
        } else if (Objects.equals(eventType, WxTempConstant.EVENT_TYPE_CONTACT)) {
            ContactDTO contactDTO = mapper.convertValue(requestMap, ContactDTO.class);
            contactDTO.setCorpId(corpId);
            contactDTO.setCreateTime(createTime.toString());
            changeContactEventService.processContactsChangeType(contactDTO);
        }else if (Objects.equals(eventType, WxTempConstant.EVENT_TYPE_EXTERNAL_CHAT)) {
            ChatEventDTO chatEventDTO = mapper.convertValue(requestMap, ChatEventDTO.class);
            chatEventDTO.setCorpId(corpId);
            chatEventDTO.setCreateTime(createTime.toString());
            changeChatEventService.processChatChangeType(chatEventDTO);
        }
    }

    /**
     * @description: 获取创建时间
     * @param requestMap
     * @return java.lang.Long
     * @author: hongdong.xie
     * @date: 2024/12/24 18:32
     * @since JDK 1.8
     */
    private static Long getCrteateTime(Map<String, Object> requestMap) {

        Object createTimeObj = requestMap.get("CreateTime") != null ? requestMap.get("CreateTime") : requestMap.get("createTime");

        // 如果为空取当前时间
        if (createTimeObj == null) {
            return System.currentTimeMillis();
        }
        Long createTime = Long.parseLong(createTimeObj.toString());

        // 外部给的部分 * 了1000，所以此处要判断下是否乘过了
        int year = Integer.parseInt(DateUtils.dateFormatToString(new Date(createTime),DateUtils.YYYYMMDD));
        // 如果格式化的日期小于2020年，说明是乘过1000的
        if (year < 20100101) {
            createTime = createTime * 1000;
        }

        return createTime;
    }

    public static void main(String[] args) {
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("CreateTime", 1735043403);
        Long createTime = getCrteateTime(requestMap);
    }


    /**
     * @description:(企业微信回调旧企微转至新企微处理.只处理[好买财富])
     * @param requestMap
     * @return void
     * @author: shuai.zhang
     * @date: 2024/3/20 16:05
     * @since JDK 1.8
     */
    public void transferEventDeal(Map<String, Object> requestMap) {
        log.info( "企业微信回调旧企微转至新企微处理|transferEventDeal|requestMap:{}",JSON.toJSONString(requestMap));
        //转发过来的默认是好买财富
        this.processEevent(requestMap, Constants.DEFAULT_COMPANY_NO);
    }
}