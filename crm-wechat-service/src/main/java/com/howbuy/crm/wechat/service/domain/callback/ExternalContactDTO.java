package com.howbuy.crm.wechat.service.domain.callback;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description: https://developer.work.weixin.qq.com/document/path/92129对应的bean
 * @author: yu.zhang
 * @date: 2023/6/8 17:46 
 * @since JDK 1.8
 * @version: 1.0
 */
@Getter
@Setter
@ToString
public class ExternalContactDTO implements Serializable {
    /**
     * 应用ID
     */
    private String corpId;
    /**
     * 企业微信CorpID
     */
    private String toUserName;
    /**
     * 此事件该值固定为sys，表示该消息由系统生成
     */
    private String fromUserName;
    /**
     * 消息创建时间 （整型）
     */
    private Long createTime;
    /**
     * 消息的类型
     */
    private String msgType;
    /**
     * 事件的类型
     */
    private String event;
    /**
     * 触发类型
     */
    private String changeType;
    /**
     * 企业服务人员的UserID
     */
    private String userID;
    /**
     * 外部联系人的userid，注意不是企业成员的帐号
     */
    private String externalUserID;
    /**
     * 添加此用户的「联系我」方式配置的state参数，或在获客链接中指定的customer_channel参数，可用于识别添加此用户的渠道
     */
    private String state;
    /**
     * 欢迎语code，可用于发送欢迎语
     */
    private String welcomeCode;
    /**
     * 删除客户的操作来源，DELETE_BY_TRANSFER表示此客户是因在职继承自动被转接成员删除
     */
    private String source;
}
