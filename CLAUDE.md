# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目架构

这是一个基于Spring Boot + Spring Cloud + Dubbo 3.x的微信CRM系统，采用多模块Maven项目结构，支持多租户微信企业应用管理。

### 模块结构
- **crm-wechat-client**: Dubbo接口定义、请求/响应对象定义
- **crm-wechat-dao**: 数据访问层，MyBatis Mapper和PO对象
- **crm-wechat-service**: 业务逻辑实现层
- **crm-wechat-remote**: 启动类和公共配置

### 核心技术栈
- JDK 1.8
- Spring Boot 2.3.7.RELEASE
- Spring Cloud Hoxton.SR9
- Dubbo 3.2.12
- MyBatis 2.2.2
- Druid 1.2.8
- MySQL 8.x
- Redis
- ZooKeeper 3.4.13

## 构建和运行命令

### 构建项目
```bash
# 全量构建
mvn clean package -DskipTests

# 单模块构建
mvn clean package -pl crm-wechat-service -am -DskipTests

# 跳过测试构建
mvn clean compile -DskipTests
```

### 启动应用
```bash
# 使用启动脚本
./start.sh

# 调试模式启动
./start.sh debug

# JMX监控模式启动  
./start.sh jmx

# 直接运行主类
java -jar crm-wechat-remote/target/crm-wechat-remote-*.jar
```

### 开发环境启动
```bash
# 在crm-wechat-remote模块中运行主类
com.howbuy.crm.wechat.remote.CrmWechatApplication

# 使用Spring Boot插件启动
mvn spring-boot:run -pl crm-wechat-remote
```

## 系统核心功能

### 多租户支持架构
- 基于CompanyNoEnum枚举管理不同企业配置
- 支持微信企业内建应用（客户联系）和第三方应用
- 应用类型：customer(客户联系)、notice(业务通知)、special(特殊应用)

### 微信API集成模块
- **部门同步**: WechatDepartmentOuterService
- **员工同步**: WechatUserOuterService  
- **外部联系人**: WechatExternalContactOuterService
- **群聊管理**: WechatGroupService、WechatGroupUserService
- **素材管理**: WechatMaterialService
- **消息推送**: WechatPushMsgService
- **JSSDK**: WechatJsSdkService

### 核心业务服务
- **客户关系管理**: 微信用户与好买客户绑定关系
- **群聊管理**: 微信群创建、成员管理、消息推送
- **消息中心**: 模板消息推送、客服消息
- **素材中心**: 图片、视频、文件素材管理
- **回调处理**: 微信事件回调处理（用户增减、消息接收等）

## 代码架构规范

### 包结构规范
```
crm-wechat-client/
├── com.howbuy.crm.wechat.client.facade.*          # Dubbo接口定义
├── com.howbuy.crm.wechat.client.domain.request.*  # 请求参数
├── com.howbuy.crm.wechat.client.domain.response.* # 响应参数
├── com.howbuy.crm.wechat.client.enums.*           # 枚举类
└── com.howbuy.crm.wechat.client.base.*            # 基础类

crm-wechat-service/
├── com.howbuy.crm.wechat.service.facade.*         # Dubbo接口实现
├── com.howbuy.crm.wechat.service.service.*        # 业务服务层
├── com.howbuy.crm.wechat.service.controller.*     # HTTP接口
├── com.howbuy.crm.wechat.service.repository.*     # 数据访问层
├── com.howbuy.crm.wechat.service.business.*       # 公共业务逻辑
├── com.howbuy.crm.wechat.service.job.*            # 定时任务
└── com.howbuy.crm.wechat.service.outerservice.*   # 外部接口调用
```

### 命名约定
- **变量**: camelCase (如: userName, wechatAppId)
- **方法**: camelCase (如: getUserInfo, sendMessage)
- **类**: PascalCase (如: WechatUserService, GetUserInfoRequest)
- **常量**: UPPERCASE_WITH_UNDERSCORES
- **请求类**: {MethodName}Request
- **响应类**: {MethodName}VO
- **接口**: {BusinessName}Facade
- **实现类**: {InterfaceName}Impl

### 注解规范
- **实体类**: 使用`@Setter/@Getter` (不使用`@Data`)
- **Dubbo服务**: `@DubboService`
- **依赖注入**: `@Resource`
- **事务管理**: `@Transactional`
- **禁止**: 禁止使用`BeanUtils.copyProperties`进行属性复制

## 任务调度系统

### 定时任务列表
- **SyncChatGroupJob**: 同步微信群聊信息
- **SyncChatEmpJob**: 同步企业员工信息
- **SyncChatGroupUserJob**: 同步群聊成员信息
- **SyncCustHboneRelationJob**: 同步客户关系绑定
- **CrmAcceptMessageJob**: 接收微信消息
- **CrmSendMessageJob**: 发送微信消息
- **CrmBuildMessageJob**: 构建待发送消息

### 批处理架构
- **AbstractBatchMessageJob**: 消息批处理抽象基类
- **BatchOperateExecutor**: 批操作执行器
- **PageQueryService**: 分页查询服务

## 数据模型设计

### 核心数据表
- **cm_wechat_company**: 企业微信配置表
- **cm_wechat_application**: 微信应用配置表
- **cm_wechat_emp**: 员工信息表
- **cm_wechat_dept**: 部门信息表
- **cm_wechat_cust_info**: 微信客户信息表
- **cm_wechat_cust_relation**: 客户关系绑定表
- **cm_wechat_group**: 微信群信息表
- **cm_wechat_group_user**: 群成员关系表
- **cm_wechat_external_info**: 外部联系人信息表

### 消息相关表
- **message_send_info**: 消息发送记录表
- **message_accept_info**: 消息接收记录表

## 配置管理

### 重要配置文件
- **crm-wechat-remote/src/main/resources/bootstrap.properties**: 启动配置
- **crm-wechat-remote/src/main/resources/dubbo.xml**: Dubbo配置
- **crm-wechat-remote/src/main/resources/log4j2-*.xml**: 日志配置
- **crm-wechat-service/src/main/resources/spring/**: Spring配置

### 环境配置
- **多环境支持**: dev、test、prod
- **配置中心**: 支持Nacos配置中心
- **数据库配置**: Druid连接池配置

## 开发规范

### 接口定义规范
1. **Dubbo接口**: 必须继承`BaseFacade<Request, Response>`
2. **APIDoc注释**: 使用标准API文档注释格式
3. **请求类**: 必须继承`BaseRequest`
4. **响应格式**: 统一使用`Response<T>`包装

### 事务管理规范
- **Repository层**: `@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)`
- **数据修改**: `@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)`
- **查询操作**: 使用默认事务传播行为

### 异常处理规范
- **统一异常**: 所有业务异常继承`BusinessException`
- **错误码**: 使用`ResponseCodeEnum`定义标准错误码
- **日志记录**: 关键操作必须记录日志
- **错误响应**: 统一使用`Response.fail()`返回错误

### 日志规范
- **日志框架**: Log4j2 + SLF4J
- **日志级别**: ERROR/WARN/INFO/DEBUG合理使用
- **敏感信息**: 日志中不得记录敏感信息
- **格式规范**: 使用占位符而非字符串拼接

## 微信集成规范

### API调用规范
- **AccessToken**: 统一缓存和管理，定时刷新
- **频率控制**: 遵守微信API调用频率限制
- **错误处理**: 处理微信返回的错误码和重试机制
- **签名验证**: 所有回调必须验证微信签名

### 消息处理规范
- **幂等性**: 所有消息处理必须实现幂等性
- **重试机制**: 支持失败重试和死信队列
- **消息验证**: 验证消息格式和签名
- **状态跟踪**: 记录消息处理状态和结果

### 安全规范
- **敏感数据**: 微信配置信息加密存储
- **回调URL**: 使用HTTPS协议
- **权限验证**: 所有接口必须验证权限
- **IP白名单**: 微信回调IP白名单验证

## 性能优化指南

### 缓存策略
- **AccessToken**: Redis缓存，2小时有效期
- **JsapiTicket**: Redis缓存，2小时有效期
- **用户信息**: 本地缓存+Redis二级缓存
- **部门信息**: 定时刷新缓存

### 数据库优化
- **索引设计**: 针对查询场景优化索引
- **分页查询**: 大数据量使用游标分页
- **批量操作**: 使用批量插入/更新减少数据库交互
- **连接池**: Druid连接池参数优化

### 异步处理
- **消息发送**: 异步消息队列处理
- **文件上传**: 异步处理大文件上传
- **数据同步**: 异步批量数据同步

## 常用开发命令

### 测试相关
```bash
# 运行单元测试
mvn test

# 运行指定测试类
mvn test -Dtest=WechatAuthServiceTest

# 跳过测试
mvn clean package -DskipTests
```

### 调试相关
```bash
# 远程调试
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -jar crm-wechat-remote-*.jar

# 查看应用状态
curl http://localhost:8080/actuator/health

# 查看Dubbo服务
http://localhost:8080/dubbo-admin
```

### 部署相关
```bash
# 打包部署
mvn clean package -DskipTests
cp crm-wechat-remote/target/*.jar /opt/app/

# 快速重启
./start.sh stop && ./start.sh start
```

## 监控与运维

### 健康检查
- **Actuator端点**: /actuator/health
- **Dubbo监控**: /dubbo-admin
- **数据库监控**: Druid监控页面

### 日志查看
```bash
# 查看应用日志
tail -f logs/cmsstdout.log

# 查看错误日志
grep ERROR logs/cmsstdout.log

# 查看微信回调日志
grep "wechat.callback" logs/cmsstdout.log
```

## 快速开始指南

1. **环境准备**: 安装JDK 1.8、Maven 3.6+、MySQL 8.0、Redis、ZooKeeper
2. **数据库初始化**: 执行项目提供的SQL脚本
3. **配置修改**: 修改bootstrap.properties中的数据库和Redis配置
4. **微信配置**: 配置企业微信相关参数
5. **启动应用**: 使用start.sh脚本启动
6. **验证服务**: 访问健康检查端点确认服务正常

## 项目特色功能

- **多企业支持**: 一套系统管理多个企业微信
- **动态配置**: 支持运行时动态调整微信应用配置
- **消息追踪**: 完整的消息发送和接收链路追踪
- **权限控制**: 细粒度的接口权限和数据权限控制
- **容灾备份**: 支持主备切换和数据备份恢复