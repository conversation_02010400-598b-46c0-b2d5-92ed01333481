package com.howbuy.crm.wechat.client.enums;

/**
 * @description:(crm接受消息处理状态：0-未处理；1-已处理；2-处理中)
 * @return
 * @author: haoran.zhang
 * @date: 2023/10/7 10:51
 * @since JDK 1.8
 */
public enum AcceptMessageStatusEnum {

    /**
     * 未处理
     */
    UNPROCESSED("0", "未处理"),
    /**
     * 已处理
     */
    PROCESSED("1", "已处理"),
    /**
     * 处理中
     */
    PROCESSING("2", "处理中");

    /**
     * 编码
     */
    private String code;


    /**
     * 描述
     */
    private String description;

    AcceptMessageStatusEnum(String code,String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 通过code获得
     *
     * @param code
     *            系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code) {
        AcceptMessageStatusEnum statusEnum=getEnum(code);
        return statusEnum==null?null :statusEnum.getDescription();
    }


    /**
     * 通过code直接返回 整个枚举类型
     *
     * @param code
     *            系统返回参数编码
     * @return BaseConstantEnum
     */
    public static AcceptMessageStatusEnum getEnum(String code) {
        for(AcceptMessageStatusEnum statusEnum : AcceptMessageStatusEnum.values()){
            if(statusEnum.getCode().equals(code)){
                return statusEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
