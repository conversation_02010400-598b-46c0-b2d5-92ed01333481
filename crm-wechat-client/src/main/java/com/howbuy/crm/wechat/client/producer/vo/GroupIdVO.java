package com.howbuy.crm.wechat.client.producer.vo;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 查询客户当前所在群id返回
 * @date 2024/1/19 15:19
 * @since JDK 1.8
 */
@Data
public class GroupIdVO implements Serializable {

    /**
     * 群id列表
     */
    private List<GroupIdDetailVO> groupIdList =  new ArrayList<>();

   @Data
    public static class GroupIdDetailVO implements Serializable {
        /**
         * 群id
         */
        private String groupId;
        /**
         * 进群时间 yyyyMMddHHmmss
         */
        private String joinGroupTime;
    }
}
