package com.howbuy.crm.wechat.client.base;
import com.howbuy.crm.wechat.client.enums.BaseConstantEnum;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * service返回对象Dto
 *
 * <AUTHOR> 2017-12-22 14:38:39
 */
public class ReturnMessageDto<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 成功-0000
     */
    public static final String SUCCESS_CODE = BaseConstantEnum.SUCCESS.getCode();

    /**
     * 失败-0001
     */
    public static final String FAIL_CODE = BaseConstantEnum.FAIL.getCode();

    /**
     * 返回代码
     */
    private String returnCode;

    /**
     * 返回信息体
     */
    private String returnMsg;

    /**
     * 自定义返回对象
     */
    private T returnObject;

    /**
     * 返回列表
     */
    private List<T> returnList = new ArrayList<>();

    public ReturnMessageDto() {
        this.returnCode = SUCCESS_CODE;
    }

    public ReturnMessageDto(String returnCode, String returnMsg) {
        this.returnCode = returnCode;
        this.returnMsg = returnMsg;
    }

    public ReturnMessageDto(String returnCode, String returnMsg, T returnObject) {
        this.returnCode = returnCode;
        this.returnMsg = returnMsg;
        this.returnObject = returnObject;
    }

    public ReturnMessageDto(String returnCode, String returnMsg, List<T> returnList) {
        this.returnCode = returnCode;
        this.returnMsg = returnMsg;
        this.returnList = returnList;
    }

    public ReturnMessageDto(String returnCode) {
        this.returnCode = returnCode;
    }

    public static <T> ReturnMessageDto<T> ok() {
        return new ReturnMessageDto<>();
    }

    public static <T> ReturnMessageDto<T> ok(String msg) {
        return new ReturnMessageDto<>(SUCCESS_CODE, msg);
    }

    public static <T> ReturnMessageDto<T> ok(String msg,T data) {
        return new ReturnMessageDto<>(SUCCESS_CODE, msg, data);
    }

    public static <T> ReturnMessageDto<T> fail(String msg) {
        return new ReturnMessageDto<>(FAIL_CODE, msg);
    }


    public static <T> ReturnMessageDto<T> fail(String msg,T data) {
        return new ReturnMessageDto<>(FAIL_CODE, msg,data);
    }

    public boolean isSuccess() {
        return SUCCESS_CODE.equals(returnCode);
    }


    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMsg() {
        return returnMsg;
    }

    public void setReturnMsg(String returnMsg) {
        this.returnMsg = returnMsg;
    }

    public T getReturnObject() {
        return returnObject;
    }

    public void setReturnObject(T returnObject) {
        this.returnObject = returnObject;
    }

    public List<T> getReturnList() {
        return returnList;
    }

    public void setReturnList(List<T> returnList) {
        this.returnList = returnList;
    }
}
