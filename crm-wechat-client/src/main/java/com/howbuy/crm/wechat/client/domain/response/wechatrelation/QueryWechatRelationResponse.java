package com.howbuy.crm.wechat.client.domain.response.wechatrelation;

import com.howbuy.crm.wechat.client.producer.BaseResponse;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 根据外部用户ID列表和投顾号查询企业微信关系响应参数
 * <AUTHOR>
 * @date 2024/5/23 11:12
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryWechatRelationResponse extends BaseResponse {
    private static final long serialVersionUID = 1L;

    /**
     * 外部用户ID
     */
    private String gnUnionId;

    /**
     * 海外外部用户ID
     */
    private String hwUnionId;

    /**
     * companyNo	企业编码
     * {@link com.howbuy.crm.wechat.client.enums.CompanyNoEnum}
     */
    private String companyNo;
} 