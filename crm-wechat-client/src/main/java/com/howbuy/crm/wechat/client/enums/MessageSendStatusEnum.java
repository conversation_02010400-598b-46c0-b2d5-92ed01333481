package com.howbuy.crm.wechat.client.enums;

/**
 * @description:(消息发送状态， 1-未推送；2-推送中；3-推送成功；4-推送失败；5-重新推送)
 * @return
 * @author: haoran.zhang
 * @date: 2023/10/7 10:51
 * @since JDK 1.8
 */
public enum MessageSendStatusEnum {

    /**
     * 未推送
     */
    UNPUSH("1", "未推送"),
    /**
     * 推送中
     */
    PUSHING("2", "推送中"),
    /**
     * 推送成功
     */
    PUSH_SUCCESS("3", "推送成功"),
    /**
     * 推送失败
     */
    PUSH_FAIL("4", "推送失败"),
    /**
     * 重新推送
     */
    RE_PUSH("5", "重新推送");

    /**
     * 编码
     */
    private String code;


    /**
     * 描述
     */
    private String description;

    MessageSendStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 通过code获得
     *
     * @param code
     *            系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code) {
        MessageSendStatusEnum statusEnum=getEnum(code);
        return statusEnum==null?null :statusEnum.getDescription();
    }


    /**
     * 通过code直接返回 整个枚举类型
     *
     * @param code
     *            系统返回参数编码
     * @return BaseConstantEnum
     */
    public static MessageSendStatusEnum getEnum(String code) {
        for(MessageSendStatusEnum statusEnum : MessageSendStatusEnum.values()){
            if(statusEnum.getCode().equals(code)){
                return statusEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
