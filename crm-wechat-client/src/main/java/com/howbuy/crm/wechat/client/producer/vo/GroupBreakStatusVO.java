package com.howbuy.crm.wechat.client.producer.vo;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 查询群是否解散返回
 * @date 2024/1/19 15:35
 * @since JDK 1.8
 */
@Data
public class GroupBreakStatusVO implements Serializable {

    /**
     * 是否解散 0-否 1-是
     */
    private String groupBreakStatus;

    /**
     * 群解散时间
     */
    private String breakGroupTime;
}
