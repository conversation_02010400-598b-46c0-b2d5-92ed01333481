package com.howbuy.crm.wechat.client.enums;

/**
 * @description:(消息发送通道|方式， 1-企微群机器发送、 2-企微自建应用发送、3-邮箱、4-短信、5-带附件的邮箱)
 * @return
 * @author: haoran.zhang
 * @date: 2023/10/7 10:51
 * @since JDK 1.8
 */
public enum MessageSendChannelEnum {

    /**
     * 企微群机器发送
     */
    BOT_MESSAGE("1", "企微群机器发送"),
    NEW_APPLICATION_MESSAGE("2", "企微自建应用发送"),

    /**
     * 邮箱发送
     */
    EMAIL("3", "邮箱"),

    /**
     * 短信发送
     */
    SMS("4", "短信"),

    /**
     * 带附件的邮箱
     */
    EMAIL_WITH_ATTACHMENT("5", "带附件的邮箱"),

    /**
     * 好买基金APP 内的消息
     */
    HB_FUND_APP("6", "好买基金APP内的消息"),

    /**
     * 自动渠道-通过HBONENO
     *
     * 消息中心的渠道：邮箱、短信、企微等等
     * 具体发哪个渠道，这个是由 产品方 配置的。
     * 举个例子：指定的一个消息模版businessId，产品方 可以既选择发送邮箱、同时又发送短信。
     *
     * 所以，这个枚举，只负责调消息中心发送接口。具体发送渠道由配置决定，不直接指定。
     */
    AUTO_CHANNEL_BY_HBONENO("7", "自动渠道-通过HBONENO"),
    ;

    /**
     * 编码
     */
    private String code;


    /**
     * 描述
     */
    private String description;

    MessageSendChannelEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 通过code获得
     *
     * @param code
     *            系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code) {
        MessageSendChannelEnum statusEnum=getEnum(code);
        return statusEnum==null?null :statusEnum.getDescription();
    }


    /**
     * 通过code直接返回 整个枚举类型
     *
     * @param code
     *            系统返回参数编码
     * @return BaseConstantEnum
     */
    public static MessageSendChannelEnum getEnum(String code) {
        for(MessageSendChannelEnum statusEnum : MessageSendChannelEnum.values()){
            if(statusEnum.getCode().equals(code)){
                return statusEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
