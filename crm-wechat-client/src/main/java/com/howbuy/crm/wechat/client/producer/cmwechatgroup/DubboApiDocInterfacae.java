/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.producer.cmwechatgroup;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/6/5 6:01 PM
 * @since JDK 1.8
 */
public interface DubboApiDocInterfacae {

    /**
     * @api {DUBBO} org.apache.dubbo.metadata.MetadataService.getServiceDefinition()
     * @apiVersion 1.0.0
     * @apiName getServiceDefinition
     **/
    void getServiceDefinition();
    /**
     * @api {DUBBO} org.apache.dubbo.metadata.MetadataService.getMetadataURL()
     * @apiVersion 1.0.0
     * @apiName getServiceDefinition
     **/
    void getMetadataURL();

    /**
     * @api {DUBBO} org.apache.dubbo.metadata.MetadataService.isMetadataService()
     * @apiVersion 1.0.0
     * @apiName isMetadataService
     **/
    void isMetadataService();

    /**
     * @api {DUBBO} org.apache.dubbo.metadata.MetadataService.getMetadataInfo()
     * @apiVersion 1.0.0
     * @apiName getMetadataInfo
     **/
    void getMetadataInfo();

    /**
     * @api {DUBBO} org.apache.dubbo.metadata.MetadataService.getExportedURLs()
     * @apiVersion 1.0.0
     * @apiName getExportedURLs
     **/
    void getExportedURLs();

    /**
     * @api {DUBBO} org.apache.dubbo.metadata.MetadataService.toSortedStrings()
     * @apiVersion 1.0.0
     * @apiName toSortedStrings
     **/
    void toSortedStrings();

    /**
     * @api {DUBBO} org.apache.dubbo.metadata.MetadataService.serviceName()
     * @apiVersion 1.0.0
     * @apiName serviceName
     **/
    void serviceName();
    /**
     * @api {DUBBO} org.apache.dubbo.metadata.MetadataService.getMetadataInfos()
     * @apiVersion 1.0.0
     * @apiName getMetadataInfos
     **/
    void getMetadataInfos();

    /**
     * @api {DUBBO} org.apache.dubbo.metadata.MetadataService.getAndListenInstanceMetadata()
     * @apiVersion 1.0.0
     * @apiName getAndListenInstanceMetadata
     **/
    void getAndListenInstanceMetadata();

    /**
     * @api {DUBBO} org.apache.dubbo.metadata.MetadataService.getInstanceMetadataChangedListenerMap()
     * @apiVersion 1.0.0
     * @apiName getInstanceMetadataChangedListenerMap
     **/
    void getInstanceMetadataChangedListenerMap();

    /**
     * @api {DUBBO} org.apache.dubbo.metadata.MetadataService.exportInstanceMetadata()
     * @apiVersion 1.0.0
     * @apiName exportInstanceMetadata
     **/
    void exportInstanceMetadata();

    /**
     * @api {DUBBO} org.apache.dubbo.metadata.MetadataService.getSubscribedURLs()
     * @apiVersion 1.0.0
     * @apiName getSubscribedURLs
     **/
    void getSubscribedURLs();

    /**
     * @api {DUBBO} org.apache.dubbo.metadata.MetadataService.getExportedServiceURLs()
     * @apiVersion 1.0.0
     * @apiName getExportedServiceURLs
     **/
    void getExportedServiceURLs();

    /**
     * @api {DUBBO} org.apache.dubbo.metadata.MetadataService.version()
     * @apiVersion 1.0.0
     * @apiName version
     **/
    void version();
}