package com.howbuy.crm.wechat.client.domain.response.wechatrelation;

import com.howbuy.crm.wechat.client.producer.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Description:查询一账通与投顾绑定的企微信息批量返回
 * @Author: yun.lu
 * Date: 2025/5/29 18:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryWechatRelationBatchResponse extends BaseResponse {
    private static final long serialVersionUID = 4123232457343789L;

    /**
     * 关系对应信息数据
     */
    private List<RelationInfo> relationInfoList;
}
