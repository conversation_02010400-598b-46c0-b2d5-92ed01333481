package com.howbuy.crm.wechat.client.domain.response.wechatrelation;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class RelationInfo implements Serializable {
    private static final long serialVersionUID = 46653833457343789L;
    /**
     * 一账通
     */
    private String hbOneNo;
    /**
     * 投顾号
     */
    private String consCode;
    /**
     * 香港微信unionId
     */
    private String hkUnionId;
    /**
     * 国内微信unionId
     */
    private String gnUnionId;

    /**
     * companyNo	企业编码
     * {@link com.howbuy.crm.wechat.client.enums.CompanyNoEnum}
     */
    private String companyNo;
}