/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.producer.cmwechatgroup;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.producer.req.QueryUserGroupRequest;
import com.howbuy.crm.wechat.client.producer.vo.*;

import java.util.List;

/**
 * @description: (dubbo接口对外暴露)
 * <AUTHOR>
 * @date 2024/5/29 1:31 PM
 * @since JDK 1.8
 */
public interface CmWechatGroupQueryService {

    /**
     * @description 用户群数据查询接口
     * @param queryUserGroupRequest
     * @return
     * <AUTHOR>
     * @date 2024/5/29 1:56 PM
     * @since JDK 1.8
     */
    /**
     * @api {DUBBO} com.howbuy.crm.wechat.client.producer.cmwechatgroup.CmWechatGroupQueryService.queryUserGroupList(queryUserGroupRequest)
     * @apiVersion 1.0.0
     * @apiName 用户群数据查询接口
     * @apiDescription 用户群数据查询接口
     * @apiParam (queryUserGroupRequest) {com.howbuy.crm.wechat.client.producer.req.QueryUserGroupRequest} queryUserGroupRequest
     * @apiParam (queryUserGroupRequest) {String} queryUserGroupRequest.hbOneNo 一账通号
     * @apiParam (queryUserGroupRequest) {String} queryUserGroupRequest.wechatNickName 用户微信昵称
     * @apiParam (queryUserGroupRequest) {Array} queryUserGroupRequest.deptIdList 部门ID列表
     * @apiParam (queryUserGroupRequest) {Number} queryUserGroupRequest.pageNo 页码
     * @apiParam (queryUserGroupRequest) {Number} queryUserGroupRequest.pageSize 每页大小
     * @apiParamExample 请求体示例
     * {"hbOneNo":"w3","deptIdList":[7672],"pageNo":913,"pageSize":8157,"wechatNickName":"ia"}
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 0002-参数错误 9999-系统错误 0004-未查询到数据
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} returnObject 返回内容
     * @apiSuccess (响应结果) {Array} returnObject.userGroupDetailVOList 明细列表
     * @apiSuccess (响应结果) {String} returnObject.userGroupDetailVOList.hbOneNo 一账通号
     * @apiSuccess (响应结果) {String} returnObject.userGroupDetailVOList.wechatNickName 微信昵称
     * @apiSuccess (响应结果) {Array} returnObject.userGroupDetailVOList.groupIdList 客户当前所在群id列表
     * @apiSuccess (响应结果) {Array} returnObject.userGroupDetailVOList.hisGroupIdList 历史所在群id列表
     * @apiSuccess (响应结果) {String} returnObject.userGroupDetailVOList.addAssistantTime 添加助手时间 yyyyMMddHHmmss
     * @apiSuccess (响应结果) {Number} returnObject.totalNum 总条数
     * @apiSuccess (响应结果) {Number} returnObject.totalPage 总页数
     * @apiSuccessExample 响应结果示例
     * {"returnObject":{"userGroupDetailVOList":[{"hbOneNo":"I5p1m2Ndb2","groupIdList":["W"],"addAssistantTime":"ncVjO9IeA","wechatNickName":"0A9shqPMlO","hisGroupIdList":["YYJuis0RxZ"]}],"totalNum":4613,"totalPage":9512},"code":"Du8Bqj68J","description":"8XLBqP"}
     */
    Response<UserGroupVO> queryUserGroupList(QueryUserGroupRequest queryUserGroupRequest);

    /**
     * @description 查询客户当前所在群id接口
     * @param hbOneNo 一帐通
     * @return
     * <AUTHOR>
     * @date 2024/5/29 1:56 PM
     * @since JDK 1.8
     */
    /**
     * @api {DUBBO} com.howbuy.crm.wechat.client.producer.cmwechatgroup.CmWechatGroupQueryService.getGroupIdByHbOneNo(hbOneNo)
     * @apiVersion 1.0.0
     * @apiName 查询客户当前所在群id接口
     * @apiDescription 查询客户当前所在群id
     * @apiParam (hbOneNo) {String} hbOneNo 一账通号
     * @apiParamExample 请求参数示例
     * hbOneNo=VfopU
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 0002-参数错误 9999-系统错误
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} returnObject 返回内容
     * @apiSuccess (响应结果) {Array} returnObject.groupIdList 群id列表
     * @apiSuccess (响应结果) {String} returnObject.groupIdList.groupId 群id
     * @apiSuccess (响应结果) {String} returnObject.groupIdList.joinGroupTime 进群时间 yyyyMMddHHmmss
     * @apiSuccessExample 响应结果示例
     * {"returnObject":{"joinGroupTime":"CG5Ofni","groupId":"mH7nSkn"},"code":"dp","description":"JIjswfNy6"}
     */
    Response<GroupIdVO> getGroupIdByHbOneNo(String hbOneNo);


    /**
     * @description 查询客户加群结果接口
     * @param hbOneNo	一帐通
     * @param groupId 群id
     * @return
     * <AUTHOR>
     * @date 2024/5/29 1:57 PM
     * @since JDK 1.8
     */
    /**
     * @api {DUBBO} com.howbuy.crm.wechat.client.producer.cmwechatgroup.CmWechatGroupQueryService.getJoinGroupResult(hbOneNo,groupId)
     * @apiVersion 1.0.0
     * @apiName 查询客户加群结果接口
     * @apiDescription 查询客户加群结果
     * @apiParam (hbOneNo) {String} hbOneNo 一账通号
     * @apiParam (groupId) {String} groupId 群id
     * @apiParamExample 请求参数示例
     * hbOneNo=Xlo&groupId=rC
     * @apiSuccess (code) {String} code 返回码 0000-成功 0002-参数错误 9999-系统错误
     * @apiSuccess (description) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} returnObject 返回内容
     * @apiSuccess (joinGroupStatus) {String} returnObject.joinGroupStatus 入群结果：1-未入群 2-已入群 3-已退群(主动退群)
     * @apiSuccess (joinGroupTime) {String} returnObject.joinGroupTime 进群时间 yyyyMMddHHmmss
     * @apiSuccess (existGroupTime) {String} returnObject.existGroupTime 退群时间 yyyyMMddHHmmss
     * @apiSuccessExample 响应结果示例
     * {"returnObject":{"joinGroupTime":"ncp","joinGroupStatus":"UVCIswk8x","existGroupTime":"EB1J"},"code":"JHnEzgAKge","description":"oG"}
     */
    Response<JoinGroupResultVO> getJoinGroupResult(String hbOneNo, String groupId);


    /**
     * @description 查询群是否解散接口
     * @param groupId 群id
     * @return
     * <AUTHOR>
     * @date 2024/5/29 1:57 PM
     * @since JDK 1.8
     */
    /**
     * @api {DUBBO} com.howbuy.crm.wechat.client.producer.cmwechatgroup.CmWechatGroupQueryService.getGroupBreakStatus(groupId)
     * @apiVersion 1.0.0
     * @apiName 查询群是否解散接口
     * @apiParam (groupId) {String} groupId
     * @apiParamExample 请求参数示例
     * groupId=aaUjtX
     * @apiSuccess (code) {String} code 返回码 0000-成功 0002-参数错误 9999-系统错误 0004-未查询到数据
     * @apiSuccess (description) {String} description 返回描述
     * @apiSuccess (returnObject) {Object} returnObject 返回内容
     * @apiSuccess (groupBreakStatus) {String} returnObject.groupBreakStatus 是否解散 0-否 1-是
     * @apiSuccess (breakGroupTime) {String} returnObject.breakGroupTime 群解散时间
     * @apiSuccessExample 响应结果示例
     * {"returnObject":{"breakGroupTime":"rf","groupBreakStatus":"o1dVxGGI2"},"code":"lYCV","description":"9"}
     */
    Response<GroupBreakStatusVO> getGroupBreakStatus(String groupId);

    /**
     * @description 用户群数据查询接口
     * @param hbOneNo
     * @return
     * <AUTHOR>
     * @date 2024/5/29 1:56 PM
     * @since JDK 1.8
     */
    /**
     * @api {DUBBO} com.howbuy.crm.wechat.client.producer.cmwechatgroup.CmWechatGroupQueryService.queryUserGroupInfoByHbOneNo(hbOneNo)
     * @apiVersion 1.0.0
     * @apiName queryUserGroupInfoByHbOneNo()
     * @apiParam (hbOneNo) {String} hbOneNo
     * @apiParamExample 请求参数示例
     * hbOneNo=8
     * @apiSuccess (code) {String} code 返回码 0000-成功 0002-参数错误 9999-系统错误 0004-未查询到数据
     * @apiSuccess (description) {String} description 返回描述
     * @apiSuccess (returnObject) {Array} returnObject 返回内容
     * @apiSuccess (status) {String} returnObject.status
     * @apiSuccess (exitGroupTime) {String} returnObject.exitGroupTime 退群时间 yyyyMMddHHmmss
     * @apiSuccess (joinGroupTime) {String} returnObject.joinGroupTime 入群时间 yyyyMMddHHmmss
     * @apiSuccess (groupId) {String} returnObject.groupId 群id
     * @apiSuccessExample 响应结果示例
     * {"returnObject":[{"joinGroupTime":"RGF5Bcnv","exitGroupTime":"xAlv","groupId":"luk","status":"zdzexaa"}],"code":"UDaI5LH","description":"npKd7uV0"}
     */
    Response<List<UserGroupInfoVO>> queryUserGroupInfoByHbOneNo(String hbOneNo);
}