/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.enums;

/**
 * <AUTHOR>
 * @Description 企微上传文件类型枚举
 * @Date 2024/9/12 14:36
 */
public enum WechatUploadTypeEnum {

    /**
     * 图片
     */
    IMAGE("图片", "image"),

    /**
     * 语音
     */
    VOICE("语音", "voice"),

    /**
     * 视频
     */
    VIDEO("视频", "video"),

    /**
     * 普通文件
     */
    FILE("普通文件", "file"),

    ;

    private String name;
    private String code;

    WechatUploadTypeEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public static WechatUploadTypeEnum getByCode(String code) {
        for (WechatUploadTypeEnum wechatUploadTypeEnum : WechatUploadTypeEnum.values()) {
            if (wechatUploadTypeEnum.getCode().equals(code)) {
                return wechatUploadTypeEnum;
            }
        }
        return null;
    }
}
