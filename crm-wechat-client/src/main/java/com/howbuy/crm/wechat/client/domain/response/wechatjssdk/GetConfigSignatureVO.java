/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.domain.response.wechatjssdk;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/9/4 14:36
 * @since JDK 1.8
 */
@Data
public class GetConfigSignatureVO implements Serializable {

    private static final long serialVersionUID = -6216099670931861958L;
    /**
     * 时间戳
     */
    private String timestamp;
    /**
     * 随机字符
     */
    private String nonceStr;
    /**
     * 签名
     */
    private String signature;

    /**
     * app-时间戳
     */
    private String appTimestamp;
    /**
     * app-随机字符
     */
    private String appNonceStr;
    /**
     * app-签名
     */
    private String appSignature;
}
