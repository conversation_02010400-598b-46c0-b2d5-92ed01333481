/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.domain.response.wechatcustinfo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/9/6 11:17
 * @since JDK 1.8
 */
@Data
public class WechatCustInfoVO implements Serializable {

    /**
     * 一账通号
     */
    private String hboneNo;

    /**
     * 微信UnionId
     */
    private String unionid;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 企业编码1好买财富2好买基金
     */
    private String companyNo;

    /**
     * 外部应用用户ID
     */
    private String externalUserId;

    /**
     * 微信头像
     */
    private String wechatAvatar;
}
