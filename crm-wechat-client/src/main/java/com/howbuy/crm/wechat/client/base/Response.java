/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.base;

import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/5/27 3:00 PM
 * @since JDK 1.8
 */
@Data
public class Response<T> implements Serializable {
    /**
     * 返回码 0000-成功 C0510002-参数错误 C0510003-系统错误 C0510004-未查询到数据
     */
    private String code;

    /**
     * 返回描述
     */
    private String description;

    /**
     * 返回内容
     */
    private T returnObject;

    public Response(String code, String description, T returnObject) {
        this.code = code;
        this.description = description;
        this.returnObject = returnObject;
    }

    public Response() {

    }

    public static <T> Response<T> ok(T data) {
        return new Response<T>(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDescription(), data);
    }

    /**
     * @return
     * @description: 是否成功
     * @author: hongdong.xie
     * @date: 2023/11/13 18:16
     * @since JDK 1.8
     */

    public boolean isSuccess() {
        return ResponseCodeEnum.SUCCESS.getCode().equals(this.getCode());
    }
}