package com.howbuy.crm.wechat.client.domain.request.wechatpushmsg;

import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;
import java.util.List;

/**
 * @description: 群发消息请求参数
 * <AUTHOR>
 * @date 2024/9/6
 * @since JDK 1.8
 */
@Getter
@Setter
public class SendGroupMessageRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /** 消息类型 */
    private String messageType;
    /** 消息标题 */
    private String title;
    /**
     * 企业微信投顾号
     */
    private String wechatConsCode;
    /**
     * 资配报告ID
     */
    private String asseetId;
    /** 跳转链接 */
    private String url;
    /** 客户号列表 */
    private List<String> custNos;
    /** 消息内容 */
    private String messageContent;
} 