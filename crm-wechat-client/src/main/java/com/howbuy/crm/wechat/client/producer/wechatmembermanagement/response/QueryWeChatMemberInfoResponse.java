/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.producer.wechatmembermanagement.response;

import com.howbuy.crm.wechat.client.producer.BaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 获取微信会员信息
 * <AUTHOR>
 * @date 2024/6/20 13:21
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryWeChatMemberInfoResponse extends BaseResponse implements Serializable {

    private static final long serialVersionUID = -8286518246549030915L;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 员工个人二维码，扫描可添加为外部联系人
     */
    private String qrCode;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 头像缩略图url
     */
    private String thumbAvatar;

    /**
     * 企微微信二维码 base64字符串（将头像缩略图放置到二维码的中间）
     */
    private String enterpriseWechatQrImageStr;

    /**
     * 企微微信二维码 base64字符串
     */
    private String enterpriseWechatQrNoImageStr;
}
