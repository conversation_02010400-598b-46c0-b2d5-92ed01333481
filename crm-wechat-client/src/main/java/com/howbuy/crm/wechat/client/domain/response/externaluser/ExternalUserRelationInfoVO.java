/**
 * Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.domain.response.externaluser;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 外部客户与企业微信成员绑定关系信息
 * @date 2025-08-19 19:27:20
 * @since JDK 1.8
 */
@Data
public class ExternalUserRelationInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 添加了此外部联系人的企业成员userid
     */
    private String userid;

    /**
     * 外部联系人的userid
     */
    private String externalUserId;

    /**
     * 企业成员对外部联系人备注
     */
    private String remark;

    /**
     * 企业成员对外部联系人描述
     */
    private String description;

    /**
     * 企业成员添加外部联系人时间
     */
    private Date createtime;

    /**
     * 企业成员对外部联系人备注的企业名称
     */
    private String remarkCorpName;
}