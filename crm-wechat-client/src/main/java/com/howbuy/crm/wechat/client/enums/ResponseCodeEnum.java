/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.enums;

/**
 * @description: (响应返回码)
 * <AUTHOR>
 * @date 2024/5/31 2:40 PM
 * @since JDK 1.8
 */
public enum ResponseCodeEnum {
    //执行成功
    SUCCESS("0000","操作成功"),
    //参数错误
    PARAM_ERROR("C0510002","参数错误"),
    //执行失败
    FAIL("C0510001","操作失败"),
    //系统错误
    SYS_ERROR("C0510003","系统错误"),

    /*
    * 配置错误
     */
    SYS_CONFIG_ERROR("C0510006","企微应用配置错误"),

    //没有查询到匹配数据
    NULL_ERROR("C0510004","没有查询到匹配数据"),

    MORE_DATA("C0510005","查询到多条匹配数据"),

    CALL_DUBBO_ERROR("C0519997","调用其他DUBBO服务时出现错误。"),

    CALL_HSB_ERROR("C0519998","调用其他HSB服务时出现错误。"),

    UNKNOWN_ERROR("C0519999","未定义的错误");


    private String code;	//编码

    private String description;  //描述

    private ResponseCodeEnum(String code, String description){
        this.code=code;
        this.description=description;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}