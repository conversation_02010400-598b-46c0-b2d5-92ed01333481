/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.domain.response.wechatauth;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/9/19 9:09
 * @since JDK 1.8
 */
@Data
public class GetUserIdVO implements Serializable {

    private static final long serialVersionUID = 9052483537724377021L;
    /**
     * 用户ID
     */
    private String userId;

}
