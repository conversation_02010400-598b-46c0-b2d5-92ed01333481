/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.domain.response.externaluser;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 外部联系人自定义展示信息
 * @date 2025-08-19 19:27:20
 * @since JDK 1.8
 */
@Data
public class ExternalUserProfileInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * profile自定义类型
     */
    private String type;

    /**
     * profile自定义名称
     */
    private String name;

    /**
     * profile的详细属性
     */
    private Map<String,List<Map<String,String>>> profileMap;
}