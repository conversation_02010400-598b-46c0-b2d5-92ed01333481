/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.facade.group;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.group.GetGroupInfoByUserIdRequest;
import com.howbuy.crm.wechat.client.domain.request.group.GetGroupInfoByChatIdRequest;
import com.howbuy.crm.wechat.client.domain.request.group.GetGroupInfoByExternalUserIdRequest;
import com.howbuy.crm.wechat.client.domain.response.group.GroupChatInfoVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 微信群管理接口
 * @date 2025-08-19 19:05:03
 * @since JDK 1.8
 */
public interface WechatGroupFacade {

    /**
     * @api {DUBBO} com.howbuy.crm.wechat.client.facade.group.WechatGroupFacade.getGroupInfoByUserId(request) getGroupInfoByUserId()
     * @apiVersion 1.0.0
     * @apiGroup WechatGroupFacade
     * @apiName getGroupInfoByUserId()
     * @apiDescription 根据员工账号查询所有有他的群
     * @apiParam (请求参数) {String} userId 员工账号
     * @apiParamExample 请求参数示例
     * userId=EMdAcJsUBw
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 ********-参数错误 ********-系统错误 ********-未查询到数据
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Array} returnObject 返回内容
     * @apiSuccess (响应结果) {String} returnObject.chatId 客户群ID
     * @apiSuccess (响应结果) {String} returnObject.chatName 群名
     * @apiSuccess (响应结果) {String} returnObject.chatOwner 群主ID
     * @apiSuccess (响应结果) {Number} returnObject.createTime 群的创建时间
     * @apiSuccess (响应结果) {String} returnObject.notice 群公告
     * @apiSuccess (响应结果) {Array} returnObject.chatMemberList 群成员列表
     * @apiSuccess (响应结果) {Array} returnObject.adminUserIdList 群管理员userid列表
     * @apiSuccessExample 响应结果示例
     * {"returnObject":[{"chatId":"Qy8VyWQfjr","chatName":"Rde","chatOwner":"ZKeGb8QGm7","createTime":1365663433809,"notice":"qdhSZ","chatMemberList":[],"adminUserIdList":["ibAbK8"]}],"code":"0000","description":"操作成功"}
     */
    Response<List<GroupChatInfoVO>> getGroupInfoByUserId(GetGroupInfoByUserIdRequest request);

    /**
     * @api {DUBBO} com.howbuy.crm.wechat.client.facade.group.WechatGroupFacade.getGroupInfoByChatId(request) getGroupInfoByChatId()
     * @apiVersion 1.0.0
     * @apiGroup WechatGroupFacade
     * @apiName getGroupInfoByChatId()
     * @apiDescription 根据群ID获取群信息
     * @apiParam (请求参数) {String} chatId 客户群ID
     * @apiParam (请求参数) {String} corpId 企业微信corpId
     * @apiParam (请求参数) {String} corpSecret 企业微信corpSecret
     * @apiParamExample 请求参数示例
     * chatId=Hey&corpId=123&corpSecret=abc
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 ********-参数错误 ********-系统错误 ********-未查询到数据
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} returnObject 返回内容
     * @apiSuccess (响应结果) {String} returnObject.chatId 客户群ID
     * @apiSuccess (响应结果) {String} returnObject.chatName 群名
     * @apiSuccess (响应结果) {String} returnObject.chatOwner 群主ID
     * @apiSuccess (响应结果) {Number} returnObject.createTime 群的创建时间
     * @apiSuccess (响应结果) {String} returnObject.notice 群公告
     * @apiSuccess (响应结果) {Array} returnObject.chatMemberList 群成员列表
     * @apiSuccess (响应结果) {Array} returnObject.adminUserIdList 群管理员userid列表
     * @apiSuccessExample 响应结果示例
     * {"returnObject":{"chatId":"xy01ujvFB","chatName":"zyFAt","chatOwner":"geM5Re","createTime":1311568597692,"notice":"Z28dV","chatMemberList":[],"adminUserIdList":["HhKZ3I00"]},"code":"0000","description":"操作成功"}
     */
    Response<GroupChatInfoVO> getGroupInfoByChatId(GetGroupInfoByChatIdRequest request);

    /**
     * @api {DUBBO} com.howbuy.crm.wechat.client.facade.group.WechatGroupFacade.getGroupInfoByExternalUserId(request) getGroupInfoByExternalUserId()
     * @apiVersion 1.0.0
     * @apiGroup WechatGroupFacade
     * @apiName getGroupInfoByExternalUserId()
     * @apiDescription 根据外部客户ID查询所有有他的群
     * @apiParam (请求参数) {String} externalUserId 外部客户ID
     * @apiParam (请求参数) {String} companyNo 企微-企业主体,1-好买财富,2-好买基金，3-好晓买
     * @apiParamExample 请求参数示例
     * externalUserId=EMdAcJsUBw&companyNo=ww1234567890abcdef
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 ********-参数错误 ********-系统错误 ********-未查询到数据
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Array} returnObject 返回内容
     * @apiSuccess (响应结果) {String} returnObject.chatId 客户群ID
     * @apiSuccess (响应结果) {String} returnObject.chatName 群名
     * @apiSuccess (响应结果) {String} returnObject.chatOwner 群主ID
     * @apiSuccess (响应结果) {Number} returnObject.createTime 群的创建时间
     * @apiSuccess (响应结果) {String} returnObject.notice 群公告
     * @apiSuccess (响应结果) {Array} returnObject.chatMemberList 群成员列表
     * @apiSuccess (响应结果) {Array} returnObject.adminUserIdList 群管理员userid列表
     * @apiSuccessExample 响应结果示例
     * {"returnObject":[{"chatId":"Qy8VyWQfjr","chatName":"Rde","chatOwner":"ZKeGb8QGm7","createTime":1365663433809,"notice":"qdhSZ","chatMemberList":[],"adminUserIdList":["ibAbK8"]}],"code":"0000","description":"操作成功"}
     */
    Response<List<GroupChatInfoVO>> getGroupInfoByExternalUserId(GetGroupInfoByExternalUserIdRequest request);
}