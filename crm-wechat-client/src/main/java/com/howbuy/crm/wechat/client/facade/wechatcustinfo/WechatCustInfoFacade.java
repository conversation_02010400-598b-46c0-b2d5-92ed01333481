/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.facade.wechatcustinfo;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.wechatcustinfo.QueryWechatAddRelationRequest;
import com.howbuy.crm.wechat.client.domain.request.wechatcustinfo.QueryWechatCustInfoRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatcustinfo.WechatCustAddInfoVO;
import com.howbuy.crm.wechat.client.domain.response.wechatcustinfo.WechatCustInfoVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 微信客户信息服务
 * @date 2024/9/6 11:06
 * @since JDK 1.8
 */
public interface WechatCustInfoFacade {

    /**
     * @api {DUBBO}  com.howbuy.crm.wechat.client.facade.wechatcustinfo.WechatCustInfoFacade.queryWechatCustInfo(request) queryWechatCustInfo()
     * @apiVersion 1.0.0
     * @apiGroup WechatCustInfoFacade
     * @apiName queryWechatCustInfo()
     * @apiDescription 查询微信客户信息
     * @apiParam (请求参数) {String} externalUserId 外部联系人id
     * @apiParam (请求参数) {String} hboneNo 一账通号
     * @apiParamExample 请求参数示例
     * externalUserId=zezuw
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 C0510002-参数错误 C0510003-系统错误 C0510004-未查询到数据
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} returnObject 返回内容
     * @apiSuccess (响应结果) {String} returnObject.hboneNo 一账通号
     * @apiSuccess (响应结果) {String} returnObject.unionid 微信UnionId
     * @apiSuccess (响应结果) {String} returnObject.nickName 昵称
     * @apiSuccess (响应结果) {String} returnObject.companyNo 企业编码1好买财富2好买基金
     * @apiSuccess (响应结果) {String} returnObject.externalUserId 外部应用用户ID
     * @apiSuccess (响应结果) {String} returnObject.wechatAvatar 微信头像
     * @apiSuccessExample 响应结果示例
     * {"returnObject":{"wechatAvatar":"Z","unionid":"Y26","companyNo":"lN","externalUserId":"XsNKEb","hboneNo":"jS7ZZpy"},"code":"3opGi41Y","description":"8bGAgE"}
     */
    Response<WechatCustInfoVO> queryWechatCustInfo(QueryWechatCustInfoRequest request);


    /**
     * @api {DUBBO}  com.howbuy.crm.wechat.client.facade.wechatcustinfo.WechatCustInfoFacade.queryListWechatCustInfo(request) queryWechatCustInfo()
     * @apiVersion 1.0.0
     * @apiGroup WechatCustInfoFacade
     * @apiName queryListWechatCustInfo()
     * @apiDescription 查询微信客户信息
     * @apiParam (请求参数) {String} externalUserId 外部联系人id
     * @apiParam (请求参数) {String} hboneNo 一账通号
     * @apiParamExample 请求参数示例
     * externalUserId=zezuw
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 C0510002-参数错误 C0510003-系统错误 C0510004-未查询到数据
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} returnObject 返回内容
     * @apiSuccess (响应结果) {String} returnObject.hboneNo 一账通号
     * @apiSuccess (响应结果) {String} returnObject.unionid 微信UnionId
     * @apiSuccess (响应结果) {String} returnObject.nickName 昵称
     * @apiSuccess (响应结果) {String} returnObject.companyNo 企业编码1好买财富2好买基金
     * @apiSuccess (响应结果) {String} returnObject.externalUserId 外部应用用户ID
     * @apiSuccess (响应结果) {String} returnObject.wechatAvatar 微信头像
     * @apiSuccessExample 响应结果示例
     * {"returnObject":{"wechatAvatar":"Z","unionid":"Y26","companyNo":"lN","externalUserId":"XsNKEb","hboneNo":"jS7ZZpy"},"code":"3opGi41Y","description":"8bGAgE"}
     */
    Response<List<WechatCustInfoVO>> queryListWechatCustInfo(QueryWechatCustInfoRequest request);

    /**
     * @api {DUBBO} com.howbuy.crm.wechat.client.facade.wechatcustinfo.WechatCustInfoFacade.queryWechatAddRelationList(request) queryWechatAddRelationList()
     * @apiVersion 1.0.0
     * @apiGroup WechatCustInfoFacade
     * @apiName queryWechatAddRelationList
     * @apiDescription 查询微信客户添加关系列表
     *
     * @apiParam (请求参数) {String} externalUserId 外部联系人id
     * @apiParam (请求参数) {String} hboneNo 一账通号
     * @apiParam (请求参数) {String} [companyNo] 企业编码1好买财富2好买基金
     *
     * @apiParamExample 请求参数示例
     * {
     *   "externalUserId": "ext123",
     *   "hboneNo": "123456",
     *   "companyNo": "1"
     * }
     *
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 C0510002-参数错误 C0510003-系统错误 C0510004-未查询到数据
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} returnObject 返回内容
     * @apiSuccess (响应结果) {String} returnObject.hboneNo 一账通号
     * @apiSuccess (响应结果) {String} returnObject.unionid 微信UnionId
     * @apiSuccess (响应结果) {String} returnObject.nickName 昵称
     * @apiSuccess (响应结果) {String} returnObject.companyNo 企业编码1好买财富2好买基金
     * @apiSuccess (响应结果) {String} returnObject.externalUserId 外部应用用户ID
     * @apiSuccess (响应结果) {String} returnObject.wechatAvatar 微信头像
     * @apiSuccess (响应结果) {Array} returnObject.relationList 添加关系列表
     * @apiSuccess (响应结果) {String} returnObject.relationList.conscode 投顾号
     * @apiSuccess (响应结果) {String} returnObject.relationList.status 状态,1-新增，2-删除客户；3-被客户删除
     * @apiSuccess (响应结果) {Date} returnObject.relationList.addTime 添加时间
     * @apiSuccess (响应结果) {Date} returnObject.relationList.delTime 删除时间
     *
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "成功",
     *   "returnObject": {
     *     "hboneNo": "123456",
     *     "unionid": "union123",
     *     "nickName": "张三",
     *     "companyNo": "1",
     *     "externalUserId": "ext123",
     *     "wechatAvatar": "http://example.com/avatar.jpg",
     *     "relationList": [{
     *       "conscode": "CONS001",
     *       "status": "1",
     *       "addTime": "2024-05-23 11:12:00",
     *       "delTime": null
     *     }]
     *   }
     * }
     *
     * @apiError (错误码) C0510002 参数错误
     * @apiError (错误码) C0510003 系统错误
     * @apiError (错误码) C0510004 未查询到数据
     *
     * @apiErrorExample 错误响应示例
     * {
     *   "code": "C0510002",
     *   "description": "参数错误",
     *   "returnObject": null
     * }
     */
    Response<WechatCustAddInfoVO> queryWechatAddRelationList(QueryWechatAddRelationRequest request);
}
