/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.domain.response.custrelation;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 批量查询客户hboneNo和投顾consCode关系响应参数
 * @date 2025-08-19 19:52:46
 * @since JDK 1.8
 */
@Data
public class SelectRelationListByVoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关系列表
     */
    private List<RelationInfo> relationList;

    /**
     * 关系信息
     */
    @Data
    public static class RelationInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * id
         */
        private Long id;

        /**
         * 客户信息-外部应用用户ID
         */
        private String externalUserId;

        /**
         * 客户信息-一账通号
         */
        private String hboneNo;

        /**
         * 客户信息-微信UnionId
         */
        private String unionid;

        /**
         * 投顾号
         */
        private String conscode;

        /**
         * 状态1新增  2删除客户   3被客户删除
         */
        private String status;

        /**
         * 添加时间
         */
        private Long addTime;

        /**
         * 删除时间
         */
        private Long delTime;

        /**
         * 企业编码1好买财富2好买基金
         */
        private String companyNo;

        /**
         * 添加客户的来源
         */
        private String addWay;

        /**
         * 添加客户的渠道
         */
        private String state;
    }
}