/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.domain.request.custrelation;

import com.howbuy.crm.wechat.client.producer.BaseRequest;
import com.howbuy.crm.wechat.client.domain.vo.custrelation.CustConsultRelationVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @description: 批量查询客户hboneNo和投顾consCode关系请求参数
 * <AUTHOR>
 * @date 2025-08-19 19:52:46
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SelectRelationListByVoRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 客户咨询关系列表
     */
    private List<CustConsultRelationVO> custConsultRelationVoList;

    /**
     * 企微-企业主体,1-好买财富,2-好买基金，3-好晓买
     */
    private String companyNo;
}