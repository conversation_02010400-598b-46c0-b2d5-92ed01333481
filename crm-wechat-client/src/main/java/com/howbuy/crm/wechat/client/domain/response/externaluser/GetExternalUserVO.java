/**
 * Copyright (c) 2024, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.domain.response.externaluser;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 获取企微外部联系人详情响应
 * @date 2025-08-19 19:27:20
 * @since JDK 1.8
 */
@Data
public class GetExternalUserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 外部联系人的自定义展示信息
     */
    private List<ExternalUserProfileInfoVO> externalUserProfileList;

    /**
     * 外部客户与企业微信成员绑定关系列表
     */
    private List<ExternalUserRelationInfoVO> followUserList;

    /**
     * 国内一账通账号
     */
    private String hboneNo;

    /**
     * 香港微信对应的一帐通号
     */
    private String hkHboneNo;
}