/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.domain.request.wechatcustinfo;

import com.howbuy.crm.wechat.client.base.BaseCompanyNoRequest;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/9/6 11:09
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class QueryWechatAddRelationRequest extends BaseCompanyNoRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 一账通号
     */
    private String hboneNo;

    /**
     * 投顾的列表
     */
    private List<String> consCodeList;
}
