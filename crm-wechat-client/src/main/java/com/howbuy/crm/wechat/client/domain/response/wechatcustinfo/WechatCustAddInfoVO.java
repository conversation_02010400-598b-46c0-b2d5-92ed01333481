/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.domain.response.wechatcustinfo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/6/10 17:46
 * @since JDK 1.8
 */
public class WechatCustAddInfoVO implements Serializable {

    /**
     * 企微关系数据了列表
     */
    private List<RealtionVO> realtionVOList;

    public static class RealtionVO implements Serializable{

        /**
         * 投顾的编码
         */
        private String consCode;

        /**
         * 添加时间
         */
        private Date addTime;

        /**
         * 企业编码1好买财富2好买基金
         */
        private String companyNo;

        public String getConsCode() {
            return consCode;
        }

        public void setConsCode(String consCode) {
            this.consCode = consCode;
        }


        public Date getAddTime() {
            return addTime;
        }

        public void setAddTime(Date addTime) {
            this.addTime = addTime;
        }

        public String getCompanyNo() {
            return companyNo;
        }

        public void setCompanyNo(String companyNo) {
            this.companyNo = companyNo;
        }
    }

    public List<RealtionVO> getRealtionVOList() {
        return realtionVOList;
    }

    public void setRealtionVOList(List<RealtionVO> realtionVOList) {
        this.realtionVOList = realtionVOList;
    }
}