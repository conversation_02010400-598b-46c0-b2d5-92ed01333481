/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.enums;

import java.util.stream.Stream;

/**
 * @description:  加群状态枚举
 * <AUTHOR>
 * @date 2024/1/27 16:38
 * @since JDK 1.8
 */
public enum JoinGroupStatusEnum {

    UN_JOIN("1","未入群"),
    ALREADY_JOIN("2","已入群"),
    LEAVE_JOIN("3","已退群"),;

    private String key;


    private String  name;

    JoinGroupStatusEnum(String key, String name) {
        this.key = key;
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public String getName() {
        return name;
    }

    /**
     * @param status
     * @return
     */
    public static JoinGroupStatusEnum getJoinGroupStatusEnum(String status) {
        return Stream.of(JoinGroupStatusEnum.values()).filter(tmp -> tmp.name().equals(status)).findFirst().orElse(null);
    }
}
