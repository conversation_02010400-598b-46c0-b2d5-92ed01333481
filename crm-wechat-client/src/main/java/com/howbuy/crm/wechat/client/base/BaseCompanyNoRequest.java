/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.base;

import com.howbuy.crm.wechat.client.producer.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @description: (基于[companyNo-企业编码]的 base请求类})
 * <AUTHOR>
 * @date 2025/7/22 9:48
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseCompanyNoRequest extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * companyNo	企业编码
     * 1-好买财富，2-好买基金，3-好晓买
     */
    private String companyNo;

}