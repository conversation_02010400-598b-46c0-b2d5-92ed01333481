/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.domain.request.group;

import com.howbuy.crm.wechat.client.producer.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: 根据群ID获取群信息请求参数
 * <AUTHOR>
 * @date 2025-08-19 19:05:03
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetGroupInfoByChatIdRequest extends BaseRequest {

    private static final long serialVersionUID = 1234567890123456790L;
    
    /**
     * 客户群ID
     */
    private String chatId;
    
    /**
     * 企业微信corpId
     */
    private String corpId;
    
    /**
     * 企业微信corpSecret
     */
    private String corpSecret;

    /**
     * 企微-企业主体,1-好买财富,2-好买基金，3-好晓买
     */
    private String companyNo;
}