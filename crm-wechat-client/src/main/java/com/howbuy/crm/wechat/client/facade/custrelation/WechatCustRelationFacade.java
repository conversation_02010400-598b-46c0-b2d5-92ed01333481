/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.facade.custrelation;

import com.howbuy.crm.wechat.client.domain.request.custrelation.SelectRelationListByVoRequest;
import com.howbuy.crm.wechat.client.domain.response.custrelation.SelectRelationListByVoVO;
import com.howbuy.crm.wechat.client.base.Response;

/**
 * <AUTHOR>
 * @description: 企业微信客户关系管理接口
 * @date 2025-08-19 19:52:46
 * @since JDK 1.8
 */
public interface WechatCustRelationFacade {

    /**
     * @api {DUBBO} com.howbuy.crm.wechat.client.facade.custrelation.WechatCustRelationFacade.selectRelationListByVo(request) selectRelationListByVo()
     * @apiVersion 1.0.0
     * @apiGroup WechatCustRelationFacade
     * @apiName selectRelationListByVo()
     * @apiDescription 批量查询客户hboneNo和投顾consCode关系
     * @apiParam (请求参数) {List} custConsultRelationVoList 客户咨询关系列表
     * @apiParam (请求参数) {String} companyNo 企微-企业主体,1-好买财富,2-好买基金，3-好晓买
     * @apiParamExample 请求参数示例
     * custConsultRelationVoList=[{"conscode":"t","hboneNo":"AVD8"}]
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 C0510002-参数错误 C0510003-系统错误 C0510004-未查询到数据
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} returnObject 返回内容
     * @apiSuccess (响应结果) {Array} returnObject.relationList 关系列表
     * @apiSuccess (响应结果) {Number} returnObject.relationList.id id
     * @apiSuccess (响应结果) {String} returnObject.relationList.externalUserId 客户信息-外部应用用户ID
     * @apiSuccess (响应结果) {String} returnObject.relationList.hboneNo 客户信息-一账通号
     * @apiSuccess (响应结果) {String} returnObject.relationList.unionid 客户信息-微信UnionId
     * @apiSuccess (响应结果) {String} returnObject.relationList.conscode 投顾号
     * @apiSuccess (响应结果) {String} returnObject.relationList.status 状态1新增  2删除客户   3被客户删除
     * @apiSuccess (响应结果) {Number} returnObject.relationList.addTime 添加时间
     * @apiSuccess (响应结果) {Number} returnObject.relationList.delTime 删除时间
     * @apiSuccess (响应结果) {String} returnObject.relationList.companyNo 企业编码1好买财富2好买基金
     * @apiSuccess (响应结果) {String} returnObject.relationList.addWay 添加客户的来源
     * @apiSuccess (响应结果) {String} returnObject.relationList.state 添加客户的渠道
     * @apiSuccessExample 响应结果示例
     * {"returnObject":{"relationList":[{"unionid":"UOHjML","addTime":2995865321154,"companyNo":"SBWNV","externalUserId":"ucxnUUut6","addWay":"j","delTime":1351503081579,"id":1390,"state":"1xRF2","conscode":"o1","hboneNo":"oVgbtOYf","status":"GZVHN"}]},"code":"0000","description":"操作成功"}
     */
    Response<SelectRelationListByVoVO> selectRelationListByVo(SelectRelationListByVoRequest request);
}