/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.domain.response.group;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 客户群跟进状态响应对象
 * @date 2025-08-19 19:05:03
 * @since JDK 1.8
 */
@Data
public class GroupChatInfoVO implements Serializable {

    private static final long serialVersionUID = 1234567890123456793L;

    /**
     * 客户群ID
     */
    private String chatId;

    /**
     * 群名
     */
    private String chatName;

    /**
     * 群主ID
     */
    private String chatOwner;

    /**
     * 群的创建时间
     */
    private Date createTime;

    /**
     * 群公告
     */
    private String notice;

    /**
     * 群成员列表
     */
    private List<GroupChatMemberInfoVO> chatMemberList;

    /**
     * 群管理员userid列表
     */
    private List<String> adminUserIdList;
}