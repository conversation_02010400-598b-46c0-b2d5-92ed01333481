package com.howbuy.crm.wechat.client.producer.vo;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 查询客户加群结果返回
 * @date 2024/1/19 15:25
 * @since JDK 1.8
 */
@Data
public class JoinGroupResultVO implements Serializable {

    /**
     * 入群结果：1-未入群 2-已入群 3-已退群(主动退群)
     */
    private String joinGroupStatus;

    /**
     * 进群时间 yyyyMMddHHmmss
     */
    private String joinGroupTime;

    /**
     * 退群时间 yyyyMMddHHmmss
     */
    private String existGroupTime;
}
