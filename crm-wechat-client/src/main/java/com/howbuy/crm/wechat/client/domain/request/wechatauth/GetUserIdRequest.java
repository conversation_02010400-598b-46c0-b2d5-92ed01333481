/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.domain.request.wechatauth;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.wechat.client.base.BaseApplicationRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/9/19 9:08
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetUserIdRequest extends BaseApplicationRequest {
    private static final long serialVersionUID = -305937283357955775L;

    /**
     * 通过成员授权获取到的code
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "通过成员授权获取到的code", isRequired = true)
    private String code;

}
