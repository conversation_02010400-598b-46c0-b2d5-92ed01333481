/**
 * Copyright (c) 2024, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.producer.userrelation;

import com.howbuy.crm.wechat.client.producer.BaseFacade;
import com.howbuy.crm.wechat.client.producer.userrelation.request.QueryWechatUserRelationRequest;
import com.howbuy.crm.wechat.client.producer.userrelation.response.QueryWechatUserRelationResponse;

/**
 * @description:  根据一账通号查询客户与企业微信用户关系服务接口
 * <AUTHOR>
 * @date 2024/8/30 17:46
 * @since JDK 1.8
 */
public interface QueryWechatUserRelationService extends BaseFacade<QueryWechatUserRelationRequest, QueryWechatUserRelationResponse>{
/**
 * @api {DUBBO} com.howbuy.crm.wechat.client.producer.userrelation.QueryWechatUserRelationService.execute(request) 根据一账通号查询客户企微好友关系接口
 * @apiVersion 1.0.0
 * @apiGroup QueryWechatUserRelationService
 * @apiName 根据一账通号查询客户企微好友关系接口
 * @apiParam (请求参数) {String} hboneNo 客户一账通号
 * @apiParam (请求参数) {Array} [userIdList] 企业微信用户ID
 * @apiParamExample 请求参数示例
 * traceId=7SLXiPM&userIdList=Ml2k65&hboneNo=AFlJ1o2z
 * @apiSuccess (响应结果) {String} code 返回码 0000-成功 ********-参数错误 ********-系统错误 ********-未查询到数据
 * @apiSuccess (响应结果) {String} description 返回描述
 * @apiSuccess (响应结果) {Object} returnObject 返回内容
 * @apiSuccess (响应结果) {String} returnObject.nickName 微信用户昵称
 * @apiSuccess (响应结果) {Array} returnObject.userRelationList 企业微信用户好友关系
 * @apiSuccess (响应结果) {String} returnObject.userRelationList.userId 企业微信用户ID
 * @apiSuccess (响应结果) {String} returnObject.userRelationList.externalUserId 微信用户外部ID
 * @apiSuccess (响应结果) {Date} returnObject.userRelationList.addTime 添加好友时间
 * @apiSuccess (响应结果) {Date} returnObject.userRelationList.deleteTime 删除好友时间
 * @apiSuccess (响应结果) {String} returnObject.userRelationList.status 状态,1-新增，2-删除客户；3-被客户删除，
 * @apiSuccessExample 响应结果示例
 * {"returnObject":{"traceId":"lRSno","nickName":"LncvIsTjm","userRelationList":[{"addTime":3933564053252,"externalUserId":"4zCPZXR","deleteTime":2086474305017,"userId":"T8cO7mmKXO","status":"f6NkIKLb"}],"reqHost":"J","respHost":"zlJWnoPL2C"},"code":"XJ","description":"XEj"}
 */
}
