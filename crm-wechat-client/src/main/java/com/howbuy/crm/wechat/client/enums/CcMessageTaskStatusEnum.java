package com.howbuy.crm.wechat.client.enums;

/**
 * @description: 消息中心邮件发送状态， 1-发送中 2-发送成功 3-发送失败
 * @return
 * @author: jin.wang03
 * @date: 2024/10/30 10:51
 * @since JDK 1.8
 */
public enum CcMessageTaskStatusEnum {

    /**
     * 发送中
     */
    PUSHING("1", "发送中"),

    /**
     * 发送成功
     */
    PUSH_SUCCESS("2", "发送成功"),

    /**
     * 发送失败
     */
    PUSH_FAIL("3", "发送失败"),

    ;

    /**
     * 编码
     */
    private String code;


    /**
     * 描述
     */
    private String description;

    CcMessageTaskStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 通过code获得
     *
     * @param code 系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code) {
        CcMessageTaskStatusEnum statusEnum = getEnum(code);
        return statusEnum == null ? null : statusEnum.getDescription();
    }


    /**
     * 通过code直接返回 整个枚举类型
     *
     * @param code 系统返回参数编码
     * @return BaseConstantEnum
     */
    public static CcMessageTaskStatusEnum getEnum(String code) {
        for (CcMessageTaskStatusEnum statusEnum : CcMessageTaskStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
