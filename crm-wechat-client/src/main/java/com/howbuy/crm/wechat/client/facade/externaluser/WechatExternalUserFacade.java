/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.facade.externaluser;

import com.howbuy.crm.wechat.client.domain.request.externaluser.GetExternalUserRequest;
import com.howbuy.crm.wechat.client.domain.response.externaluser.GetExternalUserVO;
import com.howbuy.crm.wechat.client.base.Response;

/**
 * <AUTHOR>
 * @description: 企微外部联系人Dubbo接口
 * @date 2025-08-19 19:27:20
 * @since JDK 1.8
 */
public interface WechatExternalUserFacade {

    /**
     * @api {DUBBO} com.howbuy.crm.wechat.client.facade.externaluser.WechatExternalUserFacade.getExternalUser(request) getExternalUser()
     * @apiVersion 1.0.0
     * @apiGroup WechatExternalUserFacade
     * @apiName getExternalUser()
     * @apiDescription 获取企微外部联系人详情信息
     * @apiParam (请求参数) {String} externalUserId 外部联系人ID
     * @apiParam (请求参数) {String} companyNo 企业编码 1-好买财富，2-好买基金，3-好晓买
     * @apiParamExample 请求参数示例
     * externalUserId=J2c&companyNo=A
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 C0510002-参数错误 C0510003-系统错误 C0510004-未查询到数据
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} returnObject 返回内容
     * @apiSuccess (响应结果) {Array} returnObject.externalUserProfileList 外部联系人的自定义展示信息
     * @apiSuccess (响应结果) {String} returnObject.externalUserProfileList.type profile自定义类型
     * @apiSuccess (响应结果) {String} returnObject.externalUserProfileList.name profile自定义名称
     * @apiSuccess (响应结果) {Object} returnObject.externalUserProfileList.profileMap profile的详细属性
     * @apiSuccess (响应结果) {Array} returnObject.followUserList 外部客户与企业微信成员绑定关系列表
     * @apiSuccess (响应结果) {String} returnObject.followUserList.userid 添加了此外部联系人的企业成员userid
     * @apiSuccess (响应结果) {String} returnObject.followUserList.externalUserId 外部联系人的userid
     * @apiSuccess (响应结果) {String} returnObject.followUserList.remark 企业成员对外部联系人备注
     * @apiSuccess (响应结果) {String} returnObject.followUserList.description 企业成员对外部联系人描述
     * @apiSuccess (响应结果) {Number} returnObject.followUserList.createtime 企业成员添加外部联系人时间
     * @apiSuccess (响应结果) {String} returnObject.followUserList.remarkCorpName 企业成员对外部联系人备注的企业名称
     * @apiSuccessExample 响应结果示例
     * {"returnObject":{"externalUserProfileList":[{"profileMap":{},"name":"owXZu4OP","type":"l66xlaULw"}],"followUserList":[{"createtime":3781468778185,"externalUserId":"IdYrQtj4","description":"e4","remark":"MDHi4d","remarkCorpName":"zHnPdR0L19","userid":"FEXKNxdT"}]},"code":"0000","description":"操作成功"}
     */
    Response<GetExternalUserVO> getExternalUser(GetExternalUserRequest request);
}