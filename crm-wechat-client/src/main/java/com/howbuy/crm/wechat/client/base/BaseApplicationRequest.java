/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.base;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.crm.wechat.client.producer.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @description: (基于[wechatAppEnumK<PERSON>-微信应用枚举key]的 base请求类)
 * <AUTHOR>
 * @date 2025/8/8 10:00
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseApplicationRequest extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 微信应用枚举key
     * {@link com.howbuy.crm.wechat.client.enums.WechatApplicationEnum}
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "微信应用枚举key", isRequired = true)
    private String wechatAppEnumKey;

}
