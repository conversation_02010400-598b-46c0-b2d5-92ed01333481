package com.howbuy.crm.wechat.client.facade.wechatrelation;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.wechatrelation.QueryWechatRelationBatchRequest;
import com.howbuy.crm.wechat.client.domain.request.wechatrelation.QueryWechatRelationRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatrelation.QueryWechatRelationResponse;
import com.howbuy.crm.wechat.client.domain.response.wechatrelation.QueryWechatRelationBatchResponse;

/**
 * @description: 企业微信关系查询接口
 * <AUTHOR>
 * @date 2024/5/23 11:12
 * @since JDK 1.8
 */
public interface WechatRelationFacade {


    /**
     * @api {DUBBO}  com.howbuy.crm.wechat.client.facade.wechatrelation.WechatRelationFacade.queryWechatRelation(request) queryWechatRelation()
     * @apiVersion 1.0.0
     * @apiGroup WechatRelationFacade
     * @apiName queryWechatRelation()
     * @apiDescription 根据一账通号和投顾号查询企业微信关系
     * @apiParam (请求参数) {String} hboneNo 一账通号
     * @apiParam (请求参数) {String} conscode 投顾号
     * @apiParamExample 请求参数示例
     * hboneNo=123456789&conscode=ABC001
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 C0510002-参数错误 C0510003-系统错误 C0510004-未查询到数据
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} returnObject 返回内容
     * @apiSuccess (响应结果) {String} returnObject.gnUnionId 国内外部用户ID
     * @apiSuccess (响应结果) {String} returnObject.hwUnionId 海外外部用户ID
     * @apiSuccessExample 响应结果示例
     * {"returnObject":{"gnUnionId":"gnUnion123","hwUnionId":"hwUnion456"},"code":"0000","description":"成功"}
     * @description: 根据外部用户ID列表和投顾号查询企业微信关系
     * @param request 请求参数
     * @return Response<QueryWechatRelationByExternalIdsResponse>
     * <AUTHOR>
     * @date 2024/5/23 11:12
     * @since JDK 1.8
     */
    Response<QueryWechatRelationResponse> queryWechatRelation(QueryWechatRelationRequest request);



    /**
     * @api {DUBBO}  com.howbuy.crm.wechat.client.facade.wechatrelation.WechatRelationFacade.queryWechatRelationBatch(request) queryWechatRelationBatch()
     * @apiVersion 1.0.0
     * @apiGroup WechatRelationFacade
     * @apiName queryWechatRelationBatch()
     * @apiDescription 批量查询一账通号和投顾号的企业微信关系
     * @apiParam (请求参数) {Array} hbOneAndConsCodeDtoList 批量查询参数列表
     * @apiParam (请求参数) {String} hbOneAndConsCodeDtoList.hbOneNo 一账通号
     * @apiParam (请求参数) {String} hbOneAndConsCodeDtoList.consCode 投顾号
     * @apiParamExample 请求参数示例
     * hbOneAndConsCodeDtoList=[{"hbOneNo":"123456789","consCode":"ABC001"},{"hbOneNo":"987654321","consCode":"DEF002"}]
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 C0510002-参数错误 C0510003-系统错误 C0510004-未查询到数据
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} returnObject 返回内容
     * @apiSuccess (响应结果) {Array} returnObject.relationInfoList 关系信息列表
     * @apiSuccess (响应结果) {String} returnObject.relationInfoList.hbOneNo 一账通号
     * @apiSuccess (响应结果) {String} returnObject.relationInfoList.consCode 投顾号
     * @apiSuccess (响应结果) {String} returnObject.relationInfoList.hkUnionId 香港微信unionId
     * @apiSuccess (响应结果) {String} returnObject.relationInfoList.gnUnionId 国内微信unionId
     * @apiSuccessExample 响应结果示例
     * {"returnObject":{"relationInfoList":[{"hbOneNo":"123456789","consCode":"ABC001","hkUnionId":"hkUnion123","gnUnionId":"gnUnion456"}]},"code":"0000","description":"成功"}
     */
    Response<QueryWechatRelationBatchResponse> queryWechatRelationBatch(QueryWechatRelationBatchRequest request);
}
