package com.howbuy.crm.wechat.client.producer.vo;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class UserGroupVO implements Serializable {

    /**
     * 明细列表
     */
    private List<UserGroupDetailVO> userGroupDetailVOList;

    /**
     * 总条数
     */
    private Integer totalNum;

    /**
     * 总页数
     */
    private Integer totalPage;

   @Data
    public static class UserGroupDetailVO implements Serializable {

        /**
         * 一账通号
         */
        private String hbOneNo;

        /**
         * 微信昵称
         */
        private String wechatNickName;

        /**
         * 客户当前所在群id列表
         */
        private List<String> groupIdList = new ArrayList<>();

        /**
         * 历史所在群id列表
         */
        private List<String> hisGroupIdList = new ArrayList<>();

        /**
         * 添加助手时间 yyyyMMddHHmmss
         */
        private String addAssistantTime;
    }
}
