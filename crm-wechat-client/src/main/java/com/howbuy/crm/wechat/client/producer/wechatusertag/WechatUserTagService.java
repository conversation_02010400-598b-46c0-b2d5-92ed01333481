/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.producer.wechatusertag;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.producer.wechatusertag.request.AddUserTagRequest;
import com.howbuy.crm.wechat.client.producer.wechatusertag.request.DeleteUserTagRequest;

/**
 * @description: 企业微信用户标签服务接口
 * <AUTHOR>
 * @date 2024/6/12 17:50
 * @since JDK 1.8
 */
public interface WechatUserTagService {
    /**
     * @api {DUBBO} com.howbuy.crm.wechat.client.producer.wechatusertag.WechatUserTagService.add(request) add()
     * @apiVersion 1.0.0
     * @apiGroup WechatUserTagServiceImpl
     * @apiName 新增企业微信用户标签
     * @apiParam (请求参数) {String} hboneNo 一账通账号
     * @apiParam (请求参数) {Array} tagIdList 标签列表
     * @apiParamExample 请求参数示例
     * tagIdList=6nMnLUhE&hboneNo=2Wc0L
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 ********-参数错误 ********-系统错误 ********-未查询到数据
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {String} returnObject 返回内容
     * @apiSuccessExample 响应结果示例
     * {"returnObject":"nDuBZIy6XW","code":"7T0KuYPrw","description":"MqJFhqEs"}
     */
    Response<String> add(AddUserTagRequest request) ;

    /**
     * @api {DUBBO}  com.howbuy.crm.wechat.client.producer.wechatusertag.WechatUserTagService.delete(request) delete()
     * @apiVersion 1.0.0
     * @apiGroup WechatUserTagServiceImpl
     * @apiName 删除企业微信用户标签
     * @apiParam (请求参数) {String} hboneNo 一账通账号
     * @apiParam (请求参数) {Array} tagIdList 标签列表
     * @apiParamExample 请求参数示例
     * tagIdList=RSBDMTZ&hboneNo=P
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 ********-参数错误 ********-系统错误 ********-未查询到数据
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {String} returnObject 返回内容
     * @apiSuccessExample 响应结果示例
     * {"returnObject":"81H","code":"D","description":"p"}
     */
    Response<String> delete(DeleteUserTagRequest request) ;
}
