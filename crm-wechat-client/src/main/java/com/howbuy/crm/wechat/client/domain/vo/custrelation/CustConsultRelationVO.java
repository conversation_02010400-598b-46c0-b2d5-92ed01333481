/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.domain.vo.custrelation;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description: 客户投顾企业微信关联查询条件VO
 * <AUTHOR>
 * @date 2025-08-19 19:52:46
 * @since JDK 1.8
 */
@Getter
@Setter
@ToString
public class CustConsultRelationVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户信息-一账通号
     */
    private String hboneNo;

    /**
     * 投顾号
     */
    private String conscode;
}