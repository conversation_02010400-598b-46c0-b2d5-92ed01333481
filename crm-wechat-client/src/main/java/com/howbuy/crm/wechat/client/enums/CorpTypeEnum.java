package com.howbuy.crm.wechat.client.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 企业微信应用类型枚举
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public enum CorpTypeEnum {

    /**
     * 企业内部开发
     */
    INTERNAL("1", "企业内部"),

    /**
     * 第三方应用开发
     */
    THIRD_PARTY("2", "第三方应用"),

    /**
     * 服务商代开发
     */
    SERVICE_PROVIDER("3", "服务商代开发"),

    /**
     * 智慧硬件开发
     */
    SMART_HARDWARE("4", "智慧硬件开发");

    private final String code;
    private final String description;

    CorpTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static CorpTypeEnum fromCode(String code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
