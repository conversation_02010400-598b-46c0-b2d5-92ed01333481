/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.domain.response.group;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 客户群-群成员信息响应对象
 * @date 2025-08-19 19:05:03
 * @since JDK 1.8
 */
@Data
public class GroupChatMemberInfoVO implements Serializable {

    private static final long serialVersionUID = 1234567890123456792L;

    /**
     * 群成员id
     */
    private String userId;
    
    /**
     * 成员类型 1-企业成员 2-外部联系人
     */
    private String type;

    /**
     * 外部联系人在微信开放平台的唯一身份标识（微信unionid）
     */
    private String unionid;

    /**
     * 入群时间
     */
    private Date joinTime;

    /**
     * 入群方式。1-由群成员邀请入群（直接邀请入群）2-由群成员邀请入群（通过邀请链接入群）3-通过扫描群二维码入群
     */
    private String joinScene;

    /**
     * 邀请者的userid
     */
    private String invitorUserId;

    /**
     * 在群里的昵称
     */
    private String groupNickname;

    /**
     * 如果是微信用户，则返回其在微信中设置的名字；如果是企业微信联系人，则返回其设置对外展示的别名或实名
     */
    private String name;
}