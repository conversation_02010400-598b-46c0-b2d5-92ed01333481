/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.domain.request.externaluser;

import com.howbuy.crm.wechat.client.producer.BaseRequest;
import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: 获取企微外部联系人详情请求
 * <AUTHOR>
 * @date 2025-08-19 19:27:20
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetExternalUserRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;
    
    /**
     * 外部联系人ID
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "外部联系人ID", isRequired = true)
    private String externalUserId;

    /**
     * 企业编码 1-好买财富，2-好买基金，3-好晓买
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "企业编码", isRequired = true)
    private String companyNo;
}