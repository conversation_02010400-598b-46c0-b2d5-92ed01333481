/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.facade.wechatmaterial;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.wechatmaterial.WechatUploadMediaRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatmaterial.WechatUploadMediaVO;

/**
 * <AUTHOR>
 * @description: 微信素材管理接口
 * @date 2024/9/19 9:06
 * @since JDK 1.8
 */
public interface WechatMaterialFacade {

    /**
     * @api {dubbo} com.howbuy.crm.wechat.client.facade.wechatmaterial.WechatMaterialFacade.uploadMediaFile()
     * @apiGroup wechat
     * @apiName uploadMediaFile
     * @apiDescription 上传企微文件
     * @apiParam (请求体) {Array} wechatAppEnum 微信应用枚举
     * @apiParam (请求体) {Array} bytes 文件字节数组
     * @apiParam (请求体) {String} type 文件类型（图片-image 语音-voice 视频-video 普通文件-file）
     * @apiParam (请求体) {String} fileName 文件名称
     * @apiParamExample 请求体示例
     * {"requestId":"nySdxY0JDC","type":"7","fileName":"l"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据对象
     * @apiSuccess (响应结果) {String} data.type （图片-image 语音-voice 视频-video 普通文件-file）
     * @apiSuccess (响应结果) {String} data.mediaId 媒体文件上传后获取的唯一标识，3天内有效
     * @apiSuccess (响应结果) {String} data.createdAt 媒体文件上传时间戳
     * @apiSuccessExample 响应结果示例
     * {"code":"CmN7EIVgUm","data":{"type":"Pv","mediaId":"sLe","createdAt":"1Uc"},"description":"X4KmnnZDT2"}
     */
    Response<WechatUploadMediaVO> uploadMediaFile(WechatUploadMediaRequest request);

}
