package com.howbuy.crm.wechat.client.domain.request.wechatrelation;

import com.howbuy.crm.wechat.client.base.BaseCompanyNoRequest;
import com.howbuy.crm.wechat.client.producer.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Description:校验HboneNo和ConsCode是否企微绑定批量接口入参
 * @Author: yun.lu
 * Date: 2025/5/29 17:02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryWechatRelationBatchRequest extends BaseCompanyNoRequest {
    private static final long serialVersionUID = 4665383525627243789L;
    /**
     * 批量入参
     */
    private List<HbOneAndConsCodeDto> hbOneAndConsCodeDtoList;


}
