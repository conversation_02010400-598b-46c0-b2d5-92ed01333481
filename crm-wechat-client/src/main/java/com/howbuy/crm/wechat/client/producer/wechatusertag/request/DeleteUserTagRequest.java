/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.producer.wechatusertag.request;

import com.howbuy.crm.wechat.client.base.BaseCompanyNoRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 删除企业用户标签请求类
 * <AUTHOR>
 * @date 2024/6/12 18:42
 * @since JDK 1.8
 */
@Setter
@Getter
public class DeleteUserTagRequest  extends BaseCompanyNoRequest implements Serializable {

    private static final long serialVersionUID = -2535913315484235584L;

    /**
     * 一账通账号
     */
    private String hboneNo;
    /**
     * 标签列表
     */
    private List<String> tagIdList;
}