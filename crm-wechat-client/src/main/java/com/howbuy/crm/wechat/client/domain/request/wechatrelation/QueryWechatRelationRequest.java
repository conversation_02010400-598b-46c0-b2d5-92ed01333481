package com.howbuy.crm.wechat.client.domain.request.wechatrelation;

import com.howbuy.crm.wechat.client.base.BaseCompanyNoRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 根据外部用户ID列表和投顾号查询企业微信关系请求参数
 * <AUTHOR>
 * @date 2024/5/23 11:12
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryWechatRelationRequest extends BaseCompanyNoRequest {
    private static final long serialVersionUID = 1L;

    /**
     * 一账通号
     */
    private String hboneNo;
    
    /**
     * 投顾号
     */
    private String conscode;

} 