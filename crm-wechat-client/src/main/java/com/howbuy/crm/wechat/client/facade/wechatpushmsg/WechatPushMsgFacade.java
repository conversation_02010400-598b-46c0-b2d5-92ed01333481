package com.howbuy.crm.wechat.client.facade.wechatpushmsg;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.wechatpushmsg.SendGroupMessageRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatpushmsg.WechatPushMsgVO;

/**
 * <AUTHOR>
 * @description: 微信推送消息服务
 * @date 2024/9/6
 * @since JDK 1.8
 */
public interface WechatPushMsgFacade {

    /**
     * @api {DUBBO} com.howbuy.crm.wechat.client.facade.wechatpushmsg.WechatPushMsgFacade.sendGroupMessage(request) sendGroupMessage()
     * @apiVersion 1.0.0
     * @apiGroup WechatPushMsgFacade
     * @apiName sendGroupMessage()
     * @apiDescription 群发推送消息到指定客户
     * @apiParam (请求参数) {String} messageType 消息类型
     * @apiParam (请求参数) {String} title 消息标题
     * @apiParam (请求参数) {String} wechatConsCode 企业微信投顾号
     * @apiParam (请求参数) {String} url 跳转链接
     * @apiParam (请求参数) {String} messageContent 消息内容
     * @apiParamExample 请求参数示例
     * {
     *   "messageType": "1",
     *   "title": "群发标题",
     *   "wechatConsCode": "T123",
     *   "url": "http://example.com",
     *   "custNos": ["C001", "C002"],
     *   "messageContent": "推送内容"
     * }
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 其他-失败
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccessExample 响应结果示例
     * {
     *   "code": "0000",
     *   "description": "发送成功"
     * }
     */
    Response<WechatPushMsgVO> sendGroupMessage(SendGroupMessageRequest request);

} 