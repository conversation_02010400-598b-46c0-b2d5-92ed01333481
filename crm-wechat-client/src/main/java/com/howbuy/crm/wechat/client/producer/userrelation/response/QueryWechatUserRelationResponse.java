/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.producer.userrelation.response;

import com.howbuy.crm.wechat.client.producer.BaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @description:  根据一账通号查询客户与企业微信用户关系响应参数
 * <AUTHOR>
 * @date 2024/8/30 17:51
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryWechatUserRelationResponse extends BaseResponse {
    private static final long serialVersionUID = 6371487289636448758L;
    /**
     * 微信用户昵称
     */
    private String nickName;

    /**
     * 企业微信用户好友关系
     */
    private List<UserRelationVO> userRelationList;

    /**
     * @description: 企业微信用户关系VO
     * @author: hongdong.xie
     * @date: 2024/8/30 17:53
     * @since JDK 1.8
     */
    @Setter
    @Getter
    public static class UserRelationVO implements Serializable {
        private static final long serialVersionUID = 6371487289636448758L;
        /**
         * 企业微信用户ID
         */
        private String userId;
        /**
         * 微信用户外部ID
         */
        private String externalUserId;

        /**
         * 添加好友时间
         */
        private Date addTime;
        /**
         * 删除好友时间
         */
        private Date deleteTime;
        /**
         * 状态,1-新增，2-删除客户；3-被客户删除，
         */
        private String status;

        /**
         * 首次添加时间
         */
        private Date firstAddTime;

    }
}