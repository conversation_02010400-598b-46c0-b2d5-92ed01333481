package com.howbuy.crm.wechat.client.producer.wechatmembermanagement;

import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.producer.wechatmembermanagement.request.QueryWeChatMemberInfoRequest;
import com.howbuy.crm.wechat.client.producer.wechatmembermanagement.response.QueryWeChatMemberInfoResponse;

/**
 * @description: 获取企业微信成员信息接口
 * @author: jinqing.rao
 * @date: 2024/6/20 13:17
 * @since JDK 1.8
 */
public interface QueryWeChatMemberInfoService {

    /**
     * @api {DUBBO}  com.howbuy.crm.wechat.client.producer.wechatmembermanagement.QueryWeChatMemberInfoService.queryWeChatMemberInfo(request) queryWeChatMemberInfo()
     * @apiVersion 1.0.0
     * @apiGroup QueryWeChatMemberInfoService
     * @apiName queryWeChatMemberInfo()
     * @apiDescription 获取企业微信成员信息接口
     * @apiParam (请求参数) {String} userId 企业微信成员id
     * @apiParamExample 请求参数示例
     * userId=m2mj
     * @apiSuccess (响应结果) {String} code 返回码 0000-成功 C0510002-参数错误 C0510003-系统错误 C0510004-未查询到数据
     * @apiSuccess (响应结果) {String} description 返回描述
     * @apiSuccess (响应结果) {Object} returnObject 返回内容
     * @apiSuccess (响应结果) {String} returnObject.userId 用户id
     * @apiSuccess (响应结果) {String} returnObject.name 用户姓名
     * @apiSuccess (响应结果) {String} returnObject.email 用户邮箱
     * @apiSuccess (响应结果) {String} returnObject.qrCode 员工个人二维码，扫描可添加为外部联系人
     * @apiSuccess (响应结果) {String} returnObject.traceId 跟踪ID
     * @apiSuccess (响应结果) {String} returnObject.reqHost 请求方host
     * @apiSuccess (响应结果) {String} returnObject.respHost 响应方host
     * @apiSuccessExample 响应结果示例
     * {"returnObject":{"traceId":"a","qrCode":"2","name":"amx","reqHost":"d","userId":"oAB3cm","respHost":"LXTluS","email":"TP23qn"},"code":"MtSMcP1nHi","description":"cRsfTs"}
     */
    Response<QueryWeChatMemberInfoResponse> queryWeChatMemberInfo(QueryWeChatMemberInfoRequest request);
}
