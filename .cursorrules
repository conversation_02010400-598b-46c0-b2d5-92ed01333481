# 项目背景
你是一名资深Java开发和架构师，技术栈是spring boot、spring cloud、maven、junit

# 项目配置
  编程语言: Java
  JDK版本: 1.8
  框架: Spring Boot, Spring Cloud
  数据库: mysql
  缓存: Redis
  数据库操作: mybatis
  数据库连接池: druid
  目标:
    - 提升微服务稳定性
    - 优化接口性能
    - 实现需求场景的高可用性
    - 提升代码质量
    - 提升代码可读性
    - 提升代码可维护性
    - 提升代码可测试性
    - 新业务代码开发
    - 代码重构
    - 代码优化

# 代码风格
  命名约定:
    - 变量: camelCase  # 变量名使用小驼峰
    - 方法: camelCase  # 方法名使用小驼峰
    - 类: PascalCase    # 类名使用大驼峰
    - 常量: UPPERCASE_WITH_UNDERSCORES  # 常量使用全大写+下划线分隔
  缩进: 4_spaces  # 使用 4 个空格进行缩进
  每行最大长度: 120  # 每行代码不得超过 120 个字符
  - 代码规范
    - 使用阿里巴巴代码规范
    - 使用lombok注解
    - 使用dubbo注解
    - 使用guava工具包

  - 导包顺序
    - 静态导入优先: true
    - 包顺序:
        - java
        - javax
        - org
        - com
  - 注释要求
    - Controller 层、Service 层、repository层和关键业务逻辑必须添加 Javadoc 注释
    - @RequestMapping/@GetMapping 等注解需说明 API 的用途及注意事项
    - 类和方法注释需说明其功能和使用场景
    - 注释需清晰、简洁、准确，避免冗余和模糊
    - 注释需使用中文
    - Dubbo接口注释需说明其功能和使用场景
# 项目结构
    - 父项目crm-wechat下面有4个子项目
        - crm-wechat-client 包含dubbo接口定义和出入参定义
            - 基础包名: com.howbuy.crm.wechat.client
            - 接口定义: com.howbuy.dtms.order.client.facade
            - 接口定义: com.howbuy.crm.wechat.client.facade
            - 枚举类: com.howbuy.crm.wechat.client.enums
            - 接口入参: com.howbuy.crm.wechat.client.domain.request
            - 接口出参: com.howbuy.crm.wechat.client.base.Response
            - 接口入参继承: com.howbuy.crm.wechat.client.producer.BaseRequest
            - 接口入参命名规范: 接口名+Request
            - 接口出参命名规范: 接口名+Response
            - 枚举类命名规范: 枚举名+Enum
            - 接口出入参自定义对象实体命名规范: 接口名+VO
        - crm-wechat-remote 包含公共内容，例如启动类，切面，异常公共处理，配置，日志
            - 基础包名: com.howbuy.crm.wechat.remote
            - 启动类: com.howbuy.crm.wechat.remote.CrmWechatApplication
            - 配置文件目录: src/main/resources/
        - crm-wechat-dao 包含数据库操作,mybatis配置
            - 基础包名: com.howbuy.crm.wechat.dao
            - 数据库操作: com.howbuy.crm.wechat.dao.mapper
            - 数据库Mapper: com.howbuy.crm.wechat.dao.mapper
            - 数据库实体: com.howbuy.crm.wechat.dao.po
            - 关联查询非数据库实体对象: com.howbuy.crm.wechat.dao.bo
        - crm-wechat-service 包含业务逻辑
            - 基础包名: com.howbuy.crm.wechat.service
            - 业务处理切面: com.howbuy.crm.wechat.service.aspect
            - 公共业务实现: com.howbuy.crm.wechat.service.business
            - 缓存实现: com.howbuy.crm.wechat.service.cacheservice
            - 自定义注解: com.howbuy.crm.wechat.service.common.annotation
            - 常量类包: com.howbuy.crm.wechat.service.commom.constant
            - 常量类: com.howbuy.crm.wechat.service.common.constant.Constant
            - 枚举类包: com.howbuy.crm.wechat.service.common.enums
            - 配置类包: com.howbuy.crm.wechat.service.config
            - http接口: com.howbuy.crm.wechat.service.controller
            - DubboFilter: com.howbuy.crm.wechat.service.filter
            - 定时任务Job: com.howbuy.crm.wechat.service.job
            - mq消息: com.howbuy.crm.wechat.service.mq
            - 外部接口调用: com.howbuy.crm.wechat.service.outservice
            - dubbo接口: com.howbuy.crm.wechat.service.provider.dubbo
            - 事务管理: com.howbuy.crm.wechat.service.repository
            - 业务实现: com.howbuy.crm.wechat.service.service
            - 包分层规则：
                - controller>service>repository: controller层调用service层,service层调用repository层
                - dubbo>service>repository: dubbo接口层调用service层,service层调用repository层
                - business>repository: 业务层调用repository层
                - service>validator: service层调用validator层
                - service>cacheservice: service层调用缓存层
                - service>outservice: service层调用外部接口
                - job>service: 定时任务层调用service层
                - job>business: 定时任务层调用业务层
                - mq>service: mq消息层调用service层
                - mq>business: mq消息层调用业务层

# 架构最佳实践:
  - 微服务设计
    - 每个微服务应严格遵循单一职责原则
    - 使用 Dubbo3 作为服务间通信工具
    - 接口调用需实现超时、不重试机制
    - API 返回统一封装格式，例如: Response<T>（需包含状态码、消息、数据）
  - 配置管理
    - 配置优先使用 nacos

# 最佳实践
  并发编程:
    - 优先使用 java.util.concurrent 包中的工具类
    - 避免直接操作线程,使用线程池(ThreadPoolTaskExecutor)
  错误处理:
    - 全局异常捕获：实现 @ControllerAdvice
    - 自定义错误代码和国际化消息
  单元测试:
    - 单元测试覆盖率 ≥99%
    - 使用 MockMVC 测试 Controller 层
    - 使用 powermock、mockito和junit测试 Service 层逻辑
  日志:
    - 使用 Log4j2
    - 建议使用链路追踪工具(如 Zipkin 或 Sleuth)

# 数据库设计
  数据库初始化脚本:
    - init.sql

