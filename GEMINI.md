
# GEMINI.md

## 项目概述

`crm-wechat` 是一个基于Java的后端应用程序，可与微信集成以提供CRM功能。它遵循使用Spring Boot，Spring Cloud和Dubbo的微服务架构。该项目分为几个模块，包括用于数据访问的“ crm-wechat-dao”，用于业务逻辑的“ crm-wechat-service”和用于远程通信的“ crm-wechat-remote”。

该应用程序使用MyBatis与数据库进行交互，并利用Nacos进行服务发现和配置管理。它还与其他各种服务集成，例如“ crm-account-client”，“ crm-nt-client”和“ crm-core-client”。

## 构建和运行

### 依赖

- JDK 1.8
- Maven

### 构建

要构建项目，请从根目录运行以下命令：

```bash
mvn clean install
```

### 运行

该应用程序可以使用`start.sh`脚本启动：

```bash
./start.sh
```

或者，您可以直接运行`CrmWechatApplication` Spring Boot应用程序。

## 开发约定

### 事务管理

- 事务是使用`@Transactional`注释处理的。
- 对于只读操作，请在类级别使用`@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)`。
- 对于写操作（创建，更新，删除），请在方法级别使用`@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)`。

### 外部服务

- 对外部服务的所有调用都应放在`outerservice`包中。

### 日志记录

- 该项目使用Log4j 2进行日志记录。
- 在`tms-common-log-pattern`依赖项的帮助下，支持敏感信息的日志脱敏。

### 代码风格

- 使用Lombok减少样板代码。
- 遵循`pom.xml`文件中定义的依赖项版本。
- 在添加新的Maven依赖项时，请检查是否存在冲突并加以解决。
