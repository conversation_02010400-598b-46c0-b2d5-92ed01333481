<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info" monitorInterval="30">
    <properties>
        <property name="sqlLevel">info</property>
    </properties>
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS}|${ctx:uuid}|%t|%-5p|%c{1}:%L|%msg%n" />
        </Console>
    </Appenders>

    <Loggers>
        <AsyncLogger name="com.howbuy" level="${sqlLevel}" additivity="false">
            <appender-ref ref="Console" />
        </AsyncLogger>
        <AsyncLogger name="mainlog" level="${sqlLevel}" additivity="false">
            <appender-ref ref="Console"/>
        </AsyncLogger>
        <AsyncLogger name="com.alibaba.nacos" level="error" additivity="false">
            <AppenderRef ref="Console" />
        </AsyncLogger>
        <AsyncLogger name="COST_TIME_COLLECT_LOGGER" level="off" additivity="false">
            <AppenderRef ref="Console" />
        </AsyncLogger>
        <AsyncLogger name="com.alibaba.nacos" level="error" additivity="false">
            <appender-ref ref="Console" />
        </AsyncLogger>
        <root level="${sqlLevel}">
            <appender-ref ref="Console" />
        </root>
    </Loggers>
</Configuration>