<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info" monitorInterval="30">

	<properties>
		<property name="logPath">/data/logs/crm-wechat-remote</property>
		<property name="rollingLogName">crm-wechat-remote</property>
		<property name="mainLogName">crm-wechat-main</property>
		<property name="sqlLevel">info</property>
		<property name="cacheLogName">cache-state-collect</property>
		<property name="slowSqlLog">slow-sql</property>
	</properties>

	<Appenders>
		<Console name="Console" target="SYSTEM_OUT">
			<CustomPatternLayout pattern='{"labelName":"%markerSimpleName","tid":"${ctx:uuid}","time":"%d{yyyy-MM-dd HH:mm:ss.SSS}","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%enc{%msg}{JSON}"}%n'>
				<replaces>
					<replace regex='(\\"idNo\\":\\")(\d{6})\d{1,8}(\d{4})' replacement="$1$2********$3" />
					<replace regex='(\\"bytes\\":\\")(\d{6})\d{1,6}(\d{4})' replacement="$1$2********$3" />
					<replace regex='(\\"fileBytes\\":\\")[^\\"]*' replacement="$1***" />
					<replace regex='(\\"bytes\\":\\")[^\\"]*' replacement="$1***" />
				</replaces>
			</CustomPatternLayout>
		</Console>

		<RollingFile name="RollingFile" filename="${logPath}/${rollingLogName}.log" filepattern="${logPath}/%d{yyyyMMdd}/${rollingLogName}-%i.log">
			<CustomPatternLayout pattern='{"labelName":"%markerSimpleName","tid":"${ctx:uuid}","time":"%d{yyyy-MM-dd HH:mm:ss.SSS}","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%enc{%msg}{JSON}"}%n'>
				<replaces>
					<replace regex='(\\"idNo\\":\\")(\d{6})\d{1,8}(\d{4})' replacement="$1$2********$3" />
					<replace regex='(\\"bytes\\":\\")(\d{6})\d{1,6}(\d{4})' replacement="$1$2********$3" />
					<replace regex='(\\"fileBytes\\":\\")[^\\"]*' replacement="$1***" />
					<replace regex='(\\"bytes\\":\\")[^\\"]*' replacement="$1***" />
				</replaces>
			</CustomPatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
				<SizeBasedTriggeringPolicy size="1024 MB" />
			</Policies>
			<DefaultRolloverStrategy max="100" />
		</RollingFile>
		
		<RollingFile name="MainLogger" filename="${logPath}/${mainLogName}.log"
			filepattern="${logPath}/%d{yyyyMMdd}/${mainLogName}.log">
			<PatternLayout pattern="%msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
			</Policies>
			<DefaultRolloverStrategy max="30" />
		</RollingFile>
		
		<RollingFile name="CacheStatCollectLogger" filename="${logPath}/${cacheLogName}.log"
			filepattern="${logPath}/%d{yyyyMMdd}/${cacheLogName}.log">
			<PatternLayout pattern="%msg%n" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
			</Policies>
			<DefaultRolloverStrategy max="30" />
		</RollingFile>

		<RollingFile name="SlowSqlLog" filename="${logPath}/${slowSqlLog}.log"
					 filepattern="${logPath}/%d{yyyyMMdd}/${slowSqlLog}.log">
			<PatternLayout pattern='{"tid":"${ctx:uuid}","time":"%d{yyyy-MM-dd HH:mm:ss.SSS}","thread":"%t", "msg":"%enc{%msg}{JSON}"}%n' />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
			</Policies>
			<DefaultRolloverStrategy max="30" />
		</RollingFile>

		<Routing name="txCodeRouting">
			<Routes pattern="$${ctx:txCode}">
				<Route>
					<RollingFile name="txCode" append="true"
								 fileName="${logPath}/business/${ctx:txCode}.log" filePattern="${logPath}/%d{yyyyMMdd}/business/${ctx:txCode}.log">
						<PatternLayout
								pattern="%d{yyyy-MM-dd HH:mm:ss.SSS}|$${ctx:uuid}|%t|%-5p|%c{1}:%L|%msg%n" />
						<Policies>
							<TimeBasedTriggeringPolicy />
							<SizeBasedTriggeringPolicy size="1024 MB" />
						</Policies>
					</RollingFile>
				</Route>
			</Routes>
		</Routing>

	</Appenders>

	<Loggers>
		<AsyncLogger name="com.howbuy" level="info" additivity="false">
			<appender-ref ref="RollingFile" />
		</AsyncLogger>
		<AsyncLogger name="mainlog" level="info" additivity="false">
             <appender-ref ref="MainLogger"/>
        </AsyncLogger>
		<AsyncLogger name="txServiceLog" level="info" additivity="false">
			<appender-ref ref="txCodeRouting"/>
		</AsyncLogger>
		<AsyncLogger name="com.howbuy.crm.wechat.dao.mapper" level="${sqlLevel}" additivity="false">
			<AppenderRef ref="RollingFile" />
		</AsyncLogger>
		<AsyncLogger name="COST_TIME_COLLECT_LOGGER" level="error" additivity="false">
			<AppenderRef ref="CacheStatCollectLogger" />
		</AsyncLogger>
		<AsyncLogger name="com.alibaba.nacos" level="error" additivity="false">
			<appender-ref ref="RollingFile" />
		</AsyncLogger>
		<AsyncLogger name="slowSqlLog" level="info" additivity="false">
			<AppenderRef ref="SlowSqlLog"/>
		</AsyncLogger>
		<root level="info">
			<appender-ref ref="RollingFile" />
			<appender-ref ref="Console" />
		</root>
	</Loggers>
</Configuration>