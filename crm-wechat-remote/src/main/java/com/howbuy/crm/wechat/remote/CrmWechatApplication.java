/**
 * Copyright (c) 2022, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.remote;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ImportResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description: 直销产品中心启动类
 * @date 2023/5/14 8:46
 * @since JDK 1.8
 */
@RestController
@EnableDiscoveryClient
@MapperScan("com.howbuy.crm.wechat.dao.mapper")
@EnableDubbo(scanBasePackages = "com.howbuy.crm.wechat.service")
@SpringBootApplication(scanBasePackages = {"com.howbuy.crm.wechat"})
@ImportResource("classpath:dubbo.xml")
public class CrmWechatApplication {

    @Value("${spring.application.name}")
    private String applicationName;

    public static void main(String[] args) {
        //设置dubbo 日志为slf4j
        System.setProperty("dubbo.application.log", "slf4j");
        SpringApplication.run(CrmWechatApplication.class, args);
    }


    /**
     * @api {GET} /error
     * @apiName spring内部error接口
     */

    /**
     * @api {GET} /index hello()
     * @apiVersion 1.0.0
     * @apiGroup CrmWechatApplication
     * @apiName hello()
     * @apiSuccess (响应结果) {String} response
     * @apiSuccessExample 响应结果示例
     * "Y"
     */
    @RequestMapping( value = "/index", method = {RequestMethod.GET})
    public String hello() {
        return "hello, ".concat(this.applicationName);
    }

}
