<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>crm-wechat</artifactId>
        <groupId>com.howbuy.crm</groupId>
        <version>2.0.6.0-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <name>crm-wechat-remote</name>
    <artifactId>crm-wechat-remote</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-wechat-service</artifactId>
        </dependency>

        <!-- 增加健康检测的依赖 -->
        <dependency>
            <groupId>com.howbuy.boot</groupId>
            <artifactId>howbuy-boot-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mockito-all</artifactId>
                    <groupId>org.mockito</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-core</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>

                    <archive>
                        <manifestEntries>
                            <Package-Stamp>${parelease}</Package-Stamp>
                        </manifestEntries>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <!-- 使用唯一版本 镜像版本不带时间戳-->
                            <useUniqueVersions>false</useUniqueVersions>
                            <!--这样写的目的是因为启动的时候会把打好的包也放在lib下的,
                            所以指定依赖也在当前路径,可以看打好的包的META-INF.MANIFEST.MF文件-->
                            <classpathPrefix>.</classpathPrefix>
                            <mainClass>com.howbuy.crm.wechat.remote.CrmWechatApplication</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-lib</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/crm-wechat-remote/lib</outputDirectory>
                            <excludeTransitive>false</excludeTransitive>
                            <stripVersion>false</stripVersion>
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>howbuy-release</id>
            <name>howbuy-release</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshot</id>
            <name>howbuy-snapshot</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>
    
</project>