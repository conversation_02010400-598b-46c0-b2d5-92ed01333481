1.企业微信第三方应用回调接口
    - 企业微信回调第三方模式，见文档：https://developer.work.weixin.qq.com/document/path/92277
    - 由于消息解密后内容格式不同，要和现有的内部模式回调处理区分开
    - 消息解密默认使用默认companyNo获取对应的解密token和secret
    -

## 数据库表结构修改SQL

### 为cm_wechat_company表添加SUITE_ID字段

**执行时间**: 2025-08-20 09:58:50  
**作者**: hongdong.xie

```sql
-- 为cm_wechat_company表添加SUITE_ID字段
-- 字段类型和长度与corp_id字段相同：varchar(200)
ALTER TABLE cm_wechat_company 
ADD COLUMN SUITE_ID varchar(200) NULL COMMENT '第三方应用套件ID';
```

**说明**:
- 新增字段SUITE_ID，类型为varchar(200)，与corp_id字段保持一致
- 字段允许为空（NULL），便于现有数据的兼容性
- 添加注释说明该字段用于存储第三方应用套件ID 